{"default": {"msg": "error.technical_error", "action": "alert"}, "httpCodes": {"0": {"default": {"msg": "i18n.error.timeout", "action": "alert", "type": "blocking"}}, "400": {"default": {"msg": "i18n.error.not_found_error2"}, "applicationCodes": {"F001": {"msg": "error.form.invalid", "action": "alert", "type": "warning"}, "E001": {"msg": "error.service_plan_model.version.exist_with_start_date", "action": "alert", "type": "blocking"}, "E002": {"msg": "error.service_plan.version.exist_with_start_date", "action": "alert", "type": "blocking"}, "E003": {"msg": "error.service_plan_model.update.forbidden", "action": "alert", "type": "blocking"}, "E004": {"msg": "error.service_plan.person.already_assigned", "action": "alert", "type": "blocking"}, "E005": {"msg": "error.volunteeravailability.delete.prestations_already_assigned", "action": "alert", "type": "blocking"}, "E006": {"msg": "error.volunteeravailability.update.prestations_already_assigned", "action": "alert", "type": "blocking"}, "E007": {"msg": "error.userrights.delete", "action": "alert", "type": "blocking"}, "E009": {"msg": "error.service_plan_model.version.notfound", "action": "alert", "type": "blocking"}, "E010": {"action": "ignore"}, "E011": {"action": "ignore"}, "E012": {"msg": "error.service_plan.version.startdateafterenddate", "action": "alert", "type": "blocking"}, "E013": {"msg": "error.entity.update", "action": "alert", "type": "blocking"}, "E014": {"msg": "error.service_plan_model.version.dates", "action": "alert", "type": "blocking"}, "E015": {"msg": "error.prestation.copy", "action": "alert", "type": "blocking"}, "E016": {"msg": "error.prestation.nothing-copy", "action": "alert", "type": "blocking"}, "E017": {"msg": "error.service_plan.vehicle-free", "action": "alert", "type": "blocking"}, "E034": {"msg": "error.service_plan.box-free", "action": "alert", "type": "blocking"}, "E018": {"msg": "error.prestation.exclusive", "action": "alert", "type": "blocking"}, "E019": {"msg": "error.person.no_assignments", "action": "alert", "type": "blocking"}, "E020": {"msg": "error.export.no_data_to_export", "action": "alert", "type": "blocking"}, "E021": {"msg": "error.export.no_export_type", "action": "alert", "type": "blocking"}, "E023": {"msg": "error.service_plan.timeslot.merge.default", "action": "alert", "type": "blocking"}, "E024": {"msg": "error.service_plan.timeslot.merge.service_plan_different", "action": "alert", "type": "blocking"}, "E025": {"msg": "error.service_plan.timeslot.merge.slot_already_started", "action": "alert", "type": "blocking"}, "E026": {"msg": "error.service_plan_model.version.copy", "action": "alert", "type": "blocking"}, "E027": {"msg": "error.service_plan.version.copy", "action": "alert", "type": "blocking"}, "E028": {"msg": "i18n.data.error.service_plan.export.duration", "action": "alert", "type": "blocking"}, "E029": {"msg": "error.service_plan.assignteam.empty", "action": "alert", "type": "warning"}, "E030": {"msg": "error.entity.export", "action": "alert", "type": "warning"}, "E031": {"msg": "error.person.export", "action": "alert", "type": "blocking"}, "E032": {"msg": "error.prestation.allowance.configuration.samestartdate", "action": "alert", "type": "blocking"}, "E033": {"msg": "error.prestation.allowance.configuration.pastorcurrent", "action": "alert", "type": "blocking"}, "E035": {"msg": "error.fotags.name.exists", "action": "alert", "type": "blocking"}, "E044": {"msg": "error.rici.ric.is_not_number", "action": "alert", "type": "blocking"}, "E045": {"msg": "error.rici.ric.is_divisible_by_8", "action": "alert", "type": "blocking"}, "E046": {"msg": "error.rici.ric.outside_of_entity_range", "action": "alert", "type": "blocking"}, "E047": {"msg": "error.rici.ric.no_available_ric_in_range", "action": "alert", "type": "blocking"}, "E048": {"msg": "error.rici.ric.no_active_range_for_entity", "action": "alert", "type": "blocking"}, "T999": {"msg": "error.person.export", "action": "alert", "type": "warning"}}}, "302": {"default": {"action": "fire", "url": "/login", "msg": "errorCode:302"}, "applicationCodes": {"A001": {"action": "redirect", "url": "/login", "msg": "errorCode:302"}}}, "401": {"default": {"action": "fire", "url": "/login", "msg": "errorCode:401"}, "applicationCodes": {"A001": {"action": "redirect", "url": "/login", "msg": "errorCode:401"}}}, "403": {"default": {"msg": "i18n.error.authorization_error", "action": "fire"}, "applicationCodes": {"A002": {"msg": "i18n.error.policy_authorization_error", "action": "fire"}, "A003": {"msg": "i18n.error.document_download_authorization_error", "action": "fire"}, "A004": {"msg": "i18n.error.case.closed"}, "A005": {"msg": "i18n.error.forbidden_action", "action": "fire"}, "A007": {"msg": "i18n.error.forbidden_data_access", "action": "fire"}, "E032": {"msg": "error.person.already_logas", "action": "alert", "type": "blocking"}}}, "404": {"default": {"msg": "i18n.error.not_found_error"}, "applicationCodes": {"A002": {"msg": "i18n.error.not_found_error"}}}, "409": {"default": {"msg": "i18n.error.concurrent_access"}, "applicationCodes": {"D002": {"msg": "i18n.error.concurrent_access"}, "E035": {"msg": "error.rici.simcard.alreadyexists.ICCID", "action": "alert", "type": "blocking"}, "E036": {"msg": "error.rici.simcard.alreadyexists.MSISDN", "action": "alert", "type": "blocking"}, "E037": {"msg": "error.rici.pager.alreadyexists.SERIAL_NUMBER", "action": "alert", "type": "blocking"}, "E038": {"msg": "error.rici.pager.alreadyexists.PAGER_ID", "action": "alert", "type": "blocking"}, "E039": {"msg": "error.rici.simcard.alreadyassociated", "action": "alert", "type": "blocking"}, "E040": {"msg": "error.rici.person.already_has_pager_assigned", "action": "alert", "type": "blocking"}, "E042": {"msg": "error.rici.ric.already_used", "action": "alert", "type": "blocking"}, "E043": {"msg": "error.rici.ric.already_used_by_alert_group", "action": "alert", "type": "blocking"}}}, "500": {"default": {"msg": "error.technical_error", "action": "alert", "type": "blocking"}, "applicationCodes": {"E022": {"msg": "error.export.default", "action": "alert", "type": "blocking"}, "E023": {"msg": "error.service_plan.timeslot.merge.default", "action": "alert", "type": "blocking"}, "E024": {"msg": "error.service_plan.timeslot.merge.service_plan_different", "action": "alert", "type": "blocking"}, "E025": {"msg": "error.service_plan.timeslot.merge.slot_already_started", "action": "alert", "type": "blocking"}}}, "520": {"default": {"msg": "error.person.already_logas", "action": "alert", "type": "blocking"}}, "504": {"default": {"msg": "error.badgateway"}}}}