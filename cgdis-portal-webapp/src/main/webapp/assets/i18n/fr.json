{"fetching-text": "Récupération des données...", "datatable": {"filters": {"true-false": {"true": "O<PERSON>", "false": "Non"}}}, "function-operationals": {"status": {"DRAFT": "Brouillon", "VALIDATED": "Valid<PERSON>", "CLOSED": "Clôturée"}}, "admin": {"allowance": {"back": "Retour", "configuration": {"actions": "Actions", "barracked_amount": "<PERSON><PERSON>", "barracked_amount_mobile": "<PERSON><PERSON><PERSON>", "configVersion": "", "creation": {"error": "Échec de la création de la configuration '{{name}}', Code d'erreur : {{code}}", "success": "La configuration d'indemnité a été créé avec succès", "title": "Création d'une nouvelle configuration"}, "delete": {"error": "Échec de la suppression de la configuration", "success": "La configuration d'indemnité a été supprimé avec succès"}, "endDate": "Date de fin", "label": "Libellé de version", "label_mobile": "Libellé", "new": {"fields": {"barrackedAmount": "<PERSON><PERSON>", "barrackedAmount_mobile": "<PERSON><PERSON><PERSON>", "endDate": "Date de fin", "label": "Libellé de version", "notBarrackedAmount": "Montant non caserné", "notBarrackedAmount_mobile": "Non caserné", "startDate": "Date de début", "version": "Version de calcul", "versionName": ""}, "title": "Nouvelle configuration"}, "not_barracked_amount": "Montant non caserné", "not_barracked_amount_mobile": "Non caserné", "popup": {"delete": {"message": "Souhaitez-vous vraiment supprimer cette configuration: <b>{{name}}</b> ?", "subtitle": "d'une configuration d'indemnités", "title": "Suppression"}}, "startDate": "Date de début", "title": "Configuration des indemnités", "update": {"success": "Indemnité modifiée avec succès", "title": "Editer configuration", "title_view": "Visualiser configuration"}, "version": "Version de calcul"}, "create": "<PERSON><PERSON><PERSON>", "list": {"allowanceAmount": "Montant de l'indemnité", "allowanceAmount_mobile": "<PERSON><PERSON>", "allowanceDate": "Date d'indemnité", "duration": "<PERSON><PERSON><PERSON> r<PERSON>"}, "search": {"detail": {"title": "Vue globale des indemnités pour {{firstName }} {{lastName}}"}, "title": "Vue globale des indemnités pour"}, "title": "Indemnités"}, "boxes": {"form": {"globalId": "Global Id", "isMaster": "Master box?", "name": "Nom"}, "list": {"header": {"entityName": "Entité", "globalId": "Global Id", "name": "Nom"}, "title": "Liste des boxes", "titlemobile": "Liste des boxes"}, "title": "Gestion des boxes", "view": {"title": "Detail de la box", "titlemobile": "Detail de la box"}}, "entities": {"form": {"armingPriority": "Priorité d'armement", "armingPriority_mobile": "Priorité", "category": "<PERSON><PERSON><PERSON><PERSON>", "entity": "Entité", "main": "Entité principale", "name": "Nom", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openParent": "Ouvrir entité supérieure", "parent": "Entité supérieure", "type": "Type", "update": {"success": "Entité modifiée avec succès"}}, "list": {"header": {"actions": "Actions", "armingPriority": "Priorité d'armement", "armingPriority_mobile": "Priorité", "category": "<PERSON><PERSON><PERSON><PERSON>", "mainEntity": "Entité principale", "name": "Nom", "parentEntity": "Entité supérieure", "servicePlanType": "Type de Plan de Service", "type": "Type d'entité"}, "title": "Liste des entités", "titlemobile": "Liste des entités"}, "title": "Administration des entités", "view": {"title": "Entité", "titlemobile": "Entité"}}, "export": {"creation": {"error": "Échec de la création du profil d'export '{{name}}', Code d'erreur : {{code}}", "success": "Le profil d'export '{{name}}' créé avec succès", "title": "Création d'un profil d'export"}, "delete": {"error": "Échec de suppression du profil d'export , Code d'erreur : {{code}}", "success": "Le profil d'export a été supprimé avec succès"}, "form": {"add-mail": "Ajouter un email", "cron": "Périodicité", "data-export": "Données à exporter", "email": "Email", "entity": "Entité", "export-format": {"csv": "CSV Format", "format": "Format d'export", "json": "JSON Format"}, "export-number-days": "Nombre de jour à exporter", "export-on-server": "Exporter sur le serveur", "export-persons": "Exporter les personnes", "export-service-plans": "Exporter les plans de service", "export-type": "Type d'export", "export-vehicles": "Exporter les véhicules", "name": "Nom", "remove-mail": "<PERSON><PERSON><PERSON><PERSON>"}, "list": {"header": {"actions": "Actions", "entity": "Entité", "last-execution": "Dernière exécution", "last-execution-mobile": "Dernière exé.", "name": "Nom", "next-execution": "Prochaine exécution", "next-execution-mobile": "Prochaine exé."}, "title": "Profils d'Export", "titlemobile": "Profils d'Export"}, "popup": {"delete": {"message": "Souhaitez-vous vraiment supprimer le profil d'export <b>{{name}}</b> ?", "subtitle": "d'un profil d'export", "title": "Suppression"}, "force-execution": {"message": "Souhaitez-vous vraiment force l'execution du profil d'export <b>{{name}}</b> ?", "subtitle": "d'un profil d'export", "title": "Exécution forcée"}}, "title": "Profils d'Export", "update": {"error": "Échec de la modification du profil d'export '{{name}}', Code d'erreur : {{code}}", "success": "Le profil d'export '{{name}}' a été modifé avec succès", "title": "Edition d'un profil d'export", "titlemobile": "Détail d'un profil d'export"}}, "fotags": {"title": "Tags", "form": {"fields": {"name": "Nom", "description": "Description"}, "new": {"success": "Le tag a été créé"}, "update": {"success": "Le tag a été modifié"}}, "list": {"columns": {"name": "Nom", "description": "Description", "actions": "Actions"}, "delete": {"confirmation": {"title": "Suppression de tag", "message": "Confirmez-vous la suppression du tag {{name}} ?"}}}}, "function_operational": {"close": {"error": "La fonction operationnelle n'a pas pu être clôturée car elle est utilisée dans les templates de positions suivant :"}, "creation": {"error": "Échec de la création de la fonction opérationnelle '{{label}}', Code d'erreur : {{code}}", "success": "La fonction opérationnelle '{{label}}' créée avec succès", "title": "Création d'une fonction opérationnelle"}, "delete": {"error": "Échec de suppression de la fonction opérationnelle , Code d'erreur : {{code}}", "success": "La fonction opérationnelle a été supprimée avec succès"}, "form": {"id": "Id", "intervention_type": "Type d'intervention", "label": "Nom", "portalLabel": "Nom affiché", "tags": "Tags", "tagnomoreavailable": " (supprimé)"}, "list": {"header": {"actions": "Actions", "entitiescount": "Nombre d'entités", "closed": "Clôturée", "function": "Fonction", "id": "ID", "intervention_type": "Type d'intervention", "interventiontype": "Type d'intervention", "label": "Nom", "portalLabel": "Nom"}, "modelName": "Nom du modèle", "nodata": "Aucune entité", "title": "Liste des Fonctions Opérationnelles", "titlemobile": "Liste des Fonctions Opérationnelles", "totalElements": "{{totalElements}} résultat(s)"}, "orders": {"title": "Gestion de tri des fonctions opérationnelles", "list": {"columns": {"label": "Fonction opérationnelle", "order": "Ordre", "actions": "Changer l'ordre"}}}, "popup": {"closure": {"message": "Souh<PERSON><PERSON>-vous vraiment clôturer la fonction opérationnelle <b>{{label}}</b> ?", "subtitle": "d'une fonction opérationnelle", "title": "Cl<PERSON><PERSON>"}, "delete": {"message": "Souhaitez-vous vraiment supprimer la fonction opérationnelle <b>{{label}}</b> ?", "subtitle": "d'une fonction opérationnelle", "title": "Suppression"}, "validate": {"message": "Souhaitez-vous vraiment valider et activer la fonction opérationnelle <b>{{label}}</b> ?", "subtitle": "d'une fonction opérationnelle", "title": "Validation"}}, "update": {"error": "Échec de la modification de la fonction opérationnelles '{{label}}', Code d'erreur : {{code}}", "success": "La fonction opérationnelle '{{label}}' a été modifée avec succès", "title": "Edition d'une Fonction Opérationnelle", "titlemobile": "Detail administratif de la fonction opérationnelle"}}, "general-message": {"creation": {"error": "Échec de la création du message général '{{name}}', Code d'erreur : {{code}}", "success": "Le message général '{{name}}' c<PERSON><PERSON> avec succès", "title": "Création d'un message général", "titlemobile": "Création d'un message général"}, "delete": {"error": "Échec de suppression du message général , Code d'erreur : {{code}}", "success": "Le message général a été supprimé avec succès"}, "form": {"endDateTime": "Date et heure de fin", "message": "Message", "mobileEndDateTime": "Date de fin", "mobileStartDateTime": "Date de début", "name": "Nom", "startDateTime": "Date et heure de début"}, "list": {"header": {"endDateTime": "Date de fin", "message": "Message", "mobileEndDateTime": "Fin", "mobileStartDateTime": "D<PERSON>but", "name": "Nom", "startDateTime": "Date de début"}, "title": "Messages généraux", "titlemobile": "Messages généraux"}, "title": "Messages généraux", "update": {"error": "Échec de la modification du message général '{{name}}', Code d'erreur : {{code}}", "success": "Le message général '{{name}}' a été modifé avec succès", "title": "Edition d'un message général", "titlemobile": "<PERSON><PERSON><PERSON> d'un message général"}}, "position_template": {"closure": {"error": "Le template de position n'a pas pu être clôturé car il est utilisé dans les modeles suivant :", "success": "Le template de position a été clôturé avec succès"}, "copy": {"error": "Échec de la copie du template de position", "new_name": "Co<PERSON> - {{label}}", "success": "Le template de position '{{label}}' a été copié avec succès", "title": "Copie d'un template de position existant"}, "creation": {"error": "Échec de le création du template de position '{{label}}', Code d'erreur : {{code}}", "success": "Le template de position '{{label}}' a été créé avec succès", "title": "Création d'un nouveau template de position"}, "delete": {"error": "Échec de la suppression du template de position", "success": "Le template de position a été supprimé avec succès"}, "form": {"buttons": {"addversion": "Ajouter une version"}, "id": "Identifiant", "intervention_type": "Type d'intervention", "label": "Nom", "portalLabel": "Nom affiché"}, "list": {"header": {"actions": "Actions", "closed": "Clôturé", "entity": "Entité", "id": "Identifiant", "intervention_type": "Type d'intervention", "label": "Nom", "portalLabel": "Nom"}, "title": "Liste des templates de position", "titlemobile": "Liste des templates de position"}, "popup": {"closure": {"message": "<PERSON><PERSON><PERSON><PERSON>-vous vraiment clôturer le template de position <b>{{label}}</b> ?", "subtitle": "d'un template de position", "title": "Cl<PERSON><PERSON>"}, "delete": {"message": "Souhaitez-vous vraiment supprimer le template de position: <b>{{label}}</b> ?", "subtitle": "d'un template de position", "title": "Suppression"}}, "title": "Administration des templates de position", "update": {"error": "Échec de la modification du template de position '{{label}}', Code d'erreur : {{code}}", "success": "Le modèle de plan de service '{{label}}' a été modifié avec succès", "title": "Edition d'un template de position", "titlemobile": "Detail d'un template de position"}, "version": {"closure": {"error": "Échec de la clôture du template de position", "success": "Le template de position a été clôturé avec succès"}, "copy": {"label": "Nouveau Label", "message": " Voulez-vous copier la version du template de position", "target": "Au", "title": "Copie de version de template de position"}, "create": {"success": "La version a été créée", "title": "Ajouter une version de template de position"}, "delete": {"success": "La version '{{label}}' a été supprimée"}, "edit": {"information_message": "Attention, toute validation pourra entrainer la suppression des futures prestations des plans de service liés au précédent modèle", "success": "La version a été mise à jour", "title": "Edition d'une version de template de position", "titlemobile": "Detail d'une version de template de position"}, "form": {"button": {"addversion": "Ajouter une version de template de position"}, "enddate": "Date de fin", "enddate_mobile": "Fin", "function": {"empty": "", "type": {"ideal": "", "partial": ""}}, "functions": {"empty": "Aucune fonction de ce type n'a été renseignée pour ce modèle", "type": {"ideal": "Fonctions idéales", "partial": "Fonctions reduites"}}, "label": "Nom", "startdate": "Date de début", "startdate_mobile": "D<PERSON>but", "version": "Version"}, "function": {"empty": "", "type": {"ideal": "", "partial": ""}}, "functions": {"empty": "Aucune fonction de ce type n'a été renseignée pour ce modèle", "type": {"ideal": "Fonctions idéales", "partial": "Fonctions reduites"}}, "list": {"header": {"actions": "Actions", "enddate": "Date de fin", "enddate_mobile": "Fin", "label": "Nom", "startdate": "Date de début", "startdate_mobile": "D<PERSON>but"}}, "popup": {"closure": {"message": "Veuillez indiquer la date de clôture du template de position <b>{{label}}</b> du {{startDate}} ?", "subtitle": "d'une version de template de position", "title": "Cl<PERSON><PERSON>"}, "delete": {"message": "<PERSON><PERSON><PERSON><PERSON>-vous la suppression de la version <b>{{label}}</b> du {{startDate}}", "subtitle": "d'une version de template de position", "title": "Suppression"}}}}, "public-holiday": {"creation": {"error": "Échec de la création du jour férié '{{name}}', Code d'erreur : {{code}}", "success": "Le jour férié '{{name}}' créé avec succès", "title": "Création d'un jour férié", "titlemobile": "Création d'un jour férié"}, "delete": {"error": "Échec de suppression du jour férié , Code d'erreur : {{code}}", "success": "Le jour férié a été supprimé avec succès", "title": "Suppression", "subtitle": "de jours fériés", "message": "So<PERSON><PERSON>ez-vous vraiment supprimer ce jour férié : {{name}}", "actions": "Actions"}, "form": {"date": "Date", "endDateTime": "", "message": "", "mobileEndDateTime": "", "mobileStartDateTime": "", "name": "Nom", "startDateTime": ""}, "list": {"header": {"date": "Date", "name": "Nom", "year": "<PERSON><PERSON>", "actions": "Actions"}, "title": "Jours fériés", "titlemobile": "Jours fériés"}, "title": "Jours fériés", "update": {"error": "Échec de la modification du jour férié '{{name}}', Code d'erreur : {{code}}", "success": "Le jour férié '{{name}}' a été modifé avec succès", "title": "Edition d'un jour férié", "titlemobile": "Detail d'un jour férié"}}, "service_plan": {"closure": {"error": "Échec de la clôture du plan de service, Code d'erreur : {{code}}", "success": "Le plan de service a été clôturé avec succès"}, "creation": {"error": "Échec de la création du plan de service '{{label}}', Code d'erreur : {{code}}", "success": "Le plan de service '{{label}}' cré<PERSON> avec succès", "title": "Création d'un plan de service"}, "delete": {"error": "Échec de suppression du plan de service , Code d'erreur : {{code}}", "success": "Le plan de service a été supprimé avec succès"}, "form": {"armingDelay": "<PERSON><PERSON><PERSON>", "armingPriority": "Priorité d'armement", "backupGroup": "Groupe de backup", "box": "Box", "elsBoxManagement": "Gestion des boxes ELS", "elsStatus": "Status ELS", "elsStatusManagement": "Gestion du statut ELS", "enabled": "Activé", "entity": "Entité", "error": {"novehicles": "Il n'y a pas de véhicule disponible pour l'entité {{entityName}}"}, "firstDay": "<PERSON>ur de dé<PERSON> de se<PERSON>", "id": "Id", "isusedasoptionalbackupgroup": "Le plan de service est optionnel ou backup des plans de services:{{servicePlans}}", "label": "Nom Plan de service dans ELS", "model": "<PERSON><PERSON><PERSON><PERSON>", "optionalGroup": "Groupe optionnel", "optionalGroupUnavailable": "(Indisponible)", "popup": {"warning": {"message": "La modification du champ 'exclusif' va entraîner la suppression de toutes les prestations futures des membres assignés à ce plan de service, sur l'ensemble des autres plans de service. Souhaitez-vous continuer ?", "title": "Confirmation"}}, "portalLabel": "Indicatif radio", "vehicle": "Véhicule"}, "list": {"entityName": "Nom de l'entité", "header": {"actions": "Actions", "armingDelay": "<PERSON><PERSON><PERSON>", "armingPriority": "Prio. armement", "closed": "Clôturé", "closed-no": "Non", "closed-yes": "O<PERSON>", "entity": "Entité", "id": "ID", "name": "Indicatif radio", "servicePlanType": "Type de plan de service", "servicePlanType_mobile": "Type", "vehicle": "Véhicule"}, "modelName": "Nom du modèle", "mypds": "Mes PdS", "mypdsmobile": "Mes Plans de Service", "title": "Liste des Plans de Service", "titlemobile": "Liste des Plans de Service", "totalElements": "{{totalElements}} résultat(s)"}, "popup": {"closure": {"message": "Veuillez indiquer la date de clôture du plan de service <b>{{name}}</b> ?", "subtitle": "d'un plan de service", "title": "Cl<PERSON><PERSON>"}, "delete": {"message": "Souhaitez-vous vraiment supprimer le plan de service <b>{{name}}</b> ?", "subtitle": "d'un plan de service", "title": "Suppression"}}, "teams": {"create": {"success": "L'équipe a été créée", "title": "Création d'une équipe"}, "delete": {"error": "Échec de la suppression de l'équipe", "success": "L'équipe a été supprimée avec succès"}, "form": {"addperson": "+Ajouter une personne", "button": {"addteam": "Ajouter une équipe"}, "label": "Nom de l'équipe", "popup": {"title": "Sé<PERSON><PERSON>ner une personne pour {{positionLabel}}"}, "shortlabel": "Nom Equipe affiché sur PdS (3 caractères)"}, "list": {"header": {"actions": "Actions", "label": "Nom d'équipe", "shortlabel": "Nom Equipe affiché sur PdS (3 caractères)", "shortlabelmobile": "Nom Equipe PdS (3 car.)"}, "popup": {"delete": {"message": "Souhaitez-vous vraiment supprimer l'équipe: <b>{{teamName}}</b> ?", "subtitle": "d'une équipe", "title": "Suppression"}}}, "update": {"success": "L'équipe a été modifiée", "title": "Edition d'une équipe", "titlemobile": "Visualisation d'une équipe"}}, "update": {"error": "Échec de la modification du plan de service '{{label}}', Code d'erreur : {{code}}", "information_message": "Attention, toute mise à jour du plan de service entraînera la suppression des futures prestations liées à celui-ci", "success": "Le plan de service '{{label}}' a été modifé avec succès", "title": "Edition d'un Plan de Service", "titlemobile": "Detail administratif du plan de service"}, "version": {"closure": {"error": "Échec de la clôture de version du plan de service, Code d'erreur : {{code}}", "success": "La version de plan de service a été clôturé avec succès"}, "create": {"error": "Échec de la création de la version de plan de service '{{label}}', Code d'erreur : {{code}}", "success": "La version de plan de service '{{label}}' a été créée avec succès", "title": "Création d'une version des plages horaires", "titlemobile": "Création d'une version des plages horaires"}, "delete": {"error": "Échec de suppression de la version de plan de service , Code d'erreur : {{code}}", "success": "La version de plan de service a été supprimée avec succès"}, "details": {"from": "De", "subtitle": "des plages horaires", "title": "Détails", "to": "à"}, "edit": {"error": "Échec de la mise à jour de la version, code d'erreur : {{code}}", "information_message": "Attention, toute mise à jour de la version entraînera la suppression des futures prestations liées à celle-ci", "success": "La version a été mise à jour", "title": "Version des plages horaires", "titlemobile": "Version des plages horaires"}, "form": {"button": {"addversion": "Ajouter une version"}, "enddate": "Date de fin", "errors": {"modelVersionNotCreated": "La version du modèle n'a pas été créée", "total_slots": {"max": "Le nombre de plages horaires doit être au maximum de 24", "min": "Le nombre de plages horaires doit être au minimum de 1", "no_more_place": {"none": "Il n'est pas possible d'ajouter d'autres plages horaires entre {{startTime}} et {{endTime}}", "plural": "Il est possible d'ajouter que {{total}} plages horaires entre {{startTime}} et {{endTime}}", "singular": "Il est possible d'ajouter que {{total}} plage horaire entre {{startTime}} et {{endTime}}"}, "not_enough_place": "Il n'est pas possible d'ajouter d'autres plages horaires entre {{startTime}} et {{endTime}}"}}, "filling": {"automatic": "Automatique", "manual": "<PERSON>", "title": "Mode de remplissage: "}, "label": "Nom", "servicePlanType": "Type de plan de service", "startTime": "<PERSON><PERSON> d<PERSON>", "startdate": "Date de début", "totalSlots": "Plage", "version": "Version"}, "list": {"header": {"actions": "Actions", "enddate": "Date de fin", "label": "Version de plages horaires", "split": "Splité", "startdate": "Date de début", "startdate_mobile": "D<PERSON>but", "type": "Type de service", "type_mobile": "Type"}, "popup": {"closure": {"message": "Veuillez indiquer la date de clôture de la version du plan de service <b>{{name}}</b> du {{startDate}} ?", "subtitle": "d'une version de plan de service", "title": "Cl<PERSON><PERSON>"}, "delete": {"message": "Souhaitez-vous vraiment supprimer la version de plan de service <b>{{name}}</b> du {{startDate}} ?", "subtitle": "d'une version de plan de service", "title": "Suppression"}}}}}, "service_plan_model": {"closure": {"error": "Échec de la clôture du modèle de plan de service, Code d'erreur : {{code}}", "success": "Le modèle de plan de service a été clôturé avec succès"}, "copy": {"error": "Échec de la copie du modèle de plan de service", "new_name": "<PERSON><PERSON> - {{name}}", "success": "Le modèle de plan de service '{{name}}' a été copié avec succès", "title": "Copie d'un modèle de plan de service existant"}, "creation": {"error": "Échec de le création du modèle de plan de service '{{name}}', Code d'erreur : {{code}}", "success": "Le modèle de plan de service '{{name}}' a été créé avec succès", "title": "Création d'un nouveau modèle de plan de service"}, "delete": {"error": "Échec de la suppression du modèle de plan de service", "success": "Le modèle de plan de service a été supprimé avec succès"}, "form": {"backup_statuses": "Utilisation du code de backup", "buttons": {"addversion": "Ajouter une version"}, "entity": "Entité", "exclusive": "Exclusif ?", "function_operational": "Utilisation des fonctions opérationnelles ?", "has_vehicle": "Véhicule ?", "id": "Identifiant", "intervention_type": "Type d'intervention", "name": "Nom", "popup": {"warning": {"message": {"entity": "Vous venez de modifier l'attribut 'Entité' mais des plans de service sont liés au modèle précédent. Si vous confirmez la modification, ces plans de service seront supprimés", "vehicle": "Vous venez de modifier l'attribut 'Véhicule' mais des plans de service sont liés au modèle précédent. Si vous confirmez la modification, ces plans de service seront supprimés", "vehicletypes": "Vous venez de modifier l'attribut 'Type de Véhicule' mais des plans de service sont liés au modèle précédent. Si vous confirmez la modification, ces plans de service seront supprimés", "vehicletypes_removed": "Vous venez de modifier l'attribut 'Type de Véhicule' mais des plans de service sont liés au modèle précédent. Veuillez supprimer les vehicules des plans de services suivants :"}, "title": "Confirmation"}}, "vehicle_type": "Type de véhicule"}, "list": {"header": {"actions": "Actions", "entity": "Entité", "id": "Identifiant", "intervention": "Intervention", "name": "Nom"}, "title": "Liste des modèles de plan de service", "titlemobile": "Liste des modèles de plan de service"}, "popup": {"closure": {"message": "<PERSON><PERSON><PERSON><PERSON>-vous vraiment clôturer le modèle de plan de service <b>{{name}}</b> ?", "subtitle": "d'un modèle de plan de service", "title": "Cl<PERSON><PERSON>"}, "delete": {"message": "Souhaitez-vous vraiment supprimer le modèle de plan de service: <b>{{name}}</b> ?", "subtitle": "d'un modèle de plan de service", "title": "Suppression"}}, "title": "Administration des modèles de plan de service", "update": {"error": "Échec de la modification du modèle de plan de service '{{name}}', Code d'erreur : {{code}}", "information_message": "Attention, une mise à jour du modèle entraînera la suppression des futures prestations des plans de service liés au précédent modèle", "success": "Le modèle de plan de service '{{name}}' a été modifié avec succès", "title": "Edition d'un modèle de plan de service", "titlemobile": "Detail d'un modèle de plan de service"}, "version": {"closure": {"error": "Échec de la clôture du modèle de plan de service", "success": "Le modèle de plan de service a été clôturé avec succès"}, "copy": {"label": "Nouveau Label", "message": " Voulez-vous copier la version du modèle de plan de Service", "target": "Au", "title": "Copie de version de modèle de plan de Service"}, "create": {"success": "La version a été créée", "title": "Ajouter une version de positions"}, "delete": {"success": "La version '{{label}}' a été supprimée"}, "edit": {"information_message": "Attention, toute validation pourra entrainer la suppression des futures prestations des plans de service liés au précédent modèle", "position": {"degraded": {"title": "Positions minimales"}}, "success": "La version a été mise à jour", "title": "Edition d'une version de positions", "titlemobile": "Detail d'une version de positions"}, "form": {"button": {"addversion": "Ajouter une version de positions"}, "enddate": "Date de fin", "enddate_mobile": "Fin", "label": "Nom", "startdate": "Date de début", "startdate_mobile": "D<PERSON>but", "version": "Version"}, "list": {"header": {"actions": "Actions", "enddate": "Date de fin", "enddate_mobile": "", "label": "Nom", "startdate": "Date de début", "startdate_mobile": "D<PERSON>but"}}, "popup": {"closure": {"message": "Veuillez indiquer la date de clôture du modèle de plan de service <b>{{name}}</b> du {{startDate}} ?", "subtitle": "d'une version de modèle de plan de service", "title": "Cl<PERSON><PERSON>"}, "delete": {"message": "<PERSON><PERSON><PERSON><PERSON>-vous la suppression de la version <b>{{name}}</b> du {{startDate}}", "subtitle": "d'une version de modèle de plan de service", "title": "Suppression"}}, "position": {"empty": "Aucune position de ce type n'a été renseignée pour ce modèle", "function": {"operational_function": "Fonctions Opérationnelles", "operational_function_mobile": "Fonct. Opé.", "partialfullfill": "Position minimale"}, "type": {"additional": "Positions additionnelles", "complete": "Positions complètes", "degraded": "Positions minimales"}}}}, "vehicles": {"els_status": {"1": "Einsatzbereit Funk", "2": "Einsatzbereit Wache", "3": "Einsatz übernommen", "4": "Am Einsatzort", "5": "S<PERSON>re<PERSON>wuns<PERSON>", "6": "Nicht einsatzbereit", "7": "Patient aufgenommen", "8": "Am Transportziel", "1c": "Alarmiert am Einsatzbereit Funk", "2c": "<PERSON><PERSON><PERSON><PERSON>", "3c": "Alarmierung bei Einsatz übernommen", "4c": "Alarmierung am Einsatzort", "7c": "Alarmierung bei Patienten transport", "8c": "Alarmierung am Transportziel"}, "form": {"currentElsStatus": "Statut ELS actuel", "description": "Description", "elsStatusHistoryLablel": "Historique du statut ELS", "entity": "Entité", "id": "Identifiant", "name": "Nom", "registration": "Immatriculation", "registrationmobile": "Immat.", "type": "Type de véhicule"}, "list": {"header": {"actions": "Actions", "date": "Date", "description": "Description", "id": "Identifiant", "name": "Nom", "registration": "Immatriculation", "registrationmobile": "Immat.", "servicePlanName": "Plan de service associé", "backupgroup": "Groupe de backup", "status": "statut", "text": "Texte", "type": "Type de véhicule"}, "title": "Liste des véhicules", "titlemobile": "Liste des véhicules"}, "title": "Administration des véhicules", "update6vehicle": "Le véhicule {{idVehicle}} est passé au statut: {{statusVehicle}}", "view": {"title": "<PERSON>é<PERSON> d'un véhicule", "titlemobile": "<PERSON>é<PERSON> d'un véhicule"}}}, "allowance": {"scheduler": {"form": {"date": {"label": "Date de génération ou date de début de génération"}, "enddate": {"label": "Date de fin de génération"}, "entity": {"label": "Liste des entités", "nodata": "Aucune entité sélectionnée"}, "person": {"label": "Liste de personnes", "nodata": "<PERSON><PERSON>ne personne s<PERSON>"}, "popup": {"confirm": {"message": {"date": "La génération par date va être démarrée", "entity": "La génération par date et entités va être démarrée", "person": "La génération par date et personnes va être démarrée"}, "title": "Génération des indemnités"}}, "submit": {"success": "La génération a été prise en compte et va bientôt démarrer"}}}, "search": {"allowance": "Indemnité", "duration": "<PERSON><PERSON><PERSON> r<PERSON>", "firstname": "Prénom", "lastname": "Nom", "total": {"allowance": "Indemnité totale:", "duration": "Durée réelle totale:"}}}, "assignments": {"types": {"EXT": "Externe", "VOL": "Volontaire", "PRO": "Professionnel"}, "primarytypes": {"PRIMARY": "Primaire", "SECONDARY": "Auxiliaire", "TECHNICAL": "Technique"}}, "audit": {"actionDateTime": "Effectué à ", "actionType": {"ADD": "<PERSON><PERSON><PERSON>", "COPY_ADD": "Ajout par copie", "CLOSURE": "Cl<PERSON><PERSON>", "COPY_CLOSURE": "Clôture par copie", "COPY": "<PERSON><PERSON>", "COPY_ENTITY": "<PERSON><PERSON> d'entité", "CREATE": "Création", "DAY": "Co<PERSON> d'un jour", "DELETE": "Supression", "COPY_DELETE": "Supression par copie", "END": "Fin de la connexion logas", "MERGE": "Merge d'un slot", "MERGE_DELETED": "Suppression par merge", "MERGE_EXTENDED": "Etendu par merge", "TEAM_CLOSURE": "Clôture par équipe", "TEAM_ADD": "Ajout par équipe", "TEAM_DELETE": "Suppression par équipe", "FULL_AV_REM_ADD": "Ajout pour le reste de la dispo", "FULL_AV_REM_DELETE": "Suppression pour le reste de la dispo", "FULL_AV_ADD": "Ajout pour toute la dispo", "FULL_AV_DELETE": "Suppression pour toute la dispo", "FULL_AV_CLOSURE": "Clôture pour toute la dispo", "SLOT": "Copie d'un slot", "SPLIT": "Split d'un slot", "SPLIT_ADD": "Ajout par split", "SPLIT_SPLITTED": "<PERSON><PERSON><PERSON><PERSON> par split", "START": "Début connexion logas", "UPDATE": "Modification", "VIEW": "Consultation", "WEEK": "<PERSON><PERSON> de <PERSON>", "BOOKMARKED_UPDATE": "Modification Favoris", "PERM_CATEGORY_UPDATE": "Modification Catégorie", "PERM_CATEGORY_DELETE": "Suppression Catégorie", "PERM_CATEGORY_CREATE": "Création Catégorie", "PERM_SUBCAT_UPDATE": "Modification Sous-catégorie", "title": "Type d'action", "ASSOCIATE": "Association", "DEASSOCIATE": "Désassociation", "DEACTIVATE": "Désactivation", "REACTIVATE": "Réactivation", "IMPORT_CSV": "Importation CSV", "VALIDATE": "Validation", "VALID_ORDER_IMPACT": "Ordre impacté par validation", "DELETE_ORDER_IMPACT": "Ordre impacté par suppression", "CLOSURE_ORDER_IMPACT": "Ordre impacté par clôture", "UPDATE_ORDER": "Modification de l'ordre", "UPDATE_ORDER_IMPACT": "Ordre impacté par modification"}, "allowance": "Configuration des indemnités", "copy_prestation": {"day": "<b>{{user}}</b> a copié le jour <b>{{fromDate}}</b> sur le jour <b>{{targetDate}}</b> du Plan de service <b>{{servicePlanName}}</b>", "slot": "<b>{{user}}</b> a copié le slot du <b>{{firstSlot}}</b> sur le slot du <b>{{secondSlot}}</b> pour le Plan de service <b>{{servicePlanName}}</b>", "week": "<b>{{user}}</b> a copié la semaine du <b>{{fromDate}}</b> sur la semaine du <b>{{targetDate}}</b> du Plan de service <b>{{servicePlanName}}</b>"}, "details": "Détails", "function-operationals": {"list": {"title": "Fonctions opérationnelles", "interventiontype": "Type d'intervention", "label": "Nom", "portallabel": "Nom affiché", "status": "Statut", "order": "Ordre", "tags": "Tags"}, "export": {"title": "Export des fonctions opérationnelles", "entity": "Entité", "subentities": "Entités subordonnées"}, "assignments": {"title": "Assignation des fonctions opérationnelles", "entity": "Entité", "function": "Fonction opérationnelle", "person": "<PERSON><PERSON>"}}, "impersonatedUserName": "IAM logas", "link": "+ <PERSON><PERSON>", "logas_title": "Connexion en logas", "model_title": "Administration des modèles de plans de service", "permamonitor": {"configdpce": {"title": "Permamonitor - Configuration POJ", "deploymentplanname": "Plan d'armement", "entityname": "Entité", "categoryname": "<PERSON><PERSON><PERSON><PERSON>", "day": {"MONDAY": "<PERSON><PERSON>", "TUESDAY": "<PERSON><PERSON>", "WEDNESDAY": "<PERSON><PERSON><PERSON><PERSON>", "THURSDAY": "<PERSON><PERSON>", "FRIDAY": "<PERSON><PERSON><PERSON><PERSON>", "SATURDAY": "<PERSON><PERSON>", "SUNDAY": "<PERSON><PERSON><PERSON>", "PUBLIC_HOLIDAY": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}, "dayvalue": "Jour", "starthour": "<PERSON><PERSON>", "optimalvalue": "Optimal", "criticalvalue": "Critique", "unacceptablevalue": "Non-Acceptable"}, "configdpcecopy": {"title": "Permamonitor - Copie de Configuration POJ", "deploymentplanname": "Plan d'armement", "entityname": "Entité source", "toentityname": "Entité cible", "categoryname": "<PERSON><PERSON><PERSON><PERSON>", "starthour": "De (heure source)", "endhour": "à (heure cource)", "tostarthour": "De  (heure cible)", "toendhour": "à  (heure cible)"}, "deploymentplan": {"title": "Permamonitor - Plan d'armement", "name": "Nom PA", "startdate": "Date de début", "description": "Description"}, "serviceplan": {"title": "Permamonitor - Plan de service", "name": "PDS", "categoryname": "<PERSON><PERSON><PERSON><PERSON>", "deploymentplanname": "Nom PA", "subcategoryname": "Sous-cat<PERSON><PERSON><PERSON>", "bookmarked": "<PERSON><PERSON><PERSON>"}}, "rici": {"title": "RICI Audit", "alert-group": {"title": "Administration des alert groups", "headers": {"schema-alias": "<PERSON><PERSON><PERSON>", "range-name": "Range Name"}}, "simcard": {"title": "Administration des cartes SIM", "iccid": "ICCID", "msisdn": "MSISDN", "pin": "PIN", "status": "Statut", "associatedPager": "Pager TEC ID", "associatedPagerId": "Pager ID", "import": {"title": "Audits d'Importation CSV de Cartes SIM", "fileName": "Nom du Fichier", "importStatus": "Statut d'Importation", "totalRecords": "Total Enregistrements", "successfulRecords": "Enregistrements Réussis", "validationErrors": "Erreurs de Validation", "importErrors": "Erreurs d'Importation", "statuses": {"COMPLETED_WITH_ERRORS": "TERMINÉ AVEC ERREURS", "COMPLETED_SUCCESS": "TERMINÉ AVEC SUCCÈS", "FAILED_FILE_PROCESSING": "ÉCHEC TRAITEMENT FICHIER", "INITIATED": "INITIÉ", "INITIATED_FILE_INFO_UNAVAILABLE": "INFO INDISP<PERSON>IBLE POUR FICHIER INITIÉ"}}}, "pager": {"title": "Administration des pagers", "pagerId": "Pager ID", "serialNumber": "Numero de serie", "associatedSimCardIccid": "SIM - ICCID", "associatedSimCardMsisdn": "SIM -MSISDN", "assignmentType": "Type d'assignation", "assignedPersonName": "<PERSON><PERSON>", "assignedEntityName": "Entité assignée", "status": "Statut", "associatedSim": "SIM ID", "individualRic": "RIC Individuel", "import": {"title": "Audits d'Importation CSV de Pagers", "fileName": "Nom du Fichier", "importStatus": "Statut d'Importation", "totalRecords": "Total Enregistrements", "successfulRecords": "Enregistrements Réussis", "validationErrors": "Erreurs de Validation", "importErrors": "Erreurs d'Importation", "statuses": {"COMPLETED_WITH_ERRORS": "TERMINÉ AVEC ERREURS", "COMPLETED_SUCCESS": "TERMINÉ AVEC SUCCÈS", "FAILED_FILE_PROCESSING": "ÉCHEC TRAITEMENT FICHIER", "INITIATED": "INITIÉ", "INITIATED_FILE_INFO_UNAVAILABLE": "INFO INDISP<PERSON>IBLE POUR FICHIER INITIÉ"}}, "alert": {"group": {"title": "Audit des Assignations Pager-Groupe d'Alerte", "person": {"name": "Nom de la Personne", "cgdis": {"registration": {"number": "Numéro d'Enregistrement CGDIS"}}}, "alert": {"group": {"name": "Nom du Groupe d'Alerte"}}, "schema": {"alias": "<PERSON><PERSON>"}, "range": {"name": "Nom de la Plage"}, "pager": {"id": "<PERSON> du Pager", "serial": {"number": "Numéro de Série"}}, "action": "Action", "assigned": "<PERSON><PERSON><PERSON>", "removed": "Retiré", "detail": {"title": "Détails de l'Audit Pager-Groupe d'Alerte", "view": "Voir les détails"}}}}, "ricrange": {"title": "Administration des plages RIC", "name": "Nom", "type": "Type", "rangeStart": "Plage debut", "rangeEnd": "Plage fin", "entity": "Entité"}, "schema": {"title": "Administration des schemas RIC", "schemaName": "Nom", "schemaAlias": "<PERSON><PERSON>", "functionCodeA": "A-Fonction", "functionCodeB": "B-Fonction", "functionCodeC": "C-Fonction", "functionCodeD": "D-Fonction", "type": "Type"}, "tabs": {"headers": {"rici": "RICI"}}}, "person": "Utilisa<PERSON>ur", "person.tecid": "Utilisa<PERSON>ur", "action.datetime": "Date et heure de l'action", "action.type": "Type d'action", "cgdisregistrationnumber": "Mat. CGDIS (Initiateur)", "prestation": {"bypass_function": "By pass des FO", "date": "{{date}} de {{startDatetime}} à {{ endDatetime}}", "person": "Pour la personne", "position": "Position", "service_plan": "Plan de service", "slot": "Date", "headers": {"start": "Début Prestation", "end": "Fin Prestation", "position": "Position", "person": "Mat. CGDIS (impacté)", "personname": "<PERSON><PERSON> (impactée)"}}, "prestation_title": "Prestation", "copyprestations": {"title": "Copie de prestations", "headers": {"start": "Début slot source", "end": "Début slot cible"}}, "service_plan": {"column": "PDS", "entity": "Nom de l'entité associée", "name": "Nom du Plan de service", "type": "Type"}, "service_plan_model": {"entity": "Nom de l'entité associée", "name": "Nom du Modele de Plan de service", "type": "Type d'interventions"}, "service_plan_model_version": {"endDate": "Date de fin", "model_name": "Nom du Modèle associé", "name": "Nom de la version de Modele de plan de service", "spname": "Nom du plan de service associé", "startDate": "Date de début"}, "service_plan_version": {"endDate": "Date de fin", "name": "Nom de la version de Plan de service", "spname": "Nom du Plan de Service associé", "startDate": "Date de début"}, "serviceplan_title": "Administration des plans de service", "slot": {"title": "Action sur Slot de plan de service", "merge": "<b>{{user}}</b> a merge la tranche de <b>{{startDate}}</b> à <b>{{endDate}}</b> avec celle commençant <b>{{targetDate}}</b> pour le plan de service <b>{{servicePlanName}}</b>", "split": "<b>{{user}}</b> a splité la tranche de <b>{{startDate}}</b> à <b>{{endDate}}</b> à <b>{{targetDate}}</b> pour le plan de service <b>{{servicePlanName}}</b>", "headers": {"start": "Début slot source", "end": "Fin slot source", "targetstart": "Début slot cible", "targetend": "Fin slot cible"}}, "tabs": {"headers": {"permamonitor": "Permamonitor", "portal": "Portail", "function-operational": "Fonctions opérationnelles"}}, "type": {"COPY_PRESTATION": "Copie de prestations", "LOGAS": "Logas", "MODEL": "Modèle de plan de service", "PDS": "Plan de service", "PRESTATION": "Prestation", "SLOT": "Slot de plan de service", "VERSION_MODEL": "Version de modèle de plan de service", "VERSION_PDS": "Version de plan de service", "PERM_DEPLOYMENT_PLAN": "Permamonitor - Plan d'armement", "PERM_SERVICE_PLAN": "Permamonitor - Plan de service", "PERM_CONFIGDPCE": "Permamonitor - Configuration POJ", "PERM_CONFIGDPCE_COPY": "Permamonitor - Copie de Configuration POJ", "title": "Type d'audit", "ALLOWANCE_CONFIG": "Configuration des indemnités", "IMPORT_CSV": "Importation CSV", "FO": "Fonctions opérationnelles", "FO_EXPORT": "Export des Fonctions opérationnelles"}}, "authentication": {"error": "Vous n'êtes pas authentifié", "logout": {"success": "Vous avez été déconnecté"}, "lost": "Vous n'êtes plus authentifié"}, "availability_planning": {"copypopup": {"fromTo": "<li>{{start}} à {{startTime}} au {{end}} à {{endTime}}</li>", "message": "Êtes-vous sûr de vouloir copier la(les) disponibilité(s) du {{fromDate}} ? Les disponibilités existantes seront effacées.<br>Les disponibilités à copier sont celles du:<br>{{events}}", "messageNoData": "Êtes-vous sûr de vouloir copier la(les) disponibilité(s) du {{fromDate}} ? Les disponibilités existantes seront effacées.<br>Pas de disponibilités à copier", "messageWeek": "Êtes-vous sûr de vouloir copier la(les) disponibilité(s) du {{fromDate}} au {{toDate}} ? Les disponibilités existantes seront effacées.", "success": "Disponibilité(s) copiée(s) avec succès", "title": "Copier disponibilité(s)"}, "deletepopup": {"message": "Êtes-vous sûr de vouloir supprimer la disponibilité?", "success": "Disponibilité supprimée avec succès", "title": "Supprimer disponibilité"}, "list-placeholder": "Taper le début de votre recherche, pour filtrer le resultat", "list-placeholder-mobile": "Taper le début pour filtrer", "logas": "Personnes disponibles en logas:", "no_data": "Aucun planning à afficher", "popup": {"form": {"display_warning": "(Veuillez selectionner une entité)", "enddate": "Au", "entity": "Liste des centres", "information_message": "(Possibilité de sélectionner ceux possèdant un plan de serv. actif à ces dates)", "interventiontype": "Type d'intervention", "professional": "Professionnelle", "startdate": "<PERSON>", "success": "Disponibilité ajoutée avec succès", "type": "Type de disponibilité", "volunteer": "Volontaire", "disclaimer": "Si vous souhaitez renseigner une disponibilité de 24h,<br/>merci d'indiquer le jour suivant après le \"Au [...]\""}, "title": "Je souhaite me rendre disponible", "title_non_editable": "Disponibilité", "titlemobile": "Me rendre disponible"}, "prestationpopup": {"closebutton": "<PERSON><PERSON><PERSON>", "enddate": "Au", "entity": "Centre", "interventiontype": "Type d'intervention", "position": "Position", "serviceplan": "Plan de service", "startdate": "<PERSON>", "title": "Affectation"}, "show_prestations": "Voir les prestations", "title": "Mon planning et mes disponibilités"}, "backup-management": {"export": "Générer Export", "title-activation": "Activation Backup Mode CSU112", "title-export": "Générer Export de Backup", "reload": {"success": "La génération du rapport a été prise en compte. Me<PERSON>i de vérifier dans le dossier dédié"}}, "chart": {"legend": {"ambulance": "Ambulance", "availability": "Disponibilites réelles", "commandment": "Commandement", "complete": "Complet", "degraded": "Minimal", "dms": "DMS", "empty": "Vide", "fire": "Incendie/Sauvetage", "gis": "GIS", "incomplete": "Incomplet", "intervention": "Interventions", "mobile": {"ambulance": "Amb.", "commandment": "Com.", "complete": "Comp.", "degraded": "<PERSON>.", "dms": "DMS", "empty": "Vid.", "fire": "Inc.", "gis": "GIS", "incomplete": "Incomp.", "nodata": "Pas de don<PERSON>", "others": "Autres", "partial": "Par.", "samu": "<PERSON><PERSON>"}, "nodata": "Pas de don<PERSON>", "others": "Autres", "partial": "Partiel", "prestation": "Prestations", "samu": "SAMU", "total": "", "totals": "Total"}}, "current-situation": {"active-backup": "Backup active", "add-person": "+ ", "els-status": "Stat. veh.", "fromto": "De {{startDateTime}} à {{endDateTime}}", "legend": {"barracked": "<PERSON><PERSON><PERSON>", "pro": "Professionnel"}, "person-not-found": "Aucune personne disponible", "persons": "Personnes disponibles", "ressources": "Ressources", "schedule": "<PERSON><PERSON><PERSON>", "service-plan": "Plan de service", "status": "<PERSON>nier statut: {{date}}, statut {{statut}}", "time-slot": "Tranche", "title": "Situation courante"}, "dashboard": {"default": {"welcome": "Bienvenue sur le Portail CGDIS"}, "manager": {"all-service-plan": "Mes plans de service", "new-service-plan": "Nouveau plan de service", "other-service-plan": "Les autres plans de service", "occupancy-rate": "Taux d'occupation", "subtitle": "de tous mes plans de service", "title": "Taux d'occupation", "today": "<PERSON><PERSON><PERSON>'hui"}, "members": {"chart": {"ambulance": "Secours à personne", "ambulance-mobile": "SAP", "availability": "Disponibilité", "available-hours": "Heures disponibles", "commandment": "Commandement", "commandment-mobile": "COM", "dms": "<PERSON><PERSON><PERSON>", "dms-mobile": "DMS", "exclusive": "Exclusif", "fire": "Incendie/Sauvetage", "fire-mobile": "INCSA", "gis": "Groupes d'intervention spécialisés", "gis-mobile": "GIS", "mobile": {"ambulance": "Amb.", "availability": "Dispo.", "commandment": "Com.", "dms": "DMS", "exclusive": "Exc.", "fire": "Inc.", "gis": "GIS", "others": "Autres", "planning": "Planning", "professional": "Service pro.", "samu": "SAMU", "tooltip": "Heures aff."}, "noData": "Pas de don<PERSON>", "other": "", "others": "Autres", "others-mobile": "Autres", "planning": "Planning", "professional": "Service professionnel", "professional-mobile": "Serv. Pro.", "samu": "SAMU", "samu-mobile": "SAMU", "tooltip": "Heures affectées"}, "summary": {"availability": "Dispo:", "month": "<PERSON> mois", "perm": "Perm:", "week": "<PERSON><PERSON> se<PERSON>"}, "title": "Mes permanences"}, "news": {"button": "Voir d'autres news", "title": "L'actualité du Portail CGDIS"}}, "permamonitor": {"title": "Permamonitor", "title-admin": "Permamonitor Admin", "title-pds-config": "Configuration des Plans de Service du PA", "title-poj-config": "Gestion du plan d'armement", "title-deployment-plan-create": "Création d'une version de Plan d'Armement (PA)", "title-pa-management": "Gestion des versions de Plan d'Armement (PA)", "title-deployment-plan-update": "Édition d'un Plan d'Armement", "category-selector-label": "Sélectionner la catégorie", "pds-version-selector-label": "Version sélectionnée", "pds-entity-selector-label": "Entitée sélectionnée", "filter": {"title": "Filtres", "label": {"is-primary": "<PERSON><PERSON><PERSON>", "zone": "Zones", "period": "Période", "evolution": "Evolution", "actual-period": "<PERSON><PERSON><PERSON>", "is-sauvetage": "Type", "type": "Type", "localisation": "Localisation", "all-selector": "Toutes"}}, "admin": {"pds": {"table": {"toasts": {"added-favorite": "Le PdS a bien été marqué en favoris", "deleted-category": "Le PdS n'est plus associé à la catégorie", "removed-favorite": "Le PdS a bien été retiré des favoris", "updated-category": "Le PdS a bien été associé à une catégorie", "updated-subcategory": "Le PdS a bien été associé à une sous-catégorie"}, "pds": "Pds", "category": "Categorie", "subcategory": "Sous-categorie", "favorite": "<PERSON><PERSON><PERSON>", "favorite-true": "O<PERSON>", "favorite-false": "Non", "update-success": "Plan de service modifiée avec succès"}}, "pa": {"create-form": {"toast": "Plan d'armememnt est crée!", "loading": "Création et copie de la dernière configuration en cours - cela peut prendre un peu de temps", "name": "Nom", "description": "Description", "back": "Annuler", "start-date": "Date de début"}, "update-form": {"toast": "Le plan d'armement a bien été mise a jour.", "toast-warning": "Attention, la modification peut entraîner des modifications de configuration. Pensez à les vérifier!", "name": "Nom", "description": "Description", "back": "Annuler", "start-date": "Date de début"}, "table": {"version-number": "Numéro version", "description": "Description", "name": "Nom", "start-date": "Date début", "end-date": "Date fin", "actions": "Actions"}}, "poj": {"popup": {"copy": {"success": "La/es tranche/s a/ont été copiée/s avec succès", "entity": {"title": "Copie de configuration", "fromentity": "Entité source:", "toentity": "Entité cible:", "success": "Les tranches ont été copiées avec succès", "warning": "Attention, la copie de configuration écrasera la configuration existante pour l'entité cible"}}, "title": "<PERSON><PERSON>", "toast": "Copier avec succès!", "startTime": "<PERSON><PERSON><PERSON> le <PERSON>ré<PERSON>u", "endTime": "Jusqu'au créneau", "creation": {"title": "Création de configuration", "titlenoeditright": "Configuration manquante", "message": "Aucune configuration n'existe pour cette entité.<br/><br/>A la confirmation, une configuration sera créée et initialisée à 0 pour cette entité.<br/><br/><PERSON><PERSON><PERSON> de prendre en compte ce message et de modifier les paramètres si nécessaire", "messagenoeditright": "Aucune configuration n'existe pour cette entité.", "yes": "Confirmer", "yesnoeditright": "<PERSON><PERSON><PERSON>", "no": "Annuler"}}, "filters": {"zones-title": "Zones", "groups-title": "Groups", "cis-title": "CIS/GIS", "gis-title": "GIS"}, "table": {"no-cis-note": "Aucune configuration CIS/GIS existante", "time-title": "Tranche horaire", "days": {"monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>", "sunday": "<PERSON><PERSON><PERSON>", "holidays": "Jours Fériés"}}, "update-success-toast": "Valeur changée!", "group-overriden-update-success-toast": "La valeur a été réinitialisée à la somme des valeurs des CIS", "zone-overriden-update-success-toast": "La valeur a été réinitialisée à la somme des valeurs des Groupements et CIS", "maxvalue-success-toast": "La valeur maximale est de {{maxValue}}"}}, "view": {"table": {"information": {"title": "Information de la tranche en survol...", "box": {"chef-de-section": "Chef de Section", "maschiniste": "Machiniste", "chef-binome-1": "Chef Bin<PERSON> 1", "equipier-binome-1": "Équipier Binôme 1", "chef-binome-2": "Chef <PERSON> 2", "equipier-binome-2": "Équipier Binôme 2"}}}, "map": {"information": {"title": "Information de la region en survol..."}, "back-to-zones-button": "Retour zones", "no-config-available": "Aucune configuration existante", "statuses": {"optimal": "Acceptable", "critical": "Critique", "ideal": "Optimal", "unacceptable": "Non-Acceptable"}}, "poj": {"table": {"date": "Date", "armed": "ARM ", "armedAverage": "MOY", "armedMin": "MIN", "pojPercent": "%"}, "chart": {"next24": "Prochaines 24h", "last7": "Dernier 7 jours", "next7": "Prochain 7 jours", "last30": "Dernier 30 jours"}}}}, "date": {"days": {"friday": "<PERSON><PERSON><PERSON><PERSON>", "monday": "<PERSON><PERSON>", "saturday": "<PERSON><PERSON>", "sunday": "<PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>"}, "duration": {"dashboard": "{{hours}}h{{minutes}}", "dayshoursminutes": "{{days}}j {{hours}}h {{minutes}}m", "hours": "{{hours}}h", "hoursminutes": "{{hours}}h {{minutes}}m", "minutes": "{{hours}}h {{minutes}}m", "hoursminutesseconds": "{{hours}}h {{minutes}}m {{seconds}}s", "monthsdayshoursminutes": "{{months}}m {{days}}j {{hours}}h {{minutes}}m", "yearsmonthsdayshoursminutes": "{{years}}a {{months}}m {{days}}j {{hours}}h {{minutes}}m"}, "months": {"1": {"abr": "janv.", "full": "janvier"}, "2": {"abr": "févr.", "full": "<PERSON><PERSON><PERSON><PERSON>"}, "3": {"abr": "mars", "full": "mars"}, "4": {"abr": "avr.", "full": "avril"}, "5": {"abr": "mai", "full": "mai"}, "6": {"abr": "juin", "full": "juin"}, "7": {"abr": "juill.", "full": "juillet"}, "8": {"abr": "août", "full": "août"}, "9": {"abr": "sept.", "full": "septembre"}, "10": {"abr": "oct.", "full": "octobre"}, "11": {"abr": "nov.", "full": "novembre"}, "12": {"abr": "déc.", "full": "décembre"}}, "two-columns-view": {"close": "<PERSON><PERSON><PERSON> le calendrier", "open": "<PERSON><PERSON><PERSON><PERSON> le calendrier"}}, "default": {"loading": "Chargement...", "button": {"back": "Retour", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "logas": "Se connecter au logas", "scan": "Scan QR code", "submit": "Valider", "validate": "Valider", "ok": "OK"}, "construction": {"message": "Votre application s'agrandit. Revenez bientôt.", "title": "En construction"}, "day_more": "J+1", "expansion-filters": {"title": "Filtres"}, "no": "Non", "page": {"accessdenied": "Vous n'avez pas accès à cette page", "notfound": "La page demandée n'existe pas"}, "placeholder": {"search": "Rechercher...", "select-time-slot": "{{heureDebut}}:{{finDebut}} - {{heureFin}}:{{minuteFin}}"}, "popup": {"fromto": {"from": "Date de début", "to": "Date de fin"}}, "search": "<PERSON><PERSON><PERSON>", "table": {"noresult": "Aucun résultat", "total": "résultat(s)"}, "yes": "O<PERSON>"}, "entities": {"empty": "Sans affectation", "type": {"CIS": "CENTRE D'INTERVENTION ET DE SECOURS", "CS": "CENTRE DE SOUTIEN", "GROUP": "GROUPEMENT", "GS": "GROUPE SPECIAL", "NATIONAL": "NATIONAL", "SAMU": "SAMU", "UNKNOWN": "INCONNU", "ZONE": "ZONE", "DIRECTION": "DIRECTION", "MY_ENTITIES": "MES PDS"}}, "error": {"badgateway": "<PERSON><PERSON><PERSON> d'attente dépassé. Veuillez contacter votre administrateur", "entity": {"export": "Vous n'avez pas les droits pour faire un export sur cette entité", "update": "Impossible de mettre à jour le type de cette entité car celle-ci est parente d'autres entités"}, "export": {"default": "Une erreur est survenue durant la génération de l'export", "no_data_to_export": "Veuillez sélectionner au moins un type de donnée à exporter", "no_export_type": "Veuillez sélectionner au moins un type d'export"}, "form": {"permamonitor-poj-copy-id": {"ComparableAfterAnother": "L'heure choisi doit être après l'heure de début"}, "rici-range-form": {"ComparableAfterAnother": "La fin de la plage doit être après la plage début"}, "rici-schema-form": {"Min": "Le RIC suffix doit être entre 900 et 999 (inclus).", "Max": "Le RIC suffix doit être entre 900 et 999 (inclus)."}, "RiciRange": "La plage est déja occupée", "RiciRangeName": "Le nom de la plage doit être unique.", "RiciSchemaName": "Le nom du schéma doit être unique.", "RiciSchemaAlias": "Le Alias du schéma doit être unique.", "RiciSchemaSuffix": "Le Suffix du schéma doit être unique.", "OddNumber": "Le nombre doit être impair.", "Numeric": "Le champ doit être numérique", "DatetimeAfterAnother": "La date de fin doit être après la date de début", "DatetimeNotEquals": "Les dates ne doivent pas être identiques", "EmailWithTld": "<PERSON><PERSON><PERSON><PERSON> entrer une adresse email correcte", "cron": {"minutes": "Veuillez sélectionner une minute différente de 0", "hours": "Veuillez sélectionner une heure", "days": "Veuillez sélectionner au moins 1 jour"}, "FunctionOperationalTagCreateName": "Le nom est déjà utilisé", "FunctionOperationalTagUpdateName": "Le nom est déjà utilisé", "Emails": "Veuillez entrer des adresses mails valides", "FutureDate": "<PERSON><PERSON><PERSON>z sélectionner une date future", "FutureDatetime": "<PERSON><PERSON><PERSON><PERSON> sélectionner une date dans le futur", "Issi": "Veuillez entrer un numéro ISSI correct", "NotBlank": "Le champ est obligatoire", "NotEmpty": "Le champ est obligatoire", "NotNull": "Le champ est obligatoire", "Pager": "Veuillez entrer un numéro PAGER correct", "PagerIdRequiredForAssignment": "Le ID du pager est obligatoire pour cette affectation", "PagerMobile": "Veuillez entrer un numéro PAGER mobile correct", "PhoneNumber": "Veuillez entrer un numéro de téléphone correct", "RequiredIfBooleanValue": "Le champ est obligatoire", "ServicePlanArmingDelayRange": "Le délai d'armement est soit de 60 and 900 secondes", "ServicePlanArmingDelayRangeBarracked": "Le délai d'armement est soit de 60 and 900 secondes", "ServicePlanArmingDelayRangeNotBarracked": "Le délai d'armement doit être compris entre 60 and 1200 secondes", "ServicePlanMinimumArmingDelayRangeBarracked": "Le délai d'armement est soit de 60 and 900 secondes", "ServicePlanModelFullEditable": "Le champ est obligatoire", "ServicePlanModelVersionUniqueLabel": "Le nom de la version du modèle existe déjà", "ServicePlanUniqueLabel": "Le nom du plan de service existe déjà", "ServicePlanUpdateModel": "Le modèle selectionné est incompatible avec ce plan de service. Les dates de début ou de fin du modèle ne correspondent pas avec celle(s) du plan de service.Veuillez sélectionner un autre modèle ou rectifier la/les date(s).", "ServicePlanUpdateModelWithPrestations": "Impossible de modifier le modèle tant que des prestations sont planifiées sur ce plan de service", "ServicePlanVehicleMandatory": "Le champ est obligatoire", "ServicePlanVersionUniqueLabel": "Le nom de la version existe déjà", "ServicePlanVersionUniqueStartdate": "Une version existe déjà avec la même date de début", "Size": "La taille doit être comprise entre {{args1}} et {{args0}}", "SplitSlotTimeValidator": "V<PERSON> devez selectionner une heure valide", "UpdateServicePlanArmingDelayRangeBarracked ": "Le délai d'armement est soit de 60 and 900 secondes", "UpdateServicePlanArmingDelayRangeNotBarracked": "Le délai d'armement doit être compris entre 60 and 1200 secondes", "VolunteerAvailabilityCheckAssignment": "Cette personne n'est pas affectée à certaines entités pendant ces dates et pour ce type de disponibilité", "VolunteerAvailabilityUpdateStartDate": "La date de début doit être future ou après la date de début précédente", "PermDeploymentPlanNameExists": "Le nom du plan d'armement doit être unique.", "PermDeploymentPlanCreationDate": "La date de création doit être après la date de création du dernier plan d'armement.", "PermDeploymentPlanStartTimeUpdate": "La nouveau date de dépard doit être après la date de création du dernier plan d'armement et avant la date du prochain.", "availability-full-calendar-popup": {"DatetimeAfterAnother": "L'heure de fin doit être après l'heure de début", "DatetimeNotEquals": "Les heures ne doivent pas être identiques"}, "datetimeDateNotNull": "La date est obligatoire", "invalid": "Le formulaire est invalide", "max": "La valeur est trop grande", "maxlength": "Veuillez saisir au maximum {{requiredLength}} caractères", "min": "La valeur est trop petite", "minlength": "Veuillez saisir au moins {{requiredLength}} caractères", "required": "Le champ est obligatoire", "service-plan": {"split": {"inpast": "Le split ne peut pas être dans le passé", "notinslotrange": "L'heure de split doit être après l'heure de début du slot et avant l'heure de fin du slot"}, "team": {"label": {"exist": "Le nom de l'équipe existe déjà pour ce plan de service"}, "members": {"duplicate": "Vous ne pouvez pas sélectionner une même personne sur plusieurs positions"}, "shortlabel": {"exist": "Le nom court de l'équipe existe déjà pour ce plan de service"}}}}, "person": {"already_logas": "La personne sélectionnée est un adminstrateur. Le LogAs ne le permet pas, merci de sélectionner une nouvelle personne.", "export": "La periode d'export est trop longue (max 6 mois)", "no_assignments": "Cette personne n'est affectée à aucune entité, ses coordonnées ne sont pas modifiables."}, "prestation": {"allowance": {"configuration": {"pastorcurrent": "La configuration est passée ou courante", "samestartdate": "Une configuration existe déjà avec cette date de début"}}, "copy": "Impossible de copier cette ou ces prestations, car la version du plan de service ou du modèle est différente à la date souhaitée", "exclusive": "Impossible de créer ces prestations.Une ou plusieurs personnes sont déjà assignées sur un plan de service exclusif.", "nothing-copy": "Aucune prestation à copier"}, "service_plan": {"assignteam": {"empty": "L'équipe est vide"}, "box-free": "La box sélectionnée est déjà utilisée par un autre plan de service", "person": {"already_assigned": "Cette personne est déjà affectée sur cette tranche horaire"}, "timeslot": {"merge": {"default": "Le merge des slots n'a pas été effectué", "service_plan_different": "Le merge des slots n'a pas été effectué: Les slots sont sur des plans de service différents", "slot_already_started": "Le merge des slots n'a pas été effectué: Le slot est déjà commencé"}}, "vehicle-free": "Le véhicule sélectionné est déjà utilisé par un autre plan de service", "version": {"copy": "La version n'a pas pu être copié", "exist_with_start_date": "Une version du plan de service existe déjà avec la même date de début"}}, "service_plan_model": {"update": {"forbidden": "L'édition de ce modèle de plan de service n'est pas autorisé"}, "version": {"copy": "La version n'a pas pu être copié", "dates": "Impossible de mettre à jour cette version avec ces dates, car celles-ci sont incohérentes avec une ou plusieurs précédentes versions", "exist_with_start_date": "Une version du modèle existe déjà avec la même date de début", "notfound": "Aucune version n'existe pour le modèle de plan de service sélectionné"}}, "technical_error": "Erreur technique. Veuillez contacter votre administrateur", "timeout": "<PERSON> requête met trop de temps à s'exécuter ou vous avez été déconnecté. Si le problème persiste, veuillez-vous reconnecter", "userrights": {"delete": "Cette permission est inaliénable au rôle. Impossible de la supprimer"}, "volunteeravailability": {"delete": {"prestations_already_assigned": "Impossible de supprimer la disponibilité: au moins une prestation existe"}, "duplicate": {"error": "{{startDay}}/{{startMonth}}/{{startYear}} à {{startHour}}:{{startMinute}} au {{endDay}}/{{endMonth}}/{{endYear}} à {{endHour}}:{{endMinute}}", "message_pro": "Une ou plusieurs disponibilités professionnelles existent déjà. Veuillez la/les modifier ou la/les supprimer. Les disponibilités déjà existantes sont celles du:", "message_vol": "Une ou plusieurs disponibilités volontaires existent déjà. Veuillez la/les modifier ou la/les supprimer. Les disponibilités déjà existantes sont celles du:", "title": "Attention"}, "update": {"prestations_already_assigned": "Impossible de mettre à jour la disponibilité: au moins une prestation existe"}}, "rici": {"simcard": {"alreadyassociated": "La carte SIM est déjà associée à un pager, merci d'en sélectionner une autre", "alreadyexists": {"ICCID": "Une carte SIM avec cet ICCID existe déjà.", "MSISDN": "Une carte SIM avec cet MSISDN existe déjà."}}, "pager": {"alreadyexists": {"SERIAL_NUMBER": "Le numéro de serie est déjà présent dans le système, merci d'en renseigner un nouveau.", "PAGER_ID": "Le Pager ID est déjà présent dans le système, merci d'en renseigner un nouveau."}}, "person": {"already_has_pager_assigned": "L'utilisateur possède déjà un pager, merci d'en sélectionner un nouveau."}, "ric": {"already_used": "Le RIC est déjà utilisé.", "already_used_by_alert_group": "Le RIC est déjà utilisé par un groupe d'alerte.", "is_not_number": "La valeur RIC n'est pas un nombre valide.", "is_divisible_by_8": "La valeur RIC ne peut pas être divisible par 8.", "outside_of_entity_range": "La valeur RIC est en dehors de la plage d'entité.", "no_available_ric_in_range": "Aucun RIC disponible trouvé dans les plages pour cette entité.", "no_active_range_for_entity": "Aucune plage RIC active trouvée pour cette entité."}}}, "error-management": {"delete": {"success": "Le message a été supprimé"}, "list": {"header": {"actions": "Actions", "cause": "Cause", "creationdate": "Date", "message": "Message", "objectidentifier": "Id de l'erreur", "status": "Statuts", "type": "Type"}}, "resend": {"error": "<PERSON><PERSON><PERSON> lors du rafraîchissement", "success": "<PERSON><PERSON><PERSON> rafra<PERSON><PERSON><PERSON> avec succès"}, "status": {"CREATED": "Nouveau", "DONE": "<PERSON><PERSON><PERSON><PERSON>", "RESENT": "<PERSON><PERSON><PERSON>"}, "title": "Gestion des erreurs", "type": {"ASSIGNEMENT_QUEUE_NOTIFICATION": "Notification de début d'affectation", "DELETE_ONE_VEHICLE_HISTORIC": "Supression des status expirés d'un véhicule", "ERROR_MESSAGE_TO_IGNORE": "Message d'erreur à ignorer", "INTERNAL_PROCESS": "E<PERSON>ur interne", "NOTIFIER_EMAIL": "Notification email", "NOTIFIER_MANAGER": "Notification manager", "PROCESS_DELETE_BACKUP_GROUPS": "Supression des groupes de backup (process)", "PROCESS_DELETE_PERSON_FROM_MSNAV": "MSNAV - Suppression d'une personne (process)", "PROCESS_DELETE_PERSON_IN_ELS": "ELS - Suppression d'une personne (gateway)", "PROCESS_DELETE_PERSON_IN_LEVESO": "LEVESO - Suppresion d'une personne (gateway)", "PROCESS_EXPORT_BACKUP_112": "Export des groupes de backup (process)", "PROCESS_EXPORT_MAIL": "Export des mail", "PROCESS_INSERT_APTITUDES_FROM_XLSX": "Creation des aptitudes par le fichier XLSX (process)", "PROCESS_LUXDOK_INSERT_INTERVENTION": "LUXDOK - Ajout d'une intervention (process)", "PROCESS_READ_APTITUDES_FROM_XLSX": "Lecture des aptitudes dans le fichier XLSX (process)", "PROCESS_REFRESH_BACKUP_GROUPS": "Mise à jour des groupes de Backup (process)", "PROCESS_REFRESH_ENTITIES": "Mise à jour des entités (process)", "PROCESS_REFRESH_PERSON_ASSIGNMENTS": "Rafraîchir les assignations des personnes (process)", "PROCESS_REFRESH_VEHICLE": "Mise à jour des véhicules (process)", "PROCESS_SERVICE_PLAN_EXPORT_CSV": "Export des service plans au format CSV (process)", "PROCESS_SERVICE_PLAN_EXPORT_ONE_CSV": "Export d'un service plan au format CSV (process)", "PROCESS_SERVICE_PLAN_SEND_FILLING_EXPORT": "Exportation de Remplissage de l'Envoi du Plan de Service", "PROCESS_SERVICE_PLAN_UPDATE": "ELS - Mise à jour d'un plan de service (process)", "PROCESS_SERVICE_PLAN_UPDATE_ALL": "ELS - Mise à jour de tous les plans de service (process)", "PROCESS_UPDATE_APTITUDES_FROM_GEPS": "GEPS: Mise à jour des aptitudes", "PROCESS_UPDATE_ENTITY_LEVESO": "LEVESO - Mise à jour d'une entité (process)", "PROCESS_UPDATE_PERSON_ELS": "ELS - <PERSON><PERSON> à jour d'une personne (process)", "PROCESS_UPDATE_PERSON_FROM_MSNAV": "MSNAV - Mise à jour d'une personne à partir de MSNAV (process)", "PROCESS_UPDATE_PERSON_GEPS": "GEPS - Mise à jour d'une personne (process)", "PROCESS_UPDATE_PERSON_LEVESO": "LEVESO - Mi<PERSON> à jour d'une personne (process)", "PROCESS_UPDATE_PERSON_LUXDOK": "LUXDOK: Mise à jour d'une personne (process)", "PROCESS_UPDATE_PERSON_MSNAV": "MSNAV - Mi<PERSON> à jour d'une personne vers MSNAV (process)", "PROCESS_UPDATE_PERSON_PAGERMGMT": "Mise a jour du Pager d'une personne (process)", "PROCESS_UPDATE_REMARKS_FROM_GEPS": "GEPS - Mise à jour des remarques", "REFRESH_BACKUP_GROUPS": "Mise à jour des groupes de Backup", "REFRESH_ENTITIES": "Mise à jour des entités", "REFRESH_ONE_BACKUP_GROUPS": "Mise à jour des groupes de Backup", "REFRESH_VEHICLES": "Mise à jour des véhicules", "SEND_ALL_ASSIGNMENT_CLOSURE": "Notification des affectations clôturées", "SEND_ALL_PRESTATION_QUEUE": "Notification des prestations en attente", "SEND_ONE_PRESTATION_QUEUE": "Notification d'une prestation en attente", "SERVICE_PLAN_DELETE": "ELS - Suppression d'un plan de service", "SERVICE_PLAN_OPTIONAL_BACKUP_PUSH": "ELS - Envoi de la suppression des groupes de backup et optionnel", "SERVICE_PLAN_PUSH": "ELS - <PERSON><PERSON> à jour d'un plan de service", "SMSI_EXPORT": "Export SMSI", "SMSI_SERVICE_PLAN_FILLING": "Remplissage du plan de service par SMSI", "UPDATE_ENTITY_LEVESO": "LEVESO - Mi<PERSON> à jour d'une entité", "UPDATE_PERSON_ELS": "ELS - <PERSON><PERSON> à jour d'une personne", "UPDATE_PERSON_GEPS": "GEPS - Mise à jour d'une personne", "UPDATE_PERSON_LEVESO": "LEVESO - <PERSON>se à jour d'une personne", "UPDATE_PERSON_LUXDOK": "LUXDOK - Mise à jour d'une personne", "UPDATE_PERSON_MSNAV": "MSNAV - <PERSON>se à jour d'une personne", "UPDATE_PERSON_PAGERMGMT": "Mise a jour du Pager d'une personne", "UPDATE_VEHICLE_TACTICAL_STATE": "Mise à jour du statut du véhicule vers ELS", "UPDATE_VEHICLE_TACTICAL_STATE_INPUT": "Mise à jour du statut du véhicule par ELS", "GET_SLOTS_FOR_ELS": "Récupération de slots pour ELS", "ONE_PRESTATIONS_ALLOWANCE": "Génération d'indemnités", "PROCESS_DELETE_BOX": "Suppression d'une box", "PROCESS_DELETE_ONE_BACKUP_GROUP": "Suppression d'un groupe de backup", "PROCESS_FUNCTION_OPERATIONAL_STATUS_UPDATE": "Mise à jour du statut de la fonction opérationnelle", "PROCESS_REFRESH_BOX": "Mise à jour des box (process)", "PROCESS_VEHICLE_UPDATE_NOTICE": "Mise à jour de la notice d'un véhicule (process)", "REFRESH_BOXES": "Mise à jour des box", "UPDATE_VEHICLE_NOTICE_ELS": "Mise à jour de la notice d'un véhicule"}}, "file": {"upload": {"error": {"title": "<PERSON><PERSON><PERSON> de Téléchargement", "size": "Le fichier '{{fileName}}' dépasse la taille maximale autorisée ({{maxSize}} Mo).", "type": "Le type de fichier '{{fileName}}' n'est pas autorisé. Types autorisés : {{allowedTypes}}.", "maxitems": "Nombre maximal de fichiers ({{maxItems}}) dépassé.", "default": "Erreur lors du téléchargement du fichier '{{fileName}}'."}}}, "general_availability": {"legend": {"barracked": "<PERSON><PERSON><PERSON>", "professional": "Disponibilité professionnelle", "volunteer": "Disponibilité volontaire"}, "no_data": "Aucune donnée pour ce jour", "title": "Ensemble des disponibilités de la semaine"}, "general_information": {"active_assignment": {"list": {"allClosedAssignment": "Afficher les affectations clôturées", "endDate": "Date de fin", "entityName": "Entité", "primaryType": {"list": {"PRIMARY": "Primaire", "SECONDARY": "Auxiliaire", "TECHNICAL": "Technique"}, "title": "Type de priorite"}, "startDate": "Date de début", "type": {"list": {"EXT": "EXT", "PRO": "PRO", "VOL": "VOL"}, "title": "Statut"}}, "title": "Affectations"}, "activity": {"allowance": "Indemnités", "allowance_mobile": "Indem", "availabilities": {"duration": "<PERSON><PERSON><PERSON>", "end": "Date de fin", "entities_availability": "Entités", "entities_availability_with_barracked": "Entités / Caserné", "intervention_types": "Types d'intervention", "start": "Date de début", "start_mobile": "D<PERSON>but", "title": "Disponibilités"}, "day": "Jours", "detail": {"availabilities": "Disponibilités réelles", "availabilities_mobile": "Dispo. réelles", "interventions": "Interventions", "interventions_mobile": "Interventions", "prestations": "Prestations réelles", "prestations_mobile": "Presta. réelles", "totals": "Total"}, "hours": "<PERSON><PERSON>", "interventions": {"alarm": "Date", "end": "Date de fin", "end_mobile": "Fin", "function_name": "Nom de la fonction", "keyword": "<PERSON>t clé", "mission_number": "Numéro de mission", "number": "<PERSON><PERSON><PERSON><PERSON>", "number_mobile": "<PERSON><PERSON>", "radio": "Indicatif radio", "start": "Date de début", "start_mobile": "D<PERSON>but", "title": "Interventions"}, "month": "<PERSON><PERSON>", "periode": "Période", "prestations": {"duration": "Durée indemnisée", "end": "Date de fin", "end_mobile": "", "portal_label": "Plan de service", "position_label": "Position", "realDuration": "<PERSON><PERSON><PERSON> r<PERSON>", "realDuration_mobile": "<PERSON><PERSON><PERSON> rée<PERSON>.", "start": "D<PERSON>but", "start_mobile": "D<PERSON>but", "title": "Prestations"}, "range": "Intervalle", "semester": "<PERSON><PERSON><PERSON>", "title": "Mon activité", "title_logas": "Activité", "totals": {"allowance": "Indemnités", "allowance_mobile": "Indem", "details": {"barrackedAmount": "<PERSON><PERSON><PERSON>", "entity": "Entité", "notBarrackedAmount": "Non caserné", "proDuration": "<PERSON><PERSON><PERSON> profession<PERSON>", "proDuration_mobile": "Du<PERSON>e pro.", "realDuration": "<PERSON><PERSON><PERSON> r<PERSON>", "sum": "Cumul", "total": "Total", "volDuration": "Durée volontaire", "volDuration_mobile": "Durée vol."}, "duration": "<PERSON><PERSON><PERSON>", "label": "Label", "nbPerson": "Nombre de personnes", "title": "<PERSON><PERSON><PERSON>"}, "type": "Statuts", "week": "<PERSON><PERSON><PERSON>", "year": "<PERSON><PERSON>"}, "address": {"city": "Ville", "country": "Pays", "number": "N°", "street": "Rue", "subtitle": "Addresse {{number}}", "title": "Adresse postale", "zip_code": "Code Postal"}, "aptitudes": {"list": {"aptitude": "Aptitude", "description": "Description", "endDate": "Date de fin", "isImported": "Ces données ont été importées. En cas de question veuillez contacter le secrétariat DMS-STP", "startDate": "Date de début", "status": {"title": "Validité de l'aptitude"}, "userComment": "Commentaire"}, "title": "Aptitudes"}, "availability": {"type": {"ambulance": "Secours à personne", "commandment": "Commandement", "dms": "<PERSON><PERSON><PERSON>", "fire": "Incendie/Sauvetage", "gis": "Groupes d'intervention spécialisés", "other": "", "others": "Autres", "samu": "SAMU"}}, "bank_account": {"bank_code": "Code banque", "bic": "BIC", "iban": "IBAN", "title": "Coordonnées bancaires"}, "contact_information": {"alert_email": "Envoi de l'alerte mail sur", "alert_email_notification": "Envoi des notifications mails sur", "alert_email_error_notification": "Utiliser un autre email pour les notifications d'erreur", "alert_email_error_notification_email": "Email pour les notifications d'erreur", "alert_mobile": "Envoi de l'alerte mobile sur", "general": "Coordonnées", "operational": "Coordonnées Opérationnelles", "private": {"email": "<PERSON><PERSON> privé", "mobile": "Mobile privé", "phone": "Téléphone privé"}, "professional": {"email": "Email professionnel", "mobile": "Mobile professionnel", "phone": "Téléphone professionnel"}, "ric": "RIC Individuel", "ricMobile": "RIC Mobile", "warning_no_mail": "Attention! Aucun mail n'est renseigné. Veuillez svp. renseigner votre adresse.", "warning_no_mail_mobile": "Aucun mail", "warning_one_mail": ""}, "diplomas": {"list": {"comment": "Commentaire", "ident": "Identification", "name": "Nom", "type": "Type", "validFrom": "Valide du", "validUntil": "<PERSON><PERSON> jusqu'au"}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "driver_license": {"list": {"category": "<PERSON><PERSON><PERSON><PERSON>", "endDate": "Date de fin", "startDate": "Date de début"}, "title": "Permis de conduire"}, "function_operational": {"title": "Fonctions operationnelles"}, "general": "Général", "managerial_occupations": {"list": {"header": {"allClosedOccupations": "Afficher les emplois cloturés", "assignment": "Entité", "assignment_description": "Description de l'assignation", "code": "Code", "endDate": "Date de fin", "external": "External", "external_desktop_reduced": "EXT", "external_mobile": "EX", "label": "Nom", "nomination": "Date de nomination", "professional": "Professionnel", "professional_desktop_reduced": "PRO", "professional_mobile": "PR", "startDate": "Date de début", "status": {"list": {"ONGOING": "En cours", "RESIGNED": "Démissionnaire", "REVOKED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SUSPENDED": "Suspendu"}, "title": "Statut"}, "type": "Type", "volunteer": "Volontaire", "volunteer_desktop_reduced": "VOL", "volunteer_mobile": "VL"}}, "title": "<PERSON><PERSON><PERSON><PERSON>"}, "medical": "Informations médicales", "medical-information": {"legend": {"apt": "Apte", "apt_with_restriction": "Apte avec restriction", "inapt": "Inapte", "temporary_inaptitude": "Inaptitude temporaire"}}, "medical-report": {"aptitude": "Aptitude", "empty": "Pas de rapport", "new-aptitudes": "Nouvelle(s) aptitude(s) reçue(s)", "new-restrictions": "Nouvelle(s) restriction(s) reçue(s)", "no-aptitude": "Aucune aptitude reçue", "no-restriction": "Aucune restriction reçue", "remark": "Remarque(s) :", "restriction": "Restriction", "start": "D<PERSON>but", "statut": "Statut", "title": "Rapport médical du {{ publicationDate }}"}, "operational": "Opérationnel", "operational_contact": {"email": "Email", "issi": "", "mobile": "Mobile", "pager": "Pager", "pager_mobile": "Pager mobile", "phone": "", "send_alarm_clock": "Envoyer Alarmdepesch"}, "operational_dates": {"list": {"header": {"endDate": "Date de fin", "startDate": "Date de début", "type": {"list": {"PROFESSIONAL": "Professionnel", "VOLUNTEER": "Volontaire"}, "title": "Type"}}}, "title": "Dates operationnelles"}, "operational_functions": {"list": {"granted": "Cette fonction n'est pas encore autorisée dans ce centre", "label": "Fonctions opérationnelles"}, "title": "Fonctions Opérationnelles"}, "operational_grades": {"list": {"header": {"allClosedGrade": "", "allClosedGrades": "Afficher les grades clôturés", "code": "Code", "decreeDate": "Date du décret", "echelon": "Echelon", "endDate": "Date de fin", "gradeType": {"list": {"DMS": "DMS", "GIS": "GIS", "INCSASAP": "INCSA/SAP"}, "title": "Statuts"}, "professional": "Professionnel", "professional_desktop_reduced": "PRO", "professional_mobile": "PRO", "psupp": "Pompier de support", "samu": "<PERSON><PERSON>", "samu_mobile": "EXT", "startDate": "Date de début", "title": "Titre", "volunteer": "Volontaire", "volunteer_desktop_reduced": "VOL", "volunteer_mobile": "VOL"}}, "title": "Grades operationnels"}, "operational_occupations": {"list": {"header": {"code": "Code", "endDate": "Date de fin", "entity": "Entité", "external": "External", "external_desktop_reduced": "EXT", "external_mobile": "EX", "label": "Nom", "nomination": "Date de nomination", "professional": "Professionnel", "professional_desktop_reduced": "PRO", "professional_mobile": "PR", "startDate": "Date de début", "status": {"list": {"ONGOING": "En cours", "RESIGNED": "Démissionnaire", "REVOKED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SUSPENDED": "Suspendu"}, "title": "Statut"}, "type": "Type", "volunteer": "Volontaire", "volunteer_desktop_reduced": "VOL", "volunteer_mobile": "VL"}}, "title": "Emplois Opérationnels"}, "operational_volunteer_internship": {"duration": "<PERSON><PERSON><PERSON>", "endDate": "Date de fin", "startDate": "Date de début", "title": "Stage volontaire"}, "operational_young_firefighter": {"firstName": "Prénom", "first_tutor": "Premier tuteur", "lastName": "Nom", "parental_consent": "Accord <PERSON><PERSON><PERSON>", "phoneNumber": "Numéro de téléphone", "second_tutor": "Deuxième tuteur", "title": "<PERSON><PERSON> pompier"}, "person_medical_information": {"remark": "<PERSON><PERSON><PERSON>", "title": "Informations médicales"}, "personal_information": {"birthdate": "Date de naissance", "birthplace": "Lieu de naissance", "both": "Tous", "cgdisrn": "Matricule CGDIS", "code": "Titre", "firstname": "Prénom", "foreign": "Mat<PERSON><PERSON> étranger", "gender_f": "<PERSON>mme", "gender_i": "Indéterminé", "gender_m": "<PERSON><PERSON>", "hiring": "Date d'entrée CGDIS", "iam": "Identifiant IAM", "is_candidate": "Candidat", "is_external": "SAMU", "is_intern": "Stagiaire", "is_operational_firefighter": "Pompier opérationnel", "is_operational_firefighter_short": "Pomp.ope", "is_professional": "Professionnel", "is_professional_adm_tech": "Professionnel administratif et technique", "is_professional_adm_tech_short": "Pro.admTech", "is_professional_operational": "Professionnel opérationnel", "is_professional_operational_short": "Pro.ope", "is_professional_tech": "Professionnel technique", "is_retired": "Retraité", "is_samu": "<PERSON><PERSON>", "is_support_firefighter": "Pompier support", "is_support_firefighter_short": "Pomp.sup", "is_technical": "Technique", "is_veteran": "Vé<PERSON>ran", "is_volunteer": "Volontaire", "is_volunteer_tech": "Volontaire technique", "is_young_firefighter": "<PERSON><PERSON> pompier", "is_young_firefighter_short": "<PERSON>une pomp.", "lastname": "Nom", "nationalRegistrationNumber": "Matricule National", "person_title": "Titre", "rnrpp": "Matricule RNRPP", "sex": "<PERSON>e", "status": "Statuts", "tic": "TIC", "title": "Informations personnelles", "vacation": "Date de sortie CGDIS", "hiring_date": "Date d'entrée CGDIS"}, "restrictions": {"list": {"description": "Description", "startDate": "Date de début", "startDate_mobile": "D<PERSON>but", "type": "Type", "userComment": "Commentaire"}, "title": "Restrictions"}, "roles": {"title": "Rôles dans l'application"}, "suspension": {"list": {"endDate": "Date de fin", "startDate": "Date de début"}, "title": "Suspensions"}}, "i18n": {"error": {"timeout": "Timeout"}}, "intervention": {"types": {"ambulance": "Secours à personne", "ambulance_mobile": "SAP", "commandment": "Commandement", "commandment_mobile": "COM", "dms": "DMS", "dms_mobile": "DMS", "fire": "Incendie/Sauvetage", "fire_mobile": "INCSA", "gis": "GIS", "gis_mobile": "GIS", "others": "Autres", "others_mobile": "Autres", "samu": "SAMU", "samu_mobile": "SAMU"}}, "layout": {"navigation": {"footer": {"copyright": "{{year}} Corps Grand-Ducal Incendie & Secours. Tous droits réservés.", "copyrightmobile": "{{year}} CGDIS. Tous droits réservés.", "link": {"conditions": "Conditions d'utilisation", "confidential": "Confidentialité"}}, "menu": {"items": {"admin": {"box": "", "boxes": "Boxes", "entities": "Entités", "export": "Profils d'Export", "function_operational": "Gestion des F.O.", "function_operational_order": "Gestion de la priorité des F.O.", "general-message": "Messages généraux", "position_template": "Gestion des templates de position F.O.", "public-holiday": "Jours fériés", "service_plan": "Plans de Service", "service_plan_model": "Modèles de plan de service", "fotags": "Gestion des tags des F.O.", "vehicles": "Véhicules"}, "administration": "Administratif", "allowance": {"configuration": "Parametrage des indemnités", "search": "Visualiser les indemnités", "settings": "Configuration des indemnités", "title": "Gestion des indemnités"}, "audit": {"list": {"empty": "Aucun audit disponible", "title": "Audit"}}, "backup-112-management": "Mode backup CSU112", "current_situation": "Situation courante", "fos": "Fonctions Opérationnelles", "my_planning": "Editer mes disponibilités", "news": "News", "optional_backup_group_management": {"list": {"empty": "Aucun groupe visible", "title": "Liste des groupes de l'entité"}, "title": "Gestion des groupes d'alertes"}, "organizational": "Organisationnel", "people_management": {"function": "Gestion des Fonctions Opérationnelles", "list": {"empty": "Aucun membres visibles", "title": "Liste des membres de l'entité"}, "title": "Gestion des personnes", "volunteer_availabilities_logas": "<PERSON><PERSON>rer les disponibilités de mes membres"}, "people_medical_information": {"list": {"empty": "Aucun membres visibles", "title": "Visualisation des informations médicales"}, "title": "Visualiser les informations médicales"}, "performances": "Mon planning & disponibilités", "permamonitor": {"menu": "Permamonitor", "dashboards": "Dashboards du Plan d'Armement", "administration": "Administration XXX", "pa-management": "Gestion des versions de Plan d'Armement", "config": "Configuration du Plan d'Armement"}, "plan_travail": "Mes plans de travail", "service_plan": "Mes plans de service", "sync": {"audit_management": "Gestion audit des données", "error_management": "Gestion des erreurs", "logas": "Accès au logas", "scheduler_management": "Gestion des schedulers", "title": "Système"}, "user_rights": {"permissions": "Permissions", "roles": "<PERSON><PERSON><PERSON>", "title": "Droits utilisateur"}, "vehicules": "Mes véhicules", "rici": {"menu": "RICI", "alert-groups": "Gestion des groupes d'alerte", "sim": "Gestion des Cartes SIM", "ranges": "Gestion des plages RIC", "schemas": "Gestion des schémas RIC", "pagers": "Gestion des pagers RIC", "pagers-assignments": "Gestion de l'affectation pagers et groupes d'alerte", "pager-lost": "Gestion des pagers perdus", "declare-pager-lost": "<PERSON><PERSON><PERSON><PERSON>er un pager perdu"}}, "title": "<PERSON><PERSON>"}, "profile": {"myplanning": "Mon planning", "mypreferences": {"assignment_notification": "Activer les notifications d'affectations:  ", "assignment_notification_mobile": "Notifications d'affectations:  ", "availability_notification": "Activer les notifications de disponibilités sur tranche incomplete:  ", "availability_notification_mobile": "Notifications de disponibilités:  ", "calendar_days": "Nombre de jour par semaine:  ", "closed_calendar": "Calendrier caché par défaut:  ", "error_notification": "Activer les notifications d'erreur:  ", "error_notification_mobile": "Notifications d'erreur:  ", "export-person-full": "Export complet des personnes de l'entité", "export-person-notechnical": "Export des personnes sans les affectations technique", "export-person-technical": "Export uniquement des affectations techniques", "export-prestations": "Export des prestations semestrielles", "export-prestations-first-semester": "Export des prestations du 1er semestre de {{year}}", "export-prestations-range": "Export des prestations d'une periode spécifique", "export-prestations-second-semester": "Export des prestations du 2nd semestre de {{year}}", "first-day": {"1": "<PERSON>n", "2": "Mar", "3": "<PERSON><PERSON>", "4": "<PERSON><PERSON>", "5": "Ven", "6": "Sam", "7": "<PERSON><PERSON>"}, "first_day_of_week": "Premier j<PERSON> de la semaine:  ", "fisrt_day_of_week": "Premier j<PERSON> de la semaine:", "full_availability": "Ajout de prestation sur l'ensemble de la disponibilité par défault:", "full_availability_mobile": "Prestation sur toute la dispo:", "input_geps_error_notification": "Activer les notifications des erreurs sur les rapports medicaux:", "input_geps_error_notification_mobile": "Erreurs rapports médicaux: ", "input_geps_notification": "Activer les notifications de disponibilités sur les rapports medicaux:", "input_geps_notification_mobile": "Notifications médicales: ", "language": "Langue:", "notification": "Préférences sur les notifications", "other_prestations": "Activer l'affichage des multiples prestations pour une personne:", "other_prestations_mobile": "Affichage multiple (prestations):", "partial_availability": "Activer l'affichage des distinctions sur les disponibilités partielles:", "partial_availability_mobile": "Disponibilités partielles:", "planning": "Préférences sur le planning et disponibilités", "prestation_notification": "Activer les notifications de prestations: ", "prestation_notification_mobile": "Notifications de prestations: ", "profil": "Préférences sur le profil", "serviceplan": "Préférences sur les plans de service", "show_prestation": "Afficher les prestations par défaut: ", "show_prestation-mobile": "Prestations par défaut: ", "title": "Mes préféren<PERSON>", "update-error": "Échec de la mise à jour de vos préférences", "update-success": "Préférence mise à jour avec succès", "update-vehicle": "Préférence sur les mises à jour de statuts de véhicule", "update-vehicle-6": "Activer les notifications pour un statut 6", "update-vehicle-6_mobile": "Notifications pour un statut 6: ", "update-vehicle-6c": "Activer les notifications pour un statut 6c", "update-vehicle-6c_mobile": "Notifications pour un statut 6c: "}, "myprofile": "Mon profil"}, "title": " Portail <strong>CGDIS</strong> ", "titleTest": " Portail <strong>CGDIS</strong> Test"}}, "logas": {"connected": "Vous êtes connecté en tant que {{userName}}", "info": "Pour vous connecter en logas, veuillez choisir une personnes disponibles ci-dessous :", "logout": "Revenir au profil initial", "successful": "Vous êtes connecté", "warning": "Attention, vous etes connecté en Logas"}, "login": {"field": {"password": "Mot de passe", "username": "Identifiant"}, "successful": "Vous êtes connecté"}, "managerial_occupations": {"list": {"header": {"assignment": "", "assignment_description": "", "code": "", "endDate": "", "external": "", "label": "", "nomination": "", "professional": "", "startDate": "", "status": {"list": {"ONGOING": "", "RESIGNED": "", "REVOKED": "", "SUSPENDED": ""}, "title": ""}, "type": "", "volunteer": ""}}, "title": ""}, "mobile-enrollment": {"ask": "Voulez-vous enroller votre mobile ?", "title": "Enrollement d'un mobile"}, "news": {"detail": {"button": "Voir d'autres news", "title": "Détail News"}, "list": {"empty": "Pas de news actuellement", "title": "L'actualité du Portail CGDIS"}}, "no_data": "<PERSON><PERSON><PERSON> don<PERSON>", "operational_dates": {"list": {"header": {"endDate": "", "startDate": "", "type": {"list": {"PROFESSIONAL": "", "VOLUNTEER": ""}, "title": ""}}}, "title": ""}, "operational_functions": {"popup": {"message": "En ôtant la fonction opérationnelle, il se peut que des prestations déjà entrées sur des plans de service futurs soient supprimées.<br><PERSON><PERSON><PERSON><PERSON>-vous continuer ?", "subtitle": "Oter d'une fonction opérationnelle", "title": "Attention"}}, "operational_grades": {"list": {"header": {"code": "", "decreeDate": "", "echelon": "", "endDate": "", "gradeType": {"list": {"DMS": "", "GIS": "", "INCSASAP": ""}, "title": ""}, "professional": "", "psupp": "", "samu": "", "startDate": "", "title": "", "volunteer": ""}}, "title": ""}, "operational_volunteer_internship": {"duration": "", "endDate": "", "startDate": ""}, "operational_young_firefighter": {"firstName": "", "first_tutor": "", "lastName": "", "parental_consent": "", "phoneNumber": "", "second_tutor": ""}, "optional_backup_group_management": {"list": {"allgroups": "Afficher également les groupes des entités subordonnées", "allgroupsmobile": "Afficher entités subordonnées", "cgdisregistrationnumber": "Matricule CGDIS", "members": "Me<PERSON><PERSON>", "name": "Nom", "select_entity": "Sélectionner une entité", "title": "Groupes d'alertes"}, "title": "Gestion des Groupes d'alertes"}, "people_management": {"functions": {"allpersons": "Afficher également les fonctions des entités subordonnées", "allpersonsmobile": "Afficher entités subordonnées", "ambulance": "Secours à personnes", "availability": "Statut relation", "cgdisregistrationnumber": "Matricule CGDIS", "cgdisregistrationnumbermobile": "Mat. CGDIS", "commandment": "Commandement", "create": {"success": "La fonction a été ajoutée avec succès"}, "deletion": "Retirer la fonction", "deletion_message": "Souhaitez-vous vraiment retirer la fonction opérationnelle ?", "details": {"cgdisRegistrationNumber": "Matricule CGDIS", "firstName": "Prénom", "lastName": "Nom"}, "dms": "DMS", "fire": "Incendie/Sauvetage", "firstname": "Prénom", "function_operational": "Fonctions opérationnelles", "function_operational_mobile": "Fonctions opé.", "function_operational_tag": "Tag", "function_operational_tag_mobile": "Tag", "gis": "GIS", "granted": "Autorisée ?", "lastname": "Nom", "other": "<PERSON><PERSON>", "person_number_ext": "Nombre d'Externe", "person_number_ext_mobile": "EXT", "person_number_pro": "Professionnels", "person_number_pro_mobile": "PRO", "person_number_vol": "Volontaires", "person_number_vol_mobile": "VOL", "person_number_total_distinct": "Total réel", "person_number_total_distinct_mobile": "Total", "person_number_vol_tec": "Volontaires tech.", "person_number_vol_tec_mobile": "VOL Tech", "person_number_pro_tec": "Professionnels tech.", "person_number_pro_tec_mobile": "PRO Tech", "samu": "SAMU", "select_entity": "Sélectionner une entité", "status": "Statuts de la personne", "summary": "Vue d'ensemble", "summaryheader": {"totalpersons": "Pompiers", "totalfos": "Fonctions", "filters": {"interventiontype": {"label": "Types d'intervention"}, "fo": {"label": "Fonctions Opérationnelles"}}}, "table": {"filterName": "Nom", "no_data": "Pas de FO à assigner", "status": "Statut", "assignmenttype": "Type d'affectation", "vehicletype": "Type de véhicule", "withoutfunctions": "Personnes avec aucune fonction"}, "technical": "Afficher les affectations techniques", "title": "Fonctions", "type": "Type", "update": {"information_message": "Attention, une mise à jour (autorisation ou suppression) d'une fonction opérationnelle entraînera la suppression des futures prestations liées à cette fonction uniquement", "success": "La fonction a été modifiée avec succès"}}, "general_contact": {"update": {"success": "Les coordonnées administratives ont été modifiées avec succès"}}, "operational_contact": {"update": {"success": "Les coordonnées opérationnelles ont été modifiées avec succès"}}, "title": "Gestion des Personnes"}, "people_medical_information": {"functions": {"allpersons": "Afficher également les personnes des entités subordonnées", "allpersonsmobile": "Afficher entités subordonnées", "apt": "Apte", "apt_with_restriction": "Apte avec restriction", "aptitude": "Aptitudes", "aptitudes": "Aptitudes / Validité", "aptitudesmobile": "Apt. / Val.", "cgdisregistrationnumber": "Matricule CGDIS", "cgdisregistrationnumbermobile": "Mat. CGDIS", "expired": "Expiré", "firstname": "Prénom", "inapt": "Inapte", "lastname": "Nom", "noSuspensionEndDate": "Suspension de manière indéfinie", "restrictions": "Restrictions", "restrictionsmobile": "Restr.", "status": "Validité de l'aptitude", "suspensionEndDate": "Suspension jusqu'en", "suspensionToggle": "Exclure les personnes avec une suspension", "suspensions": "Suspensions", "technical": "Afficher les affectations techniques", "temporary_inapt": "Inapte temporaire", "toggle_filters": "Filtres", "undefined": "Pas de date définie", "validity_from": "Validité de ", "validity_until": "Expi<PERSON> après le", "without_aptitudes": "Sans aptitude"}}, "planning": {"status": {"availability": "Disponible", "planning": "Planning", "professional": "Professionnel"}}, "prestation": {"assignteam": {"success": "{{nbPrestations}} prestation(s) a(ont) été créée(s)"}, "copy": {"popup": {"assignment": "Assignation", "assignment-message": "Ces prestations n'ont pas pus être copiées car les personnes concernées ne sont plus assignées à l'entité du plan de service à la date de copie", "confirm": {"copy-day-message": "Voulez-vous copier la journée du <b>{{ dayName }} {{ dayNumber }} {{ monthName }} </b>?", "copy-week-message": "Vou<PERSON>z-vous copier la semaine <b>du {{ dateFrom }} au {{ dateTo }} </b>?", "target-date": "A la date :", "title": "Confirmation"}, "day": "Date", "day-message": "Une erreur s'est produite lors de la copie des prestations du :", "function": "Fonction Opérationelle", "function-message": "Ces prestations n'ont pas pus être copiées car les personnes concernées ne possèdent plus les fonctions opérationelles nécessaires à la date de copie", "message": "Les prestations suivantes n'ont pas pus être copiées", "slotnotexist": "Slot", "slotnotexist-message": "Ces prestations n'ont pas pus être copiées car le slot n'existe pas à la date de copie", "title": "Avertissement"}, "success": "Les prestations ont toutes été copiées avec succès"}}, "scheduler-management": {"description": {"backup_optional_group_push_request": "De<PERSON>e de mise à jour de tous les groupes optionnels et de backup à l'ELS", "entities_push_request": "De<PERSON><PERSON> de mise à jour de toutes les entités à l'ELS", "export_backup_112": "Execute l'export des fichiers de backup pour le 112", "person_assignments_push_request": "Demande de mise à jour des assignations des personnes à MSNAV et LuxDok", "prestation_queue_all": "Envoi des notifications des prestations qui sont mises en attente", "prestationsallowancesall": "Exécute le calcul des indemnités pour une date (ou range de dates), entités, personnes", "service_plan_csv_export_all": "Export de l'ensemble des plans de service dans le file system", "service_plan_update_all_requestall": "Envoi de tous les plans de services courants à l'ELS", "vehicle_box_push_request": "De<PERSON><PERSON> de mise à jour de toutes les boxes à l'ELS", "vehicle_push_request": "De<PERSON><PERSON> de mise à jour de tous les véhicules à l'ELS"}, "error": "La tâche n'a pas pus être relancée. Veuillez contacter un administrateur.", "header": {"action": "Action", "cron": "<PERSON><PERSON><PERSON>", "description": "Description", "name": "Nom"}, "name": {"backup_optional_group_push_request": "Push Grp. Opt. et Back. ELS", "entities_push_request": "Push Entités ELS", "export_backup_112": "Export Backup 112", "person_assignments_push_request": "Push Assignations Personne MSNAV/LuxDok", "prestation_queue_all": "Notifications des prestations", "prestationsallowancesall": "Génération des indemnités", "service_plan_csv_export_all": "Export CSV", "service_plan_update_all_requestall": "Push PdS ELS", "vehicle_box_push_request": "Push Boxes l'ELS", "vehicle_push_request": "Push Véhicules ELS"}, "popup": {"message": "Êtes-vous sûr de vouloir lancer cette synchronisation ?", "subtitle": "Lancement d'une synchronisation", "title": "Attention"}, "success": "La tâche a été relancée avec succès !", "title": "Gestion des schedulers"}, "service-plan": {"popup": {"split": {"success": "Le slot a été séparé"}}}, "service_plan": {"add": {"error": {"nopersonselected": "Choisissez une personne"}}, "add_people": "+ Ajouter pers.", "add_people_mobile": "+ <PERSON>ne", "address": "<PERSON><PERSON><PERSON>", "armingDelay": "<PERSON><PERSON><PERSON>", "armingPriority": "Priorité d'armement", "automatic_filling": "Remplissage Automatique", "availability-details-between": "De {{startDate}} à {{endDate}}, dispo pendant {{duration}}", "availability-details-between-mobile": "De {{startDate}} à {{endDate}}", "availability-details-from": "A partir de {{startDate}}, dispo pendant {{duration}}", "availability-details-from-mobile": "A partir de {{startDate}}", "availability-details-until": "Jusqu'à {{endDate}}, dispo pendant {{duration}}", "availability-details-until-mobile": "<PERSON>s<PERSON><PERSON>'<PERSON> {{endDate}}", "availability-duration": "dispo pendant {{duration}}", "availability-duration-after": "apr<PERSON>, dispo pendant {{duration}}", "availability-duration-before": "avant, dispo pendant {{duration}}", "backupGroup": "Groupe de backup", "button": {"validate": {"default": "Valider", "professional": "Valider (professionnel)", "volunteer": "Valider (volontaire)"}}, "city": "Ville", "country": "Pays", "day_selector": "{{ date }}", "details": "Détails du plan de service", "details-link": "+ <PERSON><PERSON><PERSON>", "entity": "Entité", "exclusive": "Exclusif", "filter": {"all": "Tous", "archived": "Archivés", "current": "Courants", "future": "<PERSON><PERSON><PERSON>"}, "filter-link": "+ Filtres", "filter-link-toclose": "- Filtres", "filterTitle": "Filtres", "full-availability-long": "Ajouter pour 12h ", "full-availability-long-mobile": "Ajout. 12h:", "full-availability-short": "Pour toute la disponibilité", "full-availability-short-mobile": "Dispo:", "job": {"available": "Disponibles (volontaire)", "available-mobile": "Disponibles (vol)", "available-pro": "Disponibles (professionnel)", "available-pro-mobile": "<PERSON><PERSON><PERSON><PERSON> (pro)", "unaffected": "Sans affectation", "unavailable": "Indisponibles", "unavailable-prestation": "Permanence en cours"}, "legend": {"status": {"barracked": "<PERSON><PERSON><PERSON>", "complete": "Complet", "degraded": "Minimal", "empty": "Vide", "incomplete": "Incomplet", "nodata": "Pas de don<PERSON>", "partial": "Partiel"}}, "manual_filling": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "manual_synchronization_els": "+ Synchroniser vers ELS", "member": {"popup": {"tab": {"header": {"counter": {"tooltip": "{{counterLeft}} personne(s) sur {{counterRight}} a(ont) les fonctions"}}}}, "prestations": {"between": "Entre {{start}} et {{end}}", "from": "A partir de {{start}}", "until": "Jusque {{end}}"}}, "model": "<PERSON><PERSON><PERSON><PERSON>", "no_data": "<PERSON><PERSON><PERSON> don<PERSON>", "no_functions_vol": "Pas autorisée a la fonction (volontaire)", "no_functions_pro": "Pas autorisée a la fonction (professionnel)", "optionalGroup": "Groupe optionnel", "partial_functions_vol": "Fonctions réduites (Volontaire)", "partial_functions_pro": "Fonctions réduites (Professionnel)", "popup": {"add": {"error": {"nopersonselected": "<PERSON><PERSON><PERSON>z sélectionner une personne"}, "professional-prestation": "Prestation pro."}, "delete": {"prestation": {"message": "Souhaitez-vous vraiment supprimer l'affectation de <b>{{firstname}} {{lastname}}</b> ?", "subtitle": "d'une affectation", "success": "La prestation a bien été supprimée", "title": "Suppression", "with-availability": "Supprimer la disponibilté associée"}}, "exclusive": {"message": "Attention, le plan de service sélectionné est <b>exclusif</b>. Toutes les prestations non additionnelles en cours de cette personne seront supprimées. Souhaitez vous continuer ?", "title": "Attention"}, "export": {"csv": {"message": "Veuillez sélectionner la date de début et la date de fin de l'export", "success": "L'export a été créé"}, "pdf": {"message": "Veuillez sélectionner la date de début et la date de fin de l'export", "success": "L'export a été créé"}, "send-filling": {"message": "Veuillez sélectionner la date de début et la date de fin de l'export à envoyer", "success": "La demande d'export a été prise en compte et sera envoyée"}}, "no_functions": {"message": "Attention, la personne <b>ne possède pas</b> les fonctions opérationnelles pour cette position. Souhaitez vous continuer ?", "title": "Attention, fonctions opérationnelles manquantes"}}, "popupTitle": "Plan de service", "positions": "Positions nécessaires au service", "prestations-availability": "Dispos :", "schedule_selector": {"merge": {"popup": {"message": "Vous allez merger le slot de {{fromStartTime}}h à {{fromEndTime}}h avec celui de {{toStartTime}}h à {{toEndTime}}h", "title": "Merger 2 slots", "warningmessage": "<b>Attention, les disponibilités des personnes ci-dessous ne couvrent pas la totalité des prestations ou ces personnes sont indisponibles:</b>"}}, "split": {"popup": {"closedVersionName": "La version clôturée", "message": "Vous allez séparer le slot de {{startTime}}h à {{endTime}}h. <br/> Veuillez indiquer l'heure de début du nouveau slot :", "nextVersionName": "La version suivante", "splittedVersionName": "La version déplacée", "title": "Séparation d'un slot"}}}, "semester_selector": "Semestre {{semesterNumber}} {{year}} ", "servicePlanType": "Type de plan de service", "team": "Équipe", "title": "Mes plans de service", "title_one": "Mon plan de service", "vehicle": "Véhicule", "versions": {"copy": {"label": "Nouveau nom", "message": "Voulez-vous copier cette version de plan de service", "target": "Au", "title": "Copie de version de plan de service"}, "dates": {"default": "Du {{startDay}}/{{startMonth}}/{{startYear}} au {{endDay}}/{{endMonth}}/{{endYear}}", "from": "À partir du {{startDay}}/{{startMonth}}/{{startYear}}", "label": "Version:", "month": "Mois: {{month}} {{year}}", "year": "Année: {{year}}"}, "empty": "Aucun plan de service disponible", "filling": {"automatic": "Automatique", "manual": "<PERSON>", "title": "Mode remplissage: "}, "registration": "Pas d'immat.", "type": {"barracked": "<PERSON><PERSON><PERSON>", "barracked-mobile": "Cas.", "not_barracked": "Non caserné", "professional": "Professionnel", "title": "Type: "}, "vehicle": "Véhicule: "}, "week_selector": "Du {{ startDate }} au {{ endDate }}", "zipCode": "Code Postal"}, "tooltip": {"add": "Ajouter", "add-position": "Ajouter une position", "audit": "Voir l'audit", "availability": "Voir les disponibilités", "closure": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copy": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "É<PERSON>er", "export": {"csv": "Exporter en CSV", "excel": "Exporter en excel", "default": "Exporter", "pdf-landscape": "Exporter en PDF (paysage)", "pdf-new-format": "Exporter en PDF (Portail)", "pdf-portrait": "Exporter en PDF (portrait)", "prestation": "Exporter le recapitulatifs des prestations", "prestations": "Exporter les prestations", "prestations-detail": "Export le detail des prestations", "prestations-penalty": "Export le detail des astreintes", "prestations-penalty-explain": "Cet export prend uniquement en compte les filtres suivant : Types d'intervention Autre, Commandement, DMS, GIS et Statut PROFESSIONNEL", "prestations-summary": "Export le recapitulatif des prestations", "send-service-plan-filling": "Envoyer le plan par email"}, "force-export": "Forcer l'exécution", "info-availabilities-list": "La liste est limitée à 50 noms apparents mais en saisissant le nom souhaité dans la barre de recherche, celui ci sera filtré automatiquement et sera présent dans la liste de recherche si existant", "logout": "Déconnexion", "management": "Gestion administrative", "new": "Nouveau", "next": "Suivant", "previous": "Précédent", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "reload": "Relancer", "service-plan": {"export": {"prestationspdf": "Exporter la semaine en pdf"}, "merge": "Merger le slot", "more": "Plus d'options", "split": "<PERSON><PERSON><PERSON><PERSON> le <PERSON>"}, "validate": "Valider"}, "user-rights": {"permission": {"ROLE_PERMISSION_PERMAMONITOR": {"description": " \tCette permission permet à l'utilisateur de voir le menu et les sous menus du permamonitor Organisationnel uniquement en READ ONLY. Il peut accèder aux fonctionnalités des écrans comme les filtres.\nUn administrateur Portail a d'office cette permission.", "name": "(PermaMonitor) - Accéder aux écrans opérationnels"}, "ROLE_PERMISSION_PERMAMONITOR_ADMIN_VIEW": {"description": "Cette permission permet à l'utilisateur de voir le menu et la liste des versions de Plan d'Armement\nUn administrateur Portail a d'office cette permission.", "name": "(PermaMonitor) - Accéder aux écrans administratifs"}, "ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_POJ": {"description": " \tCette permission permet à l'utilisateur de mettre à jour la configuration d'une POJ pour une version de plan d'armement et une catégorie donnée.\nAttention, tout changement entraînera une modification définitive de la configuration.\nUn administrateur Portail a d'office cette permission.\n\nA noter : des règles de gestion s'appliquent.", "name": "(PermaMonitor) - Permission d'éditer la configuration d'un POJ"}, "ROLE_PERMISSION_PERMAMONITOR_ADMIN_ACCESS_CONFIG": {"description": " Cette permission permet à l'utilisateur d'accéder aux sous-écrans de configuration.\nSi cette permission n'est pas donnée, alors les boutons seront grisés et non cliquables\nSi cette permission est donnée, alors les boutons seront rouges et cliquables (accès aux sous-écrans en RO ou non)\nUn administrateur Portail a d'office cette permission.", "name": "(PermaMonitor) - Permission d'accéder aux écrans de configuration (boutons d'action)"}, "ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_DEPLOYMENT_PLAN": {"description": "Cette permission permet à l'utilisateur de mettre à jour le nom et la description d'une version de plan d'armement courant.\nCette permission permet à l'utilisateur de mettre à jour le nom, la description et la date de début d'une version de plan d'armement future. Attention, le changement de date de début entraînera une modification de la date de fin de la version d'armement précédente.\nUn administrateur Portail a d'office cette permission.\n\nA noter : des règles de gestion s'appliquent.", "name": "(PermaMonitor) - Permission d'éditer une version de Plan d'Armement"}, "ROLE_PERMISSION_PERMAMONITOR_ADMIN_CREATE_DEPLOYMENT_PLAN": {"description": "Cette permission permet à l'utilisateur de créer une nouvelle version de plan d'armement à minima J+1. Cette création clôturera la version courante à J-1 par rapport à la date de début du nouveau Plan d'Armement\nUn administrateur Portail a d'office cette permission.", "name": "(PermaMonitor) - Permission de créer une nouvelle version de Plan d'Armement"}, "ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_DEPLOYMENT_PLAN_CONFIG": {"description": "Cette permission permet à l'utilisateur de mettre à jour la configuration d'un PA pour une version de plan d'armement (catégorie, sous catégorie et favori).\nAttention, tout changement entraînera une modification définitive de la configuration.\nUn administrateur Portail a d'office cette permission.\n\nA noter : des règles de gestion s'appliquent.", "name": "(PermaMonitor) - Permission d'éditer la configuration d'un PA"}, "ROLE_PERMISSION_112_BACKUP": {"description": "Autoriser l'accès au menu backup 112", "name": "Accèder aux écrans backups 112"}, "ROLE_PERMISSION_112_BACKUP_ACTIVATE": {"description": "Autoriser l'activation du backup 112", "name": "Permission d'activer le mode backup 112"}, "ROLE_PERMISSION_112_BACKUP_EXPORT": {"description": "Autoriser l'export des backup 112", "name": "Permission de déclencher les exports des backups 112"}, "ROLE_PERMISSION_112_BACKUP_VIEW": {"description": "Autoriser la vue des informations du mode backup 112", "name": "Accèder aux informations du mode backup 112"}, "ROLE_PERMISSION_ADMIN": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "description": "Autoriser l'accès au menu administratif", "name": "Accèder aux écrans administratifs"}, "ROLE_PERMISSION_ADMIN_ALLOWANCE_SETTINGS": {"description": "Autoriser l'édition des indemnités", "name": "Permission d'éditer les indemnités"}, "ROLE_PERMISSION_ADMIN_AUDIT": {"description": "Voir la liste des manipulations de données", "name": "Permission de voir la liste des manipulations de données"}, "ROLE_PERMISSION_ADMIN_AUDIT_PERMAMONITOR": {"description": "Voir la liste des manipulations de données Permamnonitor", "name": "Permission de voir la liste des manipulations de données Permamonitor"}, "ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL": {"description": "Voir la liste des manipulations de données du Portail", "name": "Permission de voir la liste des manipulations de données du Portail"}, "ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_PRESTATIONS": {"description": "Voir la liste des manipulations de données des prestations", "name": "Permission de voir la liste des manipulations de données des prestations"}, "ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_SERVICE_PLAN": {"description": "Voir la liste des manipulations de données des plans de service", "name": "Permission de voir la liste des manipulations de données des plans de service"}, "ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_SP_MODEL": {"description": "Voir la liste des manipulations de données des modèles de plan de service", "name": "Permission de voir la liste des manipulations de données des modèles de plan de service"}, "ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_LOGAS": {"description": "Voir la liste des manipulations de données du logas", "name": "Permission de voir la liste des manipulations de données du logas"}, "ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_ALLOWANCE": {"description": "Voir la liste des manipulations de données des indemnités", "name": "Permission de voir la liste des manipulations de données des indemnités"}, "ROLE_PERMISSION_ADMIN_BOX": {"description": "Autoriser l'access a l'ecran de gestion des boxes", "name": "Permission d'accéder à l'ecran de gesiton des boxes"}, "ROLE_PERMISSION_ADMIN_BOX_UPDATE": {"description": "Autoriser la mise à jour d'une box", "name": "Permission de mettre à jour une box"}, "ROLE_PERMISSION_ADMIN_BOX_VIEW": {"description": "Autoriser la vue des la liste des boxes", "name": "Permission de voir la liste des boxes"}, "ROLE_PERMISSION_ADMIN_ENTITIES": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "dependency": "Accéder au menu des entités induit obligatoirement la permission de visualiser la liste des entités. La permission pour modification est accordée indépendamment.", "description": "Autoriser l'accès au menu des entités", "name": "Accèder à la liste des entités"}, "ROLE_PERMISSION_ADMIN_ENTITIES_UPDATE": {"description": "Autoriser la mise à jour de la liste des entités", "name": "Permission de mise à jour de la liste des entités"}, "ROLE_PERMISSION_ADMIN_ENTITIES_VIEW": {"description": "Autoriser la visualisation de la liste des entités", "name": "Permission de visualiser la liste des entités"}, "ROLE_PERMISSION_ADMIN_EXPORT_PROFILE": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "dependency": "Les droits d'accès aux écrans d'administration des profils d'export induisent obligatoirement le droit d'accès à la visualisation des profils d'export.", "description": "Autoriser l'accès à l'administration des profils d'export", "name": "Accèder à l'administration des profils d'export"}, "ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_CREATE": {"description": "Autoriser la création de profil d'export", "name": "Permission de créer des profils d'export"}, "ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_DELETE": {"description": "Autoriser la suppression des profils d'export", "name": "Permission de supprimer des profils d'export"}, "ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_UPDATE": {"description": "Autoriser la mise à jour des profils d'export", "name": "Permission de mise à jour des profils d'export"}, "ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_VIEW": {"description": "Autoriser la visualisation des écrans des profils d'export", "name": "Permission de visualiser les écrans des profils d'export"}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "dependency": "Les droits d'accès aux écrans d'administration des fonctions opérationelles induisent obligatoirement le droit d'accès à la visualisation des fonctions opérationnelles.", "description": "Autoriser l'accès à l'administration des fonctions opérationnelles", "name": "Accèder à l'administration des fonctions opérationnelles"}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_CLOSE": {"description": "Autoriser la clôture des fonctions opérationnelles", "name": "Permission de clôturer des fonctions opérationnelles"}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_CREATE": {"description": "Autoriser la création de fonctions opérationnelles", "name": "Permission de créer des fonctions opérationnelles"}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_DELETE": {"description": "Autoriser la suppression des fonctions opérationnelles", "name": "Permission de supprimer des fonctions opérationnelles"}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_UPDATE": {"description": "Autoriser la mise à jour des fonctions opérationnelles", "name": "Permission de mise à jour des fonctions opérationnelles"}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_VALIDATE": {"description": "Autoriser la validation des fonctions opérationnelles", "name": "Permission de valider des fonctions opérationnelles"}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_VIEW": {"description": "Autoriser la visualisation des écrans des fonctions opérationnelles", "name": "Permission de visualiser les écrans des fonctions opérationnelles"}, "ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "dependency": "Les droits d'accès aux écrans d'administration des messages généraux induisent obligatoirement le droit d'accès à la visualisation des messages généraux.", "description": "Autoriser l'accès à l'administration des messages généraux", "name": "Accèder à l'administration des messages généraux"}, "ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_CREATE": {"description": "Autoriser la création de messages généraux", "name": "Permission de créer des messages généraux"}, "ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_DELETE": {"description": "Autoriser la suppression des messages généraux", "name": "Permission de supprimer des messages généraux"}, "ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_UPDATE": {"description": "Autoriser la mise à jour des messages généraux", "name": "Permission de mise à jour des messages généraux"}, "ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_VIEW": {"description": "Autoriser la visualisation des écrans des messages généraux", "name": "Permission de visualiser les écrans des messages généraux"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "dependency": "Les droits d'accès aux écrans admin de modèle et plan de service induisent obligatoirement le droit d'accès aux versions des modèles et plans de service.", "description": "Autoriser l'accès au menu de gestion des modèles de plan de service", "name": "Accèder aux écrans administratifs de gestion des modèles de plan de service"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_CLOSURE": {"description": "Autoriser la clôture des modèles de plan de service", "name": "Permission de clôturer des modèles de plan de service"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_CREATE": {"dependency": "Les permissions de modification, clôture et suppression sont accordées indépendamment.", "description": "Autoriser la création des modèles de plan de service", "name": "Permission de créer des modèles de plan de service"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_DELETE": {"description": "Autoriser la suppression des modèles de plan de service", "name": "Permission de supprimer des modèles de plan de service"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_OPY": {"description": "Autoriser la copie des modèles de plan de service", "name": "Permission de copier des modèles de plan de service"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_UPDATE": {"description": "Autoriser la mise à jour des modèles de plan de service", "name": "Permission de mise à jour des modèles de plan de service"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_UPDATE_BACKUP": {"description": "Autoriser la mise à jour des statuts du groupe de backup pour les modèles de plan de service", "name": "Permission de mise à jour des statuts du groupe de backup pour les modèles de plan de service"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "description": "Autoriser l'accès au menu des versions de modèle de plan de service", "name": "Accéder au menu des versions de modèle de plan de service"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_CLOSURE": {"description": "Autoriser la clôture des versions de modèle de plan de service", "name": "Permission de clôture des versions de modèle de plan de service"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_COPY": {"dependency": "Une personne qui possède le droit de copie des modèles, doit avoir par défaut la permission de modifier les plans de service. Les permissions pour création, modification, clotûre et suppression sont accordées indépendamment.", "description": "Autoriser la copie des versions de modèle de plan de service", "name": "Permission de copie des versions de modèle de plan de service"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_CREATE": {"description": "Autoriser la création des versions de modèle de plan de service", "name": "Permission de création des versions de modèle de plan de service"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_DELETE": {"description": "Autoriser la suppression des versions de modèle de plan de service", "name": "Permission de suppression des versions de modèle de plan de service"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_UPDATE": {"dependency": "Une personne qui possède le droit de modification des modèles, doit avoir par défaut la permission de modifier les plans de service. Les permissions pour création, modification, clotûre et suppression sont accordées indépendamment.", "description": "Autoriser la mise à jour des versions de modèle de plan de service", "name": "Permission de mise à jour des versions de modèle de plan de service"}, "ROLE_PERMISSION_ADMIN_PDS": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "dependency": "Les droits d'accès aux écrans admin de modèle et plan de service induisent obligatoirement le droit d'accès aux versions des modèles et plans de service.", "description": "Autoriser l'accès au menu de gestion des plans de service", "name": "Accèder aux écrans administratifs de gestion de plan de service"}, "ROLE_PERMISSION_ADMIN_PDS_CLOSURE": {"description": "Autoriser la clôture d'un plan de service", "name": "Permission de clôture un plan de service"}, "ROLE_PERMISSION_ADMIN_PDS_CREATE": {"dependency": "Les permissions de modification, clôture et suppression sont accordées indépendamment.", "description": "Autoriser la création d'un plan de service", "name": "Permission de créer un plan de service"}, "ROLE_PERMISSION_ADMIN_PDS_DELETE": {"description": "Autoriser la suppression d'un plan de service", "name": "Permission de supprimer un plan de service"}, "ROLE_PERMISSION_ADMIN_PDS_ELS_MANUAL_SYNCHRO": {"description": "Autoriser la synchronisation manuelle d'un plan de service", "name": "Permission de synchroniser manuellement un plan de service"}, "ROLE_PERMISSION_ADMIN_PDS_TEAM": {"description": "Autoriser l'accès au menu des équipes de plan de service", "name": "Accéder au menu des équipes de plan de service"}, "ROLE_PERMISSION_ADMIN_PDS_TEAM_CREATE": {"description": "Autoriser la création des équipes de plan de service", "name": "Permission de création des équipes de plan de service"}, "ROLE_PERMISSION_ADMIN_PDS_TEAM_DELETE": {"description": "Autoriser la suppression des équipes de plan de service", "name": "Permission de suppression des équipes de plan de service"}, "ROLE_PERMISSION_ADMIN_PDS_TEAM_UPDATE": {"description": "Autoriser la mise à jour des équipes de plan de service", "name": "Permission de mise à jour des équipes de plan de service"}, "ROLE_PERMISSION_ADMIN_PDS_TEAM_VIEW": {"description": "Autoriser la consulter des équipes de plan de service", "name": "Permission de consulter des équipes de plan de service"}, "ROLE_PERMISSION_ADMIN_PDS_UPDATE": {"description": "Autoriser la mise à jour d'un plan de service", "name": "Permission de mise à jour d'un plan de service"}, "ROLE_PERMISSION_ADMIN_PDS_UPDATE_NATIONAL": {"description": "Autoriser la mise à jour d'un plan de service en entier", "name": "Permission de mise à jour d'un plan de service (Niveau National)"}, "ROLE_PERMISSION_ADMIN_PDS_UPDATE_ZONAL": {"description": "Autoriser la mise à jour d'une partie du plan de service", "name": "Permission de mise à jour d'un plan de service (Niveau Zonal)"}, "ROLE_PERMISSION_ADMIN_PDS_VEHICLE_UPDATE": {"description": "Autoriser la mise à jour d'un véhicule d'un plan de service", "name": "Permission de mise à jour du véhicule d'un plan de service"}, "ROLE_PERMISSION_ADMIN_PDS_VERSION": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "description": "Autoriser l'accès au menu des versions de plan de service", "name": "Accéder au menu des versions de plan de service"}, "ROLE_PERMISSION_ADMIN_PDS_VERSION_CLOSURE": {"description": "Autoriser la clôture des versions de plan de service", "name": "Permission de clôture des versions de plan de service"}, "ROLE_PERMISSION_ADMIN_PDS_VERSION_COPY": {"deletion-dependency": "Une personne qui possède la permission de copier les versions de plans de service possède obligatoirement les permissions de modification, création et colture des versions de plans de service", "description": "Autoriser la copie des versions de plan de service", "name": "Permission de copie des versions de plan de service"}, "ROLE_PERMISSION_ADMIN_PDS_VERSION_CREATE": {"description": "Autoriser la création des versions de plan de service", "name": "Permission de création des versions de plan de service"}, "ROLE_PERMISSION_ADMIN_PDS_VERSION_DELETE": {"description": "Autoriser la suppression des versions de plan de service", "name": "Permission de suppression des versions de plan de service"}, "ROLE_PERMISSION_ADMIN_PDS_VERSION_UPDATE": {"description": "Autoriser la mise à jour des versions de plan de service", "name": "Permission de mise à jour des versions de plan de service"}, "ROLE_PERMISSION_ADMIN_PDS_VERSION_VIEW": {"description": "Autoriser la visualisation des versions de plan de service", "name": "Permission de visualiser des versions de plan de service"}, "ROLE_PERMISSION_ADMIN_PDS_VIEW": {"description": "Autoriser la visualisation des écrans de gestion des plans de service", "name": "Permission de visualiser les écrans de plan de service"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "dependency": "Les droits d'accès aux écrans admin de template de positions induisent obligatoirement le droit d'accès aux versions des templates de positions.", "description": "Autoriser l'accès au menu de gestion des templates de position", "name": "Accèder aux écrans administratifs de gestion des templates de position"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_CLOSURE": {"description": "Autoriser la clôture des templates de position", "name": "Permission de clôturer des templates de position"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_CREATE": {"dependency": "Les permissions de modification, clôture et suppression sont accordées indépendamment.", "description": "Autoriser la création des templates de position", "name": "Permission de créer des templates de position"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_DELETE": {"description": "Autoriser la suppression des templates de position", "name": "Permission de supprimer des templates de position"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_OPY": {"description": "Autoriser la copie des templates de position", "name": "Permission de copier des templates de position"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_UPDATE": {"description": "Autoriser la mise à jour des templates de position", "name": "Permission de mise à jour des templates de position"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_UPDATE_BACKUP": {"description": "Autoriser la mise à jour des statuts du groupe de backup pour les templates de position", "name": "Permission de mise à jour des statuts du groupe de backup pour les templates de position"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "description": "Autoriser l'accès au menu des versions de template de position", "name": "Accéder au menu des versions de template de position"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_CLOSURE": {"description": "Autoriser la clôture des versions de template de position", "name": "Permission de clôture des versions de template de position"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_COPY": {"dependency": "Une personne qui possède le droit de copie des modèles, doit avoir par défaut la permission de modifier les plans de service. Les permissions pour création, modification, clotûre et suppression sont accordées indépendamment.", "description": "Autoriser la copie des versions de template de position", "name": "Permission de copie des versions de template de position"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_CREATE": {"description": "Autoriser la création des versions de template de position", "name": "Permission de création des versions de template de position"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_DELETE": {"description": "Autoriser la suppression des versions de template de position", "name": "Permission de suppression des versions de template de position"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_UPDATE": {"dependency": "Une personne qui possède le droit de modification des modèles, doit avoir par défaut la permission de modifier les plans de service. Les permissions pour création, modification, clotûre et suppression sont accordées indépendamment.", "description": "Autoriser la mise à jour des versions de template de position", "name": "Permission de mise à jour des versions de template de position"}, "ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "dependency": "Les droits d'accès aux écrans d'administration des jours fériés induisent obligatoirement le droit d'accès à la visualisation des jours fériés.", "description": "Autoriser l'accès à l'administration des jours fériés", "name": "Accèder à l'administration des jours fériés"}, "ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_CREATE": {"description": "Autoriser la création de jours fériés", "name": "Permission de créer des jours fériés"}, "ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_DELETE": {"description": "Autoriser la suppression des jours fériés", "name": "Permission de supprimer des jours fériés"}, "ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_UPDATE": {"description": "Autoriser la mise à jour des jours fériés", "name": "Permission de mise à jour des jours fériés"}, "ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_VIEW": {"description": "Autoriser la visualisation des écrans des jours fériés", "name": "Permission de visualiser les écrans des jours fériés"}, "ROLE_PERMISSION_ADMIN_SCHEDULER": {"description": "Voir la liste des informations relative aux schedulers", "name": "Permission de voir la liste des informations relative aux schedulers"}, "ROLE_PERMISSION_ADMIN_SCHEDULER_ELS": {"description": "Voir la liste des informations relative aux schedulers ELS", "name": "Permission de voir la liste des informations relative aux schedulers ELS"}, "ROLE_PERMISSION_ADMIN_SCHEDULER_BACKUP112": {"description": "Voir la liste des informations relative aux schedulers Export Backup 112", "name": "Permission de voir la liste des informations relative aux schedulers Export Backup 112"}, "ROLE_PERMISSION_ADMIN_SCHEDULER_EXPORTCSVCIS": {"description": "Voir la liste des informations relative aux schedulers Export CSV de tous les CIS", "name": "Permission de voir la liste des informations relative aux schedulers Export CSV de tous les CIS"}, "ROLE_PERMISSION_ADMIN_SCHEDULER_INTERNAL": {"description": "Voir la liste des informations relative aux schedulers Internes", "name": "Permission de voir la liste des informations relative aux schedulers Internes"}, "ROLE_PERMISSION_ADMIN_SCHEDULER_ASSIGNMENT": {"description": "Voir la liste des informations relative aux schedulers de mise à jour des affectations", "name": "Permission de voir la liste des informations relative aux schedulers de mise à jour des affectations"}, "ROLE_PERMISSION_ADMIN_SCHEDULER_ALL": {"description": "Voir la liste des informations relative à tous les schedulers", "name": "Permission de voir la liste des informations relative à tous les schedulers"}, "ROLE_PERMISSION_ADMIN_VEHICLES": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "dependency": "Accéder au menu des véhicules induit obligatoirement la permission de visualiser la liste des véhicules.", "description": "Autoriser l'accès au menu des véhicules", "name": "Accèder à la liste des véhicules"}, "ROLE_PERMISSION_ADMIN_VEHICLES_ID_VIEW": {"description": "Autoriser la visualisation des identifiants des véhicules", "name": "Permission de visualiser les identifiants des véhicules"}, "ROLE_PERMISSION_ADMIN_VEHICLES_STATUS_VIEW": {"description": "Autoriser la visualisation de l'historique des statuts d'un véhicule", "name": "Permission de visualiser l'historique des statuts d'un véhicule"}, "ROLE_PERMISSION_ADMIN_VEHICLES_VIEW": {"description": "Autoriser la visualisation de la liste des véhicules", "name": "Permission de visualiser la liste des véhicules"}, "ROLE_PERMISSION_ADMIN_VEHICLES_VIEW_ALL": {"description": "Autoriser la visualisation de la liste des véhicules sans restriction", "name": "Permission de visualiser la liste des véhicules sans restriction"}, "ROLE_PERMISSION_ALLOWANCE": {"dependency": "Accès à l'ecran de gestion des indemnités", "description": "Autoriser l'accès à l'écran de gestion des indemnités", "name": "Permission d'accéder au management des indemnités"}, "ROLE_PERMISSION_ALLOWANCE_AUDIT": {"description": "Autoriser la visualization de l'audit des indemnités", "name": "Permission de visualiser l'audit des indemnités"}, "ROLE_PERMISSION_ALLOWANCE_CONFIGURATION": {"dependency": "Accès à l'ecran de gestion de la configuration d'indemnités", "description": "Autoriser l'accès à l'écran de gestion de la configuration d'indemnités", "name": "Permission d'accéder au management de la configuration d'indemnités"}, "ROLE_PERMISSION_ALLOWANCE_CONFIGURATION_CREATE": {"description": "Autoriser la création des configurations d'indemnités", "name": "Permission de créer des configurations d'indemnités"}, "ROLE_PERMISSION_ALLOWANCE_CONFIGURATION_DELETE": {"description": "Autoriser la suppression des configurations d'indemnités", "name": "Permission de supprimer des configurations d'indemnités"}, "ROLE_PERMISSION_ALLOWANCE_CONFIGURATION_UPDATE": {"description": "Autoriser la mise à jour des configurations d'indemnités", "name": "Permission de mise à jour des configurations d'indemnités"}, "ROLE_PERMISSION_ALLOWANCE_CONFIGURATION_VIEW": {"description": "Autoriser la visualisation de la liste des configurations d'indemnités", "name": "Permission de visualiser la liste des configurations d'indemnités"}, "ROLE_PERMISSION_ALLOWANCE_SEARCH": {"description": "Autoriser la recherche d'indemnités", "name": "Permission de chercher des indemnités"}, "ROLE_PERMISSION_ALLOWANCE_VIEW": {"description": "Autoriser la visualisation de la liste des indemnités", "name": "Permission de visualiser la liste des indemnités"}, "ROLE_PERMISSION_CURRENT_SITUATION": {"description": "Autoriser la visualisation de la situation courante", "name": "Permission de visualiser la situation courante"}, "ROLE_PERMISSION_DASHBOARD_MANAGER": {"description": "Autoriser l'accès au dashboard responsable", "name": "Accèder au dashboard Responsable"}, "ROLE_PERMISSION_DASHBOARD_MEMBER": {"description": "Autoriser l'accès au dashboard membre", "name": "Accèder au dashboard Membre"}, "ROLE_PERMISSION_DASHBOARD_MEMBER_WORKING_PLAN": {"description": "Autoriser l'accès au menu des plans de travail", "name": "Accèder à l'écran plan de travail"}, "ROLE_PERMISSION_GENERATE_API_DESCRIPTION": {"description": "Permettre la génération de la description d'API", "name": "Permission de générer la description d'API"}, "ROLE_PERMISSION_GENERATE_PRESTATIONS_ALLOWANCES": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "description": "Autoriser l'accès à la génération des indemnités", "name": "Accéder à la génération des indemnités"}, "ROLE_PERMISSION_GLOBAL_ACCESS_LOGAS": {"description": "Autoriser l'accès au logas", "name": "Permission d'accéder au logas"}, "ROLE_PERMISSION_GLOBAL_EXPORT_PRESTATIONS": {"description": "Autoriser l'export semestriel des prestations pour l'ensemble des personnes CGDIS", "name": "Permettre l'export semestriel des prestations pour l'ensemble des personnes CGDIS"}, "ROLE_PERMISSION_IMPERSONATED": {"description": "Autoriser le logas", "name": "Permission de logas"}, "ROLE_PERMISSION_MY_PROFILE_ACTIVITY_EXPORT_PRESTATIONS": {"description": "Autoriser l'export des prestations de la personne connecté dans l'écran mes activités", "name": "Autoriser l'export des prestations de la personne connectée"}, "ROLE_PERMISSION_MY_PROFILE_ACTIVITY_EXPORT_PRESTATIONS_PENALTY": {"description": "Autoriser l'export des astreintes de la personne connecté dans l'écran mes activités", "name": "Autoriser l'export des astreintes de la personne connectée"}, "ROLE_PERMISSION_MY_PROFILE_ACTIVITY_VIEW": {"description": "Autoriser l'accès à l'onglet activité du profil de l'utilisateur connecté", "name": "Accèder au profil - mes activités"}, "ROLE_PERMISSION_MY_PROFILE_GENERAL_CONTACT_UPDATE": {"description": "Autoriser la mise à jour des coordonnées administratives et opérationnelles de la personne connectée.", "name": "Permission de mettre à jour les informations de contact général du profil"}, "ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW": {"description": "Autoriser l'accès à l'onglet général du profil de l'utilisateur connecté", "name": "Accèder au profil - onglet général"}, "ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW_DIPLOMAS": {"description": "Autoriser l'accès aux diplômes du profil de l'utilisateur connecté", "name": "Accèder au profil - diplômes"}, "ROLE_PERMISSION_MY_PROFILE_MEDICAL_VIEW": {"description": "Autoriser l'accès à l'onglet médical du profil de l'utilisateur connecté", "name": "Accèder au profil - onglet médical"}, "ROLE_PERMISSION_MY_PROFILE_MOBILE_GENERATE_QRCODE": {"description": "Autoriser la génération de QR code", "name": "Permission de générer un QR code"}, "ROLE_PERMISSION_MY_PROFILE_OPERATIONAL_VIEW": {"description": "Autoriser l'accès à l'onglet opérationnel du profil de l'utilisateur connecté", "name": "Accèder au profil - onglet opérationnel"}, "ROLE_PERMISSION_MY_PROFILE_PREFERENCES_UPDATE": {"description": "Autoriser la mise à jour les préférences de la personne connectée", "name": "Permission de mettre à jour les préférences"}, "ROLE_PERMISSION_MY_PROFILE_PREFERENCES_VIEW": {"dependency": "Accéder à l'écran des préférences induit obligatoirement la permission de modifier les préférences.", "description": "Autoriser l'accès aux préférences de l'utilisateur connecté", "name": "Accèder aux préférences"}, "ROLE_PERMISSION_MY_PROFILE_VIEW": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "dependency": "Accéder au menu profil induit obligatoirement la permission de modifier les informations pouvant l'être.", "description": "Autoriser l'accès au profil de l'utilisateur connecté", "name": "Accèder au profil"}, "ROLE_PERMISSION_MY_PROFILE_VIEW_FUNCTION_OPERATIONAL": {"description": "Autoriser l'accès aux fonctions opérationnelles du profil de l'utilisateur connecté", "name": "Accèder au profil - fonctions opérationnelles"}, "ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT": {"dependency": "Accès à l'ecran de gestion des groupes d'alerte", "description": "Autoriser l'accès à l'écran de gestion des groupes d'alerte", "name": "Permission d'accéder au management des groupes d'alerte"}, "ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT_VIEW": {"dependency": "Voir la liste des groupes d'alerte", "description": "Autoriser la visualisation de la liste des groupes d'alerte", "name": "Permission la visualisation de la liste des groupes d'alerte"}, "ROLE_PERMISSION_PAGER_UPDATE": {"ROLE_PERMISSION_GLOBAL_ACCESS_LOGAS": {"description": "", "name": ""}, "ROLE_PERMISSION_IMPERSONATED": {"description": "", "name": ""}, "ROLE_PERMISSION_MY_PROFILE_MOBILE_GENERATE_QRCODE": {"description": "", "name": ""}, "ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT_VIEW": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_ADD_PERSON": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_ADD_PERSON_FULL_AVAILABILITY": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_ADD_PERSON_VIEW_UNAFFECTED": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_ADD_TEAM": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_DELETE_PRESTATION": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_EXPORT": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_EXPORT_PDF": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_SEND_FILLING": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_SPLIT_TIMESLOT": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_VIEW_ALL_AVAILABILITIES": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_LIST": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_PDS_LIST_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_ERROR_MANAGEMENT": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT": {"deletion-dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS_PENALTY": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_EXPORT": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW_DIPLOMAS": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_MEDICAL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_OPERATIONAL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE_SEND_ALARM_CLOCK": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_FUNCTION_OPERATIONAL": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ROLE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_YOUNG_ALL": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_YOUNG_FIREFIGHTER": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MEDICAL_INFORMATION_LIST": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_RIGHTS": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_RIGHTS_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_RIGHTS_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_RIGHTS_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_COPY": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_CREATE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_COPY": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_CREATE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_VIEW": {"description": "", "name": ""}, "column": "", "dependency": "", "description": "Autoriser la modification du GSM Pager (RW)", "entity": "", "name": "Autoriser la modification du GSM Pager (RW)", "no-permissions": "", "no-search-result": "", "search-permission": "", "select-permission": ""}, "ROLE_PERMISSION_PAGER_VIEW": {"description": "Permettre l'affichage du GSM Pager (RO)", "name": "Permettre l'affichage du GSM Pager (RO)"}, "ROLE_PERMISSION_PDS_FILL_": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "dependency": "Accéder au menu du détail des plans de service induit obligatoirement la permission de visualiser les détails des plans de service. La permission pour modification est accordée indépendamment.", "description": "Autoriser l'accès au détail opérationnel d'un plan de service.", "name": "Accèder au détail opérationnel des plans de service"}, "ROLE_PERMISSION_PDS_FILL_ADD_PERSON": {"description": "Autoriser la modification d'un plan de service. Ajout ou remplacement d'une personne sur un plan de service.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission d'ajouter une personne sur un plan de service"}, "ROLE_PERMISSION_PDS_FILL_ADD_PERSON_FULL_AVAILABILITY": {"description": "Autoriser la modification d'un plan de service. Ajout ou remplacement d'une personne sur un plan de service pour la totalité de la disponibilité de la personne.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission d'ajouter une personne sur un plan de service sur la totalité de la disponibilité"}, "ROLE_PERMISSION_PDS_FILL_ADD_PERSON_VIEW_UNAFFECTED": {"description": "Autoriser la vue du tab contenant les personnes sans affectation dans la popup de remplissage des plans de service", "name": "Permission de voir le tab des personnes sans affectation"}, "ROLE_PERMISSION_PDS_FILL_ADD_TEAM": {"description": "Autoriser l'ajout d'une équipe complète sur une tranche horaire d'un plan de service", "name": "Permission d'ajouter une équipe complète sur une tranche horaire d'un plan de service"}, "ROLE_PERMISSION_PDS_FILL_DELETE_PRESTATION": {"description": "Autoriser la clôture d'une prestation sur une position d'un plan de service.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission de clôturer une prestation sur un plan de service"}, "ROLE_PERMISSION_PDS_FILL_EXPORT": {"description": "Autoriser l'export les prestations d'un plan de service au format CSV", "name": "Permission d'exporter les prestations d'un plan de service au format CSV"}, "ROLE_PERMISSION_PDS_FILL_EXPORT_PDF": {"description": "Autoriser l'export des prestations de la semaine d'un plan de service au format PDF", "name": "Permission d'exporter les prestations de la semaine d'un plan de service au format PDF"}, "ROLE_PERMISSION_PDS_FILL_SEND_FILLING": {"description": "Autoriser l'export les prestations d'un plan de service par mail", "name": "Permission d'exporter les prestations d'un plan de service par mail"}, "ROLE_PERMISSION_PDS_FILL_SPLIT_TIMESLOT": {"description": "Autoriser le split d'un tranche horaire sur un plan de service.</br>", "name": "Permission de spliter une tranche horaire d'un plan de service"}, "ROLE_PERMISSION_PDS_FILL_VIEW": {"description": "Autoriser la visualisation le remplissage d'un plan de service.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission de visualiser le remplissage d'un plan de service"}, "ROLE_PERMISSION_PDS_FILL_VIEW_ALL_AVAILABILITIES": {"description": "Autoriser la visualisation de toutes les disponibilités pour un plan de service", "name": "Permission de visualiser toutes les disponibilités pour un plan de service"}, "ROLE_PERMISSION_PDS_LIST": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "dependency": "Accéder au menu de la liste des plans de service induit obligatoirement la permission de visualiser la liste des plans de service. La permission pour modification est accordée indépendamment.", "description": "Autoriser l'accès à la liste des plans de service.", "name": "Accèder au menu opérationnel des plans de service"}, "ROLE_PERMISSION_PDS_LIST_VIEW": {"description": "Autoriser la visualisation de la liste des plans de service.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission de visualiser la liste opérationnelle des plans de service"}, "ROLE_PERMISSION_PDS_STATE_VIEW": {"description": "Autoriser la visualisation de l'état d'un plan de service (groupes optionnel/backup indisponibles par exemple)<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission de visualiser l'état d'un plan de service (groupes optionnel/backup indisponible par exemple)"}, "ROLE_PERMISSION_PREFERENCES_UPDATE_VEHICLE": {"description": "Permettre le choix de preferences dans la visulation des vehicules en statuts 6 ou 6c", "name": "Acces preferences statuts véhicules 6 ou 6c"}, "ROLE_PERMISSION_PROFILE_TIC_LOGAS_VIEW": {"description": "Autoriser la visualisation du TIC d'une personne dans la liste des membres d'une entité<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission de visualiser le TIC sur le profil d'une personne dans la liste des membres d'une entité"}, "ROLE_PERMISSION_PROFILE_TIC_VIEW": {"description": "Autoriser la visualisation du TIC sur mon profil", "name": "Permission de visualiser le TIC sur mon profil"}, "ROLE_PERMISSION_ERROR_MANAGEMENT": {"description": "Autoriser l'accès au menu de gestion des erreurs", "name": "Accèder aux écrans de gestion des erreurs"}, "ROLE_PERMISSION_ERROR_MANAGEMENT_PAGER": {"description": "Autoriser l'accès à la gestion des erreurs Pager", "name": "Accèder à la gestion des erreurs Pager"}, "ROLE_PERMISSION_ERROR_MANAGEMENT_ELS": {"description": "Autoriser l'accès à la gestion des erreurs ELS", "name": "Accèder à la gestion des erreurs ELS"}, "ROLE_PERMISSION_ERROR_MANAGEMENT_FO": {"description": "Autoriser l'accès à la gestion des erreurs des", "name": "Accèder à la gestion des erreurs des FO"}, "ROLE_PERMISSION_ERROR_MANAGEMENT_INTERNAL": {"description": "Autoriser l'accès à la gestion des erreurs Interne", "name": "Accèder à la gestion des erreurs Interne"}, "ROLE_PERMISSION_ERROR_MANAGEMENT_LEVESO": {"description": "Autoriser l'accès à la gestion des erreurs Leveso", "name": "Accèder à la gestion des erreurs Leveso"}, "ROLE_PERMISSION_ERROR_MANAGEMENT_GEPS": {"description": "Autoriser l'accès à la gestion des erreurs GEPS", "name": "Accèder à la gestion des erreurs GEPS"}, "ROLE_PERMISSION_ERROR_MANAGEMENT_MSNAV": {"description": "Autoriser l'accès à la gestion des erreurs MSNAV", "name": "Accèder à la gestion des erreurs MSNAV"}, "ROLE_PERMISSION_ERROR_MANAGEMENT_LUXDOK": {"description": "Autoriser l'accès à la gestion des erreurs LUXDOK", "name": "Accèder à la gestion des erreurs LUXDOK"}, "ROLE_PERMISSION_ERROR_MANAGEMENT_EXPORT": {"description": "Autoriser l'accès à la gestion des erreurs Exports", "name": "Accèder à la gestion des erreurs Exports"}, "ROLE_PERMISSION_USER_MANAGEMENT": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "description": "Autoriser l'accès au menu de la gestion des utilisateurs du portail", "name": "Accèder au menu de la gestion utilisateur"}, "ROLE_PERMISSION_SYSTEM_MANAGEMENT": {"description": "Autoriser l'accès au menu de la gestion systèmes", "name": "Accèder au menu système"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "dependency": "Accéder au menu de gestion des personnes induit obligatoirement la permission de visualiser les informations des personnes. La permission pour modification est accordée indépendamment.", "description": "Autoriser l'accès au menu de la gestion des personnes", "name": "Accèder au menu de la gestion des personnes"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS": {"description": "Autoriser l'export des prestations des personnes.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Autoriser l'export des prestations des personnes."}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS_PENALTY": {"description": "Autoriser l'export des astreintes des personnes.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Autoriser l'export des astreintes des personnes."}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_VIEW": {"description": "Autoriser la visualisation  des activités des personnes.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission de visualiser l'onglet mes activités des informations des personnes"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_EXPORT": {"description": "Autoriser l'export au format CSV de la liste des personnes et de leurs informations personnelles.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission d'exporter au format CSV les informations des personnes"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW": {"description": "Autoriser la visualisation des informations générales des personnes.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission de visualiser l'onglet général des informations des personnes"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW_DIPLOMAS": {"description": "Autoriser la visualisation des diplômes des personnes.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission de visualiser les diplômes des personnes"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_MEDICAL_VIEW": {"description": "Autoriser la visualisation des informations médicales des personnes.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission de visualiser l'onglet médical des informations des personnes"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_OPERATIONAL_VIEW": {"description": "Autoriser la visualisation  des informations opérationnelles des personnes.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission de visualiser l'onglet opérationnel des informations des personnes"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE": {"description": "Autoriser la mise à jour des informations générales des personnes de son entité.", "name": "Permission de mise à jour des informations générales des personnes"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE_SEND_ALARM_CLOCK": {"dependency": "Mettre à jour le paramètre 'Alarmdepesch' entraîne obligatoirement le droit à la mise à jour des informations personnelles des personnes", "description": "Autoriser la mise à jour du parametre 'Alarmdepesch' des informations personnelles des personnes de son entité.", "name": "Permission de mise à jour du parametre 'Alarmdepesch' des personnes"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW": {"description": "Autoriser la visualisation de la liste des personnes et de leurs informations personnelles.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission de visualiser les informations des personnes"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ALL": {"description": "Visualiser uniquement toutes les personnes et de leurs informations personnelles.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission pour visualiser toutes les personnes dans la liste des membres"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_FUNCTION_OPERATIONAL": {"dependency": "Consulter les functions operationnelles entraîne obligatoirement le droit à la mise à jour des informations personnelles des personnes", "description": "Autoriser la vue des fonctions operationnelles des informations personnelles des personnes de son entité.", "name": "Permission d'acceder aux fonctions operationnelles des personnes"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ROLE": {"description": "Visualiser uniquement les roles applicatifs d'une personne.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission pour visualiser uniquement les roles applicatifs d'une personne"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_YOUNG_ALL": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_YOUNG_FIREFIGHTER": {"description": "Visualiser uniquement la liste des jeunes pompiers et de leurs informations personnelles.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission pour visualiser uniquement les jeunes pompiers dans la liste des membres"}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "dependency": "Accéder au menu des fonctions opérationnelles induit obligatoirement la permission de visualiser la liste des fonctions opérationnelles. La permission pour modification est accordée indépendamment.", "description": "Autoriser l'accès au menu des fonctions opérationnelles", "name": "Accèder au menu de la gestion des fonctions opérationnelles"}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "dependency": "Accéder au menu du mapping des fonctions opérationnelles induit obligatoirement la permission de visualiser l'écran de mapping des fonctions opérationnelles. La permission pour modification est accordée indépendamment.", "description": "Autoriser l'accès au menu du mapping des fonctions opérationnelles", "name": "Accèder au menu de la gestion du mapping des fonctions opérationnelles"}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_UPDATE": {"description": "Autoriser la mise à jour du mapping des fonctions opérationnelles", "name": "Permission de mise à jour du mapping des fonctions opérationnelles"}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_VIEW": {"description": "Autoriser la visualisation du mapping des fonctions opérationnelles", "name": "Permission de visualiser le mapping des fonctions opérationnelles"}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_UPDATE": {"description": "Autoriser la mise à jour des fonctions opérationnelles", "name": "Permission de mise à jour des fonctions opérationnelles"}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW": {"description": "Autoriser la visualisation des fonctions opérationnelles.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission de visualiser la liste des fonctions opérationnelles"}, "ROLE_PERMISSION_USER_MEDICAL_INFORMATION_LIST": {"description": "Autoriser la visualisation des aptitudes et des restrictions de toutes les personnes.", "name": "Permission de visualiser les informations médicales de toutes les personnes"}, "ROLE_PERMISSION_USER_RIGHTS": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "dependency": "Accéder au menu des droits utilisateurs induit obligatoirement la permission de visualiser la liste des droits utilisateurs.", "description": "Autoriser l'accès au menu des droits utilisateur", "name": "Accèder au menu des droits utilisateur"}, "ROLE_PERMISSION_USER_RIGHTS_DELETE": {"description": "Autoriser la suppression des droits utilisateur", "name": "Permission de suppression des droits utilisateur"}, "ROLE_PERMISSION_USER_RIGHTS_UPDATE": {"description": "Autoriser la mise à jour des droits utilisateur", "name": "Permission de mise à jour des droits utilisateur"}, "ROLE_PERMISSION_USER_RIGHTS_VIEW": {"description": "Autoriser la visualisation des droits utilisateur", "name": "Permission de visualiser la liste des droits utilisateur"}, "ROLE_PERMISSION_VEHICLE_UPDATE_STATUS_6_VIEW": {"description": "Autoriser l'accès au notification des modifications de statuts de véhicules vers les statuts 6 ou 6c", "name": "Permission de visualiser les notifications des véhicules en statuts 6 ou 6c"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "dependency": "Accéder au menu dashboard membre induit obligatoirement la permission de visualiser le dashboard membre. Les permissions pour création, modification, clôture et suppression sont accordées indépendamment.", "description": "Autoriser l'accès au dashboard membre CGDIS", "name": "Accèder au dashboard membre CGDIS"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_COPY": {"description": "Autoriser la copie d'une disponibilité hebdomadaire ou journalière", "name": "Permission de copier une disponibilité"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_CREATE": {"description": "Autoriser la création d'une disponibilité.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission de création d'une disponibilité"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_DELETE": {"description": "Autoriser la suppression d'une disponibilité", "name": "Permission de suppression d'une disponibilité"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS": {"deletion-dependency": "La suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "dependency": "Accéder au menu de gestion des disponibilités des volontaires induit obligatoirement la permission de visualiser les disponibilités. Les permissions pour création, modification et suppression sont accordées indépendamment.", "description": "Accéder au menu de gestion des disponibilités des volontaires", "name": "Accéder à la gestion des disponibilités des volontaires"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_COPY": {"description": "Autoriser la copie de disponibilités pour des volontaires", "name": "Permission de copier des disponibilités pour des volontaires"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_CREATE": {"description": "Autoriser la création de disponibilités pour des volontaires", "name": "Permission de créer des disponibilités pour des volontaires"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_DELETE": {"description": "Autoriser la suppression des disponibilités des volontaires", "name": "Permission de supprimer les disponibilités des volontaires"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_UPDATE": {"description": "Autoriser la mise à jour des disponilibilités des volontaires", "name": "Permission de mettre à jour les disponibilités des volontaires"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_VIEW": {"description": "Accéder à l'écran de gestion des disponibilités des volontaires", "name": "Permission de voir les disponibilités des volontaires"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_UPDATE": {"description": "Autoriser la mise à jour d'une disponibilité", "name": "Permission de mise à jour d'une disponibilité"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_VIEW": {"description": "Autoriser la visualisation du dashboard membre CGDIS<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Permission de visualiser le dashboard membre CGDIS"}, "column": "Permission", "dependency": "cette permission entraîne obligatoirement la permission à d'autres droits et/ou la suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "entity": "Entité", "no-permissions": "Au<PERSON>n r<PERSON><PERSON>", "no-search-result": "Aucun résultat", "role": {"ROLE_ADMIN": {"description": "", "name": ""}, "ROLE_ASTREINTE_AUDIT": {"description": "", "name": ""}, "ROLE_AUDIT": {"description": "", "name": ""}, "ROLE_CENTER_CHIEF": {"description": "", "name": ""}, "ROLE_CHEF_COMPAGNIE": {"description": "", "name": ""}, "ROLE_DCOCGO_ASSISTANT": {"description": "", "name": ""}, "ROLE_DCOCGO_COORDINATOR": {"description": "", "name": ""}, "ROLE_DCOCSU_CHEFDESALLE": {"description": "", "name": ""}, "ROLE_DCO_DATA_ELS": {"description": "", "name": ""}, "ROLE_DCO_VOLUNTEER": {"description": "", "name": ""}, "ROLE_DIRECTOR": {"description": "", "name": ""}, "ROLE_DMS_COORDINATOR_SAMU": {"description": "", "name": ""}, "ROLE_HELPDESK": {"description": "", "name": ""}, "ROLE_INFS_SECRETARIAT": {"description": "", "name": ""}, "ROLE_MANAGEMENT_EXPORT": {"description": "", "name": ""}, "ROLE_MEMBER": {"description": "", "name": ""}, "ROLE_MONITEUR_JEUNES": {"description": "", "name": ""}, "ROLE_PAGER_MANAGEMENT": {"description": "", "name": ""}, "ROLE_PERMANENCE_MANAGEMENT": {"description": "", "name": ""}, "ROLE_PERMANENCE_MANAGEMENT_GLOBAL": {"description": "", "name": ""}, "ROLE_PERMANENCE_MANAGEMENT_LIGHT": {"description": "", "name": ""}, "ROLE_PERSON_AUDIT": {"description": "", "name": ""}, "ROLE_PERSON_AUDIT_GLOBAL": {"description": "", "name": ""}, "ROLE_PERSON_MANAGEMENT": {"description": "", "name": ""}, "ROLE_PERSON_MANAGEMENT_GLOBAL": {"description": "", "name": ""}, "ROLE_VEHICLE_AUDIT": {"description": "", "name": ""}, "ROLE_VEHICLE_MANAGEMENT": {"description": "", "name": ""}, "ROLE_ZONE_CHIEF": {"description": "", "name": ""}, "column": "", "dependency": "", "entity": "", "entityinheritance": "", "no-permissions": "", "no-search-result": "", "search-permission": "", "select-permission": ""}, "search-permission": "Rechercher un rôle accordé ...", "select-permission": "Sélectionner un rôle", "added-permission": "La permission a bien été ajoutée. Cela sera effectif à la prochaine connexion au Portail.", "removed-permission": "La permission a bien été supprimée. Cela sera effectif à la prochaine connexion au Portail."}, "role": {"ROLE_ADMIN": {"description": "Un administrateur peut effectuer <b>toutes</b> les actions administratives et opérationnelles du portail.<br/>Il peut également effectuer des actions d'audit et techniques", "name": "Administrateur"}, "ROLE_ADMIN_OPERATIONAL": {"description": "Un administrateur opérationnel peut effectuer <b>toutes</b> les actions opérationnelles du portail", "name": "Administrateur Opérationnel"}, "ROLE_ASTREINTE_AUDIT": {"description": "Auditeur des astreintes", "name": "Auditeur des astreintes"}, "ROLE_AUDIT": {"description": "Un auditeur possède des <b>accès en consultation</b> uniquement et sur toutes les données opérationnelles du portail", "name": "Auditeur"}, "ROLE_CENTER_CHIEF": {"description": "Un chef de centre accède à des fonctionnalités administratives et opérationnelles de second niveau (Gestion de plan de service, gestion des fonctions opérationnelles).<br />Il peut visualiser la liste des entités, des véhicules et les droits utilisateur du portail.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Chef de centre"}, "ROLE_CHEF_COMPAGNIE": {"description": "Chef de Comp<PERSON>nie", "name": "Chef de Comp<PERSON>nie"}, "ROLE_DAF_COMPTABILITE": {"description": "Description non disponible actuellement", "name": "DAF Comptabilité"}, "ROLE_DCOCGO_ASSISTANT": {"description": "DCO CGO Assistant", "name": "DCO CGO Assistant"}, "ROLE_DCODCO_ADMINISTRATIF": {"description": "DCO DCO Administratif", "name": "DCO DCO Administratif"}, "ROLE_DCOCGO_COORDINATOR": {"description": "DCO CGO Coordinateur", "name": "DCO CGO Coordinateur"}, "ROLE_DMS_OFFICIER_SANTE_CSU": {"description": "DMS CSU Officier <PERSON>", "name": "DMS CSU Officier <PERSON>"}, "ROLE_DCOCSU_CHEFDESALLE": {"description": "DCO CSU Chef de salle", "name": "DCO CSU Chef de salle"}, "ROLE_DCOCSU_CADRE": {"description": "DCO CSU Cadre", "name": "DCO CSU Cadre"}, "ROLE_DCOCSU_REFERENT": {"description": "DCO CSU Référent", "name": "DCO CSU Référent"}, "ROLE_DCOCSU_REGULATEUR": {"description": "DCO CSU Régulateur", "name": "DCO CSU Régulateur"}, "ROLE_DCOCSU_DISPATCHER": {"description": "DCO CSU Dispatcher", "name": "DCO CSU Dispatcher"}, "ROLE_INFS_STAGE_GEST": {"description": "INFS Gestionnaire Stages Pratiques", "name": "INFS Gestionnaire Stages Pratiques"}, "ROLE_DCOCSU_SUPPORT_IT": {"description": "DCO CSU Support IT", "name": "DCO CSU Support IT"}, "ROLE_DCO_DATA_ELS": {"description": "DCO Data ELS", "name": "DCO Data ELS"}, "ROLE_DCO_VOLUNTEER": {"description": "DCO Département Volontariat", "name": "DCO Département Volontariat"}, "ROLE_DIRECTOR": {"description": "Description non disponible actuellement", "name": "Directeur"}, "ROLE_DMS_COORDINATOR_SAMU": {"description": "Description non disponible actuellement", "name": "DMS Coordination SAMU"}, "ROLE_FO_GEST": {"description": "Gestionnaire des fonctions opérationnelles", "name": "Gestionnaire des fonctions opérationnelles"}, "ROLE_FO_GEST_LIGHT": {"description": "Gestionnaire des fonctions opérationnelles light", "name": "Gestionnaire des fonctions opérationnelles light"}, "ROLE_HELPDESK": {"description": "DML TIC Helpdesk", "name": "DML TIC Helpdesk"}, "ROLE_INFS_SECRETARIAT": {"description": "INFS Secretariat", "name": "INFS Secretariat"}, "ROLE_INFS_FORM_SPEC": {"description": "INFS Formation Spécialisée", "name": "INFS Formation Spécialisée"}, "ROLE_MANAGEMENT_EXPORT": {"description": "Description non disponible actuellement", "name": "Gestionnaire des exports"}, "ROLE_MEMBER": {"description": "Un membre peut accéder aux fonctionnalités opérationnelles du portail.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b> ", "name": "Membre"}, "ROLE_MONITEUR_JEUNES": {"description": "Description non disponible actuellement", "name": "Moniteur des jeunes pompiers"}, "ROLE_PAGER_MANAGEMENT": {"description": "DML TIC Gestionnaire des pagers", "name": "DML TIC Gestionnaire des pagers"}, "ROLE_PERMANENCE_MANAGEMENT": {"description": "Un gestionnaire de prestations accède à des fonctionnalités administratives et opérationnelles concernant uniquement les plans de service.<br />Il peut visualiser et effectuer des modifications sur les plans de service.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Gestionnaire de prestations"}, "ROLE_PERMANENCE_MANAGEMENT_GLOBAL": {"description": "Un gestionnaire de prestations accède à des fonctionnalités administratives et opérationnelles concernant uniquement les plans de service.<br />Il peut visualiser et effectuer des modifications sur les plans de service.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Gestionnaire de prestations global"}, "ROLE_PERMANENCE_MANAGEMENT_LIGHT": {"description": "Un gestionnaire de prestations accède à des fonctionnalités administratives et opérationnelles concernant uniquement les plans de service.<br />Il peut visualiser et effectuer des modifications sur les plans de service.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Gestionnaire de prestations Light"}, "ROLE_PERSON_AUDIT": {"description": "Description non disponible actuellement", "name": "Auditeur de personnes"}, "ROLE_PERSON_AUDIT_GLOBAL": {"description": "Description non disponible actuellement", "name": "Auditeur de personnes global"}, "ROLE_PERSON_MANAGEMENT": {"description": "Description non disponible actuellement", "name": "Gestionnaire des personnes"}, "ROLE_PERSON_MANAGEMENT_GLOBAL": {"description": "Description non disponible actuellement", "name": "Gestionnaire des personnes global"}, "ROLE_VEHICLE_AUDIT": {"description": "Un auditeur des véhicules peut visualiser l'ensemble de la flotte des véhicules de secours", "name": "Auditeur des véhicules"}, "ROLE_VEHICLE_MANAGEMENT": {"description": "Un gestionnaire des véhicules du portail peut lancer la synchronisation des véhicules.", "name": "Gestionnaire des véhicules"}, "ROLE_ZONE_CHIEF": {"description": "Un chef de zone accède à des fonctionnalités administratives de premier et second niveaux (Gestion de modèle de plan de service, gestion de plan de service, gestion des fonctions opérationnelles).<br />Il peut visualiser la liste des entités, des véhicules et les droits utilisateur du portail.<br /><b>L'ensemble des accès et droits sont dans la limite de son/es entité(s)</b>", "name": "Chef de zone"}, "column": "R<PERSON><PERSON>", "dependency": "cette permission entraîne obligatoirement la permission à d'autres droits et/ou la suppression de cette permission entraîne obligatoirement la suppression d'autres permissions", "entity": "Enti<PERSON>(s)", "entityinheritance": "Heritage des droits", "no-permissions": "Aucune permission accordée", "no-search-result": "Aucun résultat", "search-permission": "Rechercher une permission accordée ...", "select-permission": "<PERSON><PERSON><PERSON><PERSON><PERSON> une permission"}, "vehicles": {"title": "", "type": {"AMB": "", "DL": "", "FR": "", "HLF": "", "LF": "", "TMF": ""}}, "warnings": {"message": {"VEHICLE_TYPE_UPDATE": ""}}, "working_plan": {"new": {"title": ""}, "title": ""}}, "vehicles": {"title": "Mes véhicules", "type": {"AMB": "", "DL": "", "FR": "", "HLF": "", "LF": "", "TMF": ""}}, "warning_allowances": "Attention, le montant des indemnités est calculé 1 fois par heure.", "warnings": {"message": {"VEHICLE_TYPE_UPDATE": "Le type du véhicule a changé dans ELS mais le véhicule est utilisé sur un plan de service", "backup-group-unavailable": "Le groupe de backup {{group.name}} n'est pas disponible pour le plan de service {{servicePlan.portalLabel}}", "backup-mode-activated": "Backup mode CSU112 est activé", "group-unavailable-for-plans": "Le groupe {{group}} n'est pas disponible pour le(s) plan(s) {{servicePlan}}", "optional-group-unavailable": "Le groupe optionnel {{group.name}} n'est pas disponible {{servicePlan.portalLabel}}", "pleaserotatedevice": "L'affichage est optimisé pour le mode paysage. Veuillez tourner votre appareil.", "pager-owner-change-only-person": "Attention, le pager owner a été modifié. Ce changement ne sera pris en compte qu'à la validation de la modification du pager. A la validation, {{personName.firstName}} {{personName.lastName}} n'aura plus de pager assigné", "pager-owner-change-person-with-sim": "Attention, une carte SIM est encore associée à ce pager. A la validation, {{personName.firstName}} {{personName.lastName}} n'aura plus de pager assigné", "affectation-change-only-with-sim": "Attention, une carte SIM est encore associée à ce pager.", "sim-inactive": "Attention, la carte SIM associée est désactivée."}}, "working_plan": {"new": {"title": "Création d'un plan de travail"}, "title": "Plan de travail"}, "rici": {"title": "RICI 2.0", "common": {"validate-cancel-buttons": {"label-validate": "Valider", "label-cancel": "Annuler"}}, "alert-groups": {"list": {"title:": "Gestion des groupes d'alerte", "add-alert-group": "Ajouter und groupe d'alerte", "headers": {"name": "Nom Groupe Alerte", "ric": "RIC", "status": "Statut", "order": "Ordre", "description": "Description", "actions": "Actions", "deletion": "Suppression"}, "tooltips": {"schema": "<PERSON><PERSON>", "range": "É<PERSON>", "bell-mute": "Quick mute", "bell-unmute": "Quick unmute", "person": "Assign<PERSON> <PERSON>", "audit": "Audit"}, "popups": {"delete": {"title": "Supprimer un groupe d'alerte?", "text": "Confirmer la suppression du groupe d'alert {{name}}"}}, "add": {"title": "Création des groupes d'alerte", "schema-alias-header": "<PERSON><PERSON>", "add-description-placeholder": "+ Déscription", "schema-suffix-header": "Suffix", "range-start-header": "RIC", "tooltip-delete": "Supprimer groupe d'alerte", "tooltip-delete-impossible": "Impossible de supprimer le groupe d'alerte. {{count}} RIC assigné(s).", "success-toast": "Le(s) groupe(s) d'alerte a/ont été créé(s) avec succès!", "success-delete-toast": "Le groupe d'alerte a bien été supprimé!", "missing-description": "La description est obligatoire. Merci de completer le(s) champ(s) manquant(s)"}, "edit": {"title": "Édition des groupes d'alerte", "order-toast": "L'ordre a bien été changé"}}}, "pager-assignment-and-groups": {"list": {"title": "Gestion de l'affectation pagers et groupes d'alerte", "headers": {"name": "Nom", "first-name": "Prenom", "registration": "Matricule CGDIS", "pager-id": "PagerID", "update-status": "MAJ statut", "G-AG-CIS": "G-AG-CIS", "G-INCSA": "G-INCSA", "G-AGT": "G-AGT", "G-CSec": "G-CSec", "G-AG-INCSA": "G-AG-INCSA", "G-FR": "G-FR", "G-AG-SAP": "G-AG-SAP", "G-CADRE": "G-CADRE", "G-TLFW": "G-TLFW", "actions": "Actions"}, "popups": {"assignment": {"add-allocation-link": "Affecter + ", "add-allocation": "A<PERSON><PERSON> un pager?", "add-allocation-success": "Le pager a bien été assigné", "pagers-list-label": "Liste des pagers disponibles:", "pagers-list-selector-placeholder": "Pager ID", "pagers-list-empty": "Aucun pager disponible"}, "edit": {"title": "Modifier le pager affecté?", "status-label": "Statut du pager actuel", "reason-label": "<PERSON><PERSON> du changement", "reason-placeholder": "Demande de reparation", "pager-list-label": "Lise des pagers disponibles", "pager-list-placeholder": "Pager ID"}}, "actions": {"edit-tooltip": "Modifier l'affectation pager/utilisateur", "repair-tooltip": "Repare pager", "lost-tooltip": "Perd pager", "transmit-tooltip": "Transmis pager", "transmit-all-tooltip": "Transmis tous les pagers"}, "add-allocation-link": "Affecter + ", "add-allocation": "A<PERSON><PERSON> un pager?", "pagers-list-label": "Liste des pagers disponibles:", "pagers-list-selector-placeholder": "Pager ID", "pagers-list-empty": "Aucun pager disponible.", "assignment-modification-toast-true": "Le groupe alerte a bien été ajouté!", "assignment-modification-toast-false": "Le groupe alerte a bien été enlevé!"}}, "pagers": {"list": {"title": "Gestion des pagers", "headers": {"inactiv-toggle": "Afficher les pagers inactifs", "pager-id": "PagerID", "serial-number": "Numéro série", "pager-status": "Statut pager", "MSISDN": "MSISDN", "assignment": "Affectation", "update-status": "MAJ statut", "programming-date": "Date Progr", "last-update": "Dernière MAJ", "actions": "Actions", "pager-assignment-person": "Pager Owner", "pager-assignment-entity": "Entité", "pager-assignment-entity-filter": "Sélectionner une entité", "pager-assignment-type": "Type Affectation"}, "delete": {"success": "Le pager a été supprimé avec succès.", "error": "Une erreur s'est produite lors de la suppression du pager."}, "popup": {"delete": {"title": "Supp<PERSON>er le Pager?", "message": "Le pager <b>{{name}}</b> va être supprimé.", "message-no-pager-id": "Le pager dont le Pager ID non renseigné va être supprimé."}, "upload": {"title": "Importer des Pagers par CSV", "note": "Veuillez sélectionner un fichier CSV à importer.", "note-file-columns": "Colonnes attendues : SERIAL_NUMBER, PAGER_ID (optionnel), STATUS, MANUFACTURER, MODEL, DELIVERY_DATE, SIM_ICCID (optionnel)", "mandatoryPagerFieldsLabel": "Données obligatoires", "ifSimAssociatedMandatoryFieldsLabel": "Si carde SIM associée, données obligatoires", "dateFormat": "YYYY-MM-DD", "button": {"import": "Importer"}, "loading": "Importation des pagers en cours...", "error": {"no-file-selected": "Aucun fichier sélectionné. Veuillez sélectionner un fichier CSV.", "generic": "Une erreur est survenue lors de l'importation du fichier CSV.", "noReportData": "Aucune donnée de rapport disponible."}, "report": {"dialogTitle": "Rapport d'Importation CSV des Pagers", "title": "Détails de l'Importation", "fileName": "Nom du fichier", "total": "Total lignes à traiter", "successful": "Importés avec succès", "validationErrors": "Erreurs de validation", "importErrors": "Erreurs d'importation", "errorsDetails": "Détails des erreurs", "line": "Ligne", "rawData": "Données brutes", "allSuccess": "Tous les {{count}} enregistrements ont été importés avec succès.", "noErrorsNoSuccess": "Aucune erreur détectée, mais aucun enregistrement n'a été importé avec succès.", "emptyFileAfterHeader": "Le fichier est vide ou ne contient que l'en-tête.", "fileLevelError": "Erreur au niveau du fichier (par exemple, en-tête invalide) :", "noReportData": "Aucune donnée de rapport disponible."}, "expectedColumnsTitle": "Colonnes attendues dans le fichier CSV", "column": {"serial_number": {"name": "Numéro de série", "description": "Numéro de série du pager"}, "pager_id": {"name": "Identifiant du pager", "description": "Identifiant unique du pager"}, "manufacturer": {"name": "Fabricant", "description": "Fabricant du pager"}, "model": {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> du pager"}, "delivery_date": {"name": "Date de livraison", "description": "Date de livraison du pager (Format AAAA-MM-JJ)"}, "sim_iccid": {"name": "Identifiant de carte SIM (ICCID)", "description": "ICCID de la carte SIM à associer à ce pager"}, "sim_msisdn": {"name": "Numéro MSISDN de la SIM", "description": "Numéro MSISDN de la carte SIM"}, "sim_pin": {"name": "Code PIN de la SIM", "description": "Code PIN de la carte SIM"}, "optional": "optionnel"}, "downloadExample": "Télécharger le fichier d'exemple (.csv)"}}, "actions": {"addPagerTooltip": "Ajouter un nouveau pager", "downloadTooltip": "Exporter la liste des pagers en CSV", "downloadTooltipOne": "Exporter le pager en CSV", "auditTooltip": "Voir l'histoire", "deleteTooltip": "Supp<PERSON><PERSON> le pager", "deleteWasAssignedTooltip": "Suppression impossible - affectation courante ou passée", "deleteDisabledTooltip": "Suppression impossible - affectation courante ou passée", "repairTooltip": "Prise en charge d'un pager défectueux/perdu", "uploadTooltip": "Importer une liste de pagers depuis un CSV"}}, "resetPassword": {"success": "Mot de passe de maintenance réinitialisé avec succès.", "error": "Erreur lors de la réinitialisation du mot de passe de maintenance."}, "creation": {"title": "Création d'un pager", "success": "Pager c<PERSON><PERSON> avec succès."}, "update": {"title": "Edition d'un pager", "success": "Pager c<PERSON><PERSON> avec succès."}, "details": "<PERSON><PERSON><PERSON> d'un pager", "details-link-closed": "+ <PERSON><PERSON><PERSON>", "details-link-opened": "- <PERSON><PERSON><PERSON>", "auditTooltipDisabled": "L'audit n'est disponible qu'après la création du pager.", "pagerId": "Pager ID", "pagerIdTooltip": "Le Pager ID est optionnel à la création. Il doit être unique, de type alphanumérique et d'une longueur de 10 caractères. Attention, Il est obligatoire pour activer un Pager.", "serialNumber": "Numéro de série", "serialNumberTooltip": "Il est obligatoire, unique, de type alphanumérique et d'une longueur de 14 caractères.", "manufacturer": "Fabricant", "manufacturerTooltip": "Il est obligatoire et permet de déterminer les choix possibles du modèle.", "model": "<PERSON><PERSON><PERSON><PERSON>", "modelTooltip": "Il est obligatoire. Il est dépendant du choix du fabricant.", "deliveryDate": "Date de livraison", "deliveryDateTooltip": "Elle est obligatoire. <PERSON><PERSON> <PERSON>, la date du jour est indiquée", "maintenancePassword": "MdP Maintenance", "maintenancePasswordTooltip": "Le mot de passe maintenance est généré automatiquement par le système à la création ou à la réinitialisation si besoin (modification du pager)", "resetPasswordTooltipDisabled": "La réinitialisation du mot de passe n'est pas disponible à la création.", "pagerStatus": "Statut Pager", "pagerStatusTooltip": "Statut actuel du pager (défini automatiquement selon l'affectation).", "simAllocation": "Allocation SIM - ICCID", "simAllocationTooltip": "L'allocation d'une SIM est optionnelle. Elle peut se faire par l'ICCID ou MSISDN, les listes sont interdépendantes et proposent uniquement les cartes SIM actives et disponibles. Attention, l'allocation est obligatoire pour activer un Pager", "searchSimPlaceholder": "Rechercher par ICCID...", "simAllocationMsisdn": "Allocation SIM - MSISDN", "simAllocationMsisdnTooltip": "L'allocation d'une SIM est optionnelle. Elle peut se faire par l'ICCID ou MSISDN, les listes sont interdépendantes et proposent uniquement les cartes SIM actives et disponibles. Attention, l'allocation est obligatoire pour activer un Pager", "searchMsisdnPlaceholder": "Rechercher par MSISDN...", "assignment": "Affectation", "assignmentTooltip": "L'affectation par défaut est STOCK. L'affectation indique où se trouve (assigné à une personne, à une entité, au stock et si perdu, inconnu) et l'état du pager (déclassé)", "pagerOwner": "Pager owner", "pagerOwnerLink": "+Pager owner", "simCardLink": "+Carte SIM", "pagerOwnerTooltip": "Utilisa<PERSON>ur auquel le pager a été affecté", "searchUserPlaceholder": "Rechercher par nom...", "matricule": "Matricule CGDIS", "individualRic": "RIC Individuel", "entity": "Entité", "change-person-reason": "<PERSON><PERSON> du changement", "entityTooltip": "Entité à laquelle le pager a été affecté", "searchEntityPlaceholder": "Rechercher par nom d'entité...", "auditComingSoon": "Fonctionnalité d'audit bientôt disponible.", "resetPasswordComingSoon": "Fonctionnalité de réinitialisation bientôt disponible.", "form": {"programming": {"status": "Programmation Pager", "status-tooltip": "Programmation Pager TT", "programming-date": "Date programmation", "last-update": "Derniere mise à jour", "toast-success": "L'envoi de la programmation du pager a été effectué avec succès", "toast-error": "L'envoi de la programmation du pager a échoué", "bulk-toast-success": "L'envoi de la programmation des pagers en masse a été effectué avec succès", "bulk-toast-error": "L'envoi de la programmation des pagers en masse a échoué"}, "update": {"toast": "Le Pager a bien été mis à jour"}, "actions": {"transmit": "Transmettre"}}, "status": {"ACTIVE": "Actif", "INACTIVE": "Inactif", "DEFECTIVE": "Dé<PERSON><PERSON>ux", "LOST": "Inconnu", "DECOMMISSIONED": "Décommissionné", "DESTROYED": "<PERSON>é<PERSON><PERSON>", "updated": "Mis à jour", "pending": "En attente", "overdue": "En retard"}, "assignmentType": {"PERSON": "Utilisa<PERSON>ur", "STOCK": "Stock", "POOL_CIS": "Pool CGDIS", "LOST": "Inconnu", "DESTROYED": "<PERSON>é<PERSON><PERSON>", "DECLASSED": "Déclassé"}, "update-status": {"UP_TO_DATE": "À JOUR", "PENDING_UPDATE": "MAJ EN ATTENTE", "UPDATE_OVERDUE": "MAJ EN RETARD", "UPDATE_ERROR": "MAJ ERREUR", "TRANSMISSION_ERROR": "MAJ ERREUR EN TRANSMISSION", "DEFAULT": "DÉFAUT"}, "assignment-types": {"PERSON": "PERSONNE", "STOCK": "STOCK", "POOL_CIS": "POOL_CIS", "LOST": "PERDU", "DECLASSED": "DÉCLASSÉ"}, "generateRic": {"title": "Générer un RIC", "affectation": "Affectation", "affectation-message": "Person est affecté à plusieurs entités, veuil<PERSON>z sélectionner l'entité concernée", "entity": "Entité", "button": "<PERSON><PERSON><PERSON><PERSON>"}}, "sim-list": {"title": "Gestion des cartes SIM", "table": {"filters": {"toggle-deactivated-sims": "Afficher les cartes SIM désactivées"}, "headers": {"iccid": "ICCID", "msisdn": "MSISDN", "pin": "PIN", "status": "Statut", "pager-id": "PagerID", "operations": "Opérations"}}, "actions": {"addSimTooltip": "Ajouter une nouvelle carte sim", "downloadSimTooltip": "Télécharger la liste des cartes sim", "uploadSimTooltip": "Importer une liste des cartes sim", "deactivate-tooltip": "Désactiver la carte SIM", "deactivate-disabled-tooltip": "Désactivation impossible.", "delete-tooltip": "Supprimer la carte SIM", "delete-disabled-tooltip": "Suppression impossible. Il existe une affectation courante ou passée.", "pager-details-tooltip": "Voir les details du pager", "history-tooltip": "Voir l'historique", "reactivate-tooltip": "Réactiver la carte SIM", "reactivate-disabled-tooltip": "Réactivations n'est pas possible", "last-pager-tooltip": "Ancien pagerID associé", "active-pager-tooltip": "Actuel pagerID associé"}, "popup": {"delete": {"title": "Suppression - Carte SIM", "note": "Veuillez confirmer la suppression de la carte SIM avec l'ICCID: {{iccid}}", "number": "<PERSON><PERSON><PERSON><PERSON>", "error": {"error": "Une erreur s'est produite lors de la création de la carte SIM.", "no-id": "Suppression impossible, la carte n'est pas trouvé."}, "success": "La carte SIM a bien été supprimée."}, "deactivate": {"title": "Suppression impossible - désactiver la carte SIM?", "confirmation": "Veuillez confirmer la désactivation de la carte SIM avec l'ICCID: {{iccid}}", "active-pager-id": "Actuel pagerID associé", "last-pager-id": "Ancien pagerID associé", "no-pager-history": "Ancien pagerID : Aucun", "error": {"error": "Une erreur s'est produite lors de la désactivation de la carte SIM.", "no-id": "Désactivation impossible, la carte n'est pas trouvé."}, "success": "La carte SIM a bien été désactivée."}, "reactivate": {"title": "Réactiver la carte SIM?", "confirmation": "Veuillez confirmer la réactivation de la carte SIM avec l'ICCID: {{iccid}}", "last-pager-id": "Ancien pagerID associé", "no-pager-history": "Ancien pagerID : Aucun", "error": {"error": "Une erreur s'est produite lors de la réactivation de la carte SIM.", "no-id": "Réactivation impossible, la carte n'est pas trouvé."}, "success": "La carte SIM a bien été réactivée."}, "upload": {"title": "Import CSV - Carte(s) SIM", "note": "Veuillez sélectionner un fichier CSV à importer.", "note-file-columns": "Le fichier doit contenir les colonnes : ICCID, MSISDN, PIN, SIM_STATUS, et optionnellement PAGER_ID, LINK_STATUS, LINK_START_DATETIME, LINK_END_DATETIME.", "loading": "Importation des cartes SIM en cours...", "noSimsImported": "Aucune carte SIM n'a été importée.", "mandatoryFieldsLabel": "Données obligatoires", "optionalFieldsLabel": "Si pager associé, données obligatoires", "expectedColumnsTitle": "Colonnes attendues dans le fichier CSV", "column": {"iccid": {"name": "Identifiant de carte SIM (ICCID)", "description": "Numéro d'identification de la carte SIM (ICCID)"}, "msisdn": {"name": "Numéro MSISDN", "description": "Numéro MSISDN de la carte SIM"}, "pin": {"name": "Code PIN", "description": "Code PIN de la carte SIM"}, "pager_id": {"name": "Identifiant du pager", "description": "Identifiant du pager auquel cette carte SIM doit être associée"}, "pager_serial_number": {"name": "Numéro de série du Pager", "description": "Numéro de série du pager auquel cette carte SIM doit être associée"}, "optional": "optionnel"}, "downloadExample": "Télécharger le fichier d'exemple (.csv)", "error": {"no-report-data": "Aucune donnée de rapport disponible.", "server-processing": "Une erreur s'est produite lors du traitement du fichier sur le serveur. Veuillez consulter le rapport.", "no-file-selected": "Veuillez sélectionner un fichier.", "generic": "Une erreur est survenue lors de l'importation du fichier CSV des cartes SIM.", "noReportData": "Aucune donnée de rapport disponible."}, "button": {"import": "Importer"}, "success": {"generic": "Importation terminée avec succès.", "partial": "{{success}} sur {{total}} carte(s) SIM importée(s) avec succès. Certaines lignes comportent des erreurs."}, "report": {"dialogTitle": "Rapport d'Importation CSV des cartes SIM", "title": "Détails de l'Importation", "fileName": "Nom du fichier", "total": "Total lignes à traiter", "successful": "Su<PERSON>ès", "validationErrors": "Erreurs de validation", "importErrors": "Erreurs d'importation", "errorsDetails": "Détails des erreurs", "line": "Ligne", "rawData": "Données brutes", "allSuccess": "{{count}} carte(s) SIM importée(s) avec succès.", "noErrorsNoSuccess": "Aucune erreur détectée, mais aucune carte SIM n'a été importée.", "emptyFileAfterHeader": "Le fichier est vide après l'en-tête. Aucune ligne de données n'a été trouvée.", "fileLevelError": "Erreur au niveau du fichier :", "containsErrors": "L'importation contient des erreurs. Veuillez consulter le rapport."}}, "download": {"title": "Export CSV - Carte(s) SIM", "note": "Veuillez confirmer le fichier à exporter"}}}, "schemas": {"list": {"title": "Gestion des schémas RIC", "headers": {"name": "Nom du schema RIC", "alias": "<PERSON><PERSON>", "description": "Description", "a-fct": "A-Fct", "b-fct": "B-Fct", "c-fct": "C-Fct", "d-fct": "D-Fct", "actions": "Actions"}, "delete": {"tooltip-impossible": "Supprimer impossible du schéma RIC. Le schema RIC est utilisé par les entités: {{entities}}", "tooltip": "Supp<PERSON>er le schéma RIC", "popup": {"title": "Supprimer le schéma RIC?", "message": "Le schéma RIC <b>{{name}}</b> va être supprimer."}, "toast": "Le schéma RIC a bien été supprimée."}}, "popup": {"validate": {"title": "Confirmer la mise à jour du schéma", "message": "Ce schéma est lié à un ou plusieurs groupes d'alerte. La mise à jour peut nécessiter la programmation des pagers. Voulez-vous continuer ?", "success-toast": "Schéma mis à jour avec succès et programmation des pagers déclenchée."}}, "form": {"add": {"title": "Création d'un schéma RIC", "toast": "Le schéma RIC a bien été crée."}, "edit": {"title": "Édition d'un schéma RIC", "toast": "Le schéma RIC a bien été mise a jour."}, "schema": {"title": "Détails schéma RIC", "audit": "+<PERSON><PERSON>", "name": "Nom schéma RIC", "name-tooltip": "Nom schéma RIC", "alias": "<PERSON><PERSON>", "alias-tooltip": "<PERSON><PERSON>", "suffix": "RIC suffix", "suffix-tooltip": "RIC suffix", "description": "Description", "description-tooltip": "Description"}, "functions": {"title": "Détails fonctions du schéma RIC", "title-tooltip": "Détails fonctions du schéma RIC", "a-fct": "A-Fct", "b-fct": "B-Fct", "c-fct": "C-Fct", "d-fct": "D-Fct", "suffix": "RIC suffix", "description": "Description", "suffix-placeholder": "Suffix", "tone-placeholder": "<PERSON><PERSON>", "model-placeholder": "<PERSON>e", "color-placeholder": "<PERSON><PERSON><PERSON>", "codes": {"tones": {"TONE_1": "1", "TONE_2": "2", "TONE_3": "3", "TONE_4": "4", "TONE_5": "5"}, "models": {"MODEL_1": "1", "MODEL_2": "2", "MODEL_3": "3", "MODEL_4": "4", "MODEL_5": "5"}, "colors": {"YELLOW": "jeune", "RED": "rouge", "VIOLET": "violet", "GREEN": "vert"}}}}}, "ranges": {"popup": {"delete": {"title": "Supprimer la plage RIC?", "message": "La plage RIC <b>{{name}}</b> va être supprimée."}, "validate": {"title": "Confirmer la mise à jour de la plage", "message": "Cette plage est liée à un ou plusieurs groupes d'alerte. La mise à jour peut nécessiter la programmation des pagers. Voulez-vous continuer ?", "success-toast": "Plage mise à jour avec succès et programmation des pagers déclenchée."}}, "list": {"title": "Gestion des plages RIC", "tooltip": {"delete": "Interdiction de suppression. Plage RIC allouée à "}, "headers": {"name": "Nom de la plage RIC", "type": "Type de Plage", "start": "Début plage", "end": "Fin plage", "allocation": "Allocation", "rate": "Taux d'occupation", "actions": "Actions"}, "filters": {"allocated": "Afficher les plages allouées"}}, "add": {"title": "Création d'une plage RIC"}, "edit": {"title": "Edition d'une plage RIC"}, "form": {"details-title": "Détails plage RIC", "audit-link": "Audit +"}, "single": {"title": "Plage RIC"}, "types": {"special": "Spécial", "standard": "Standard", "system": "System"}, "forms": {"delete": {"toast": "La plage RIC a bien été supprimée."}, "update-form": {"name": "Nom", "tooltip-name": "To be defined", "type": "Type", "tooltip-type": "To be defined", "allocation": "Allocation", "tooltip-allocation": "To be defined", "rangeStart": "Début plage", "tooltip-rangeStart": "To be defined", "rangeEnd": "Fin plage", "tooltip-rangeEnd": "To be defined", "toast": "La plage RIC a bien été mise a jour."}, "create-form": {"name": "Nom", "tooltip-name": "To be defined", "type": "Type", "tooltip-type": "To be defined", "allocation": "Allocation", "tooltip-allocation": "To be defined", "rangeStart-prefix": "Plage debut (Prefixe)", "rangeStart-suffix": "Plage debut (Suffixe)", "rangeEnd-prefix": "Plage fin (Prefixe)", "rangeEnd-suffix": "Plage fin (Suffixe)", "tooltip-rangeStart": "To be defined", "rangeEnd": "Fin plage", "tooltip-rangeEnd": "To be defined", "toast": "La plage RIC a bien été créée."}}}, "sim": {"title": "Création d'une carte SIM", "details": "Détails carte SIM", "associated-pager-info-subtitle": "Pager associé", "associated-pager-info-no-pager": "Aucun pager associé pour le moment.", "iccid": "ICCID", "msisdn": "MSISDN", "pin": "PIN", "status": "Statut", "pagerId": "PagerID", "serialNumber": "Numéro de série", "pager-owner": "Pager owner", "matricule-cgdis": "Matricule CGDIS", "iccidTooltip": "ICCID = Numéro d'identification de carte de circuit intégré. Il est obligatoire, unique, de type numérique et d'une longueur de 16 à 20 caractères", "msisdnTooltip": "MSISDN = Numéro d'identification du numéro de téléphone à l'international. Il est obligatoire, unique, de type numérique et d'une longueur maximale de 15 caractères", "pinTooltip": "PIN = Personal Identification Number. Il est obligatoire, de type numérique et d'une longueur de 4 à 8 caractères", "statusTooltip": "STATUT = Statut de la carte SIM. Il est obligatoire et soumis à des règles métier", "auditLink": "+<PERSON><PERSON>", "auditComingSoon": "Audit - coming soon", "historiqueStatutLink": "+Historique statut", "pager-details": "+<PERSON><PERSON><PERSON>", "add-pager": "+Pager", "status-history": {"title": "Historique des statuts", "no-data": "Donnes non trouves", "date": "Date changement", "status": "Statut"}, "statuses": {"disponible": "Disponible", "associee": "<PERSON><PERSON><PERSON><PERSON>", "inactive": "Inactive"}, "pager-details-coming-soon": "Pager details - coming soon", "historiqueStatutComingSoon": "Historique statut - coming soon", "createNewSimCard": "Création d'une carte SIM", "creation": {"success": "La carte SIM a été créée avec succès.", "error": "Une erreur s'est produite lors de la création de la carte SIM."}, "edit": {"title": "Modification d'une carte SIM", "success": "La carte SIM a été modifiée avec succès.", "error": "Une erreur s'est produite lors de la modification de la carte SIM.", "no-pager-associated": "Aucun pager associé."}}, "pagerAssociation": {"title": "Associer la nouvelle carte SIM à un pager ?", "subtitle": "Veuillez sélectionner le pager", "success": "La carte SIM a été associée au pager avec succès.", "error": "Une erreur s'est produite lors de l'association de la carte SIM au pager.", "noPagersAvailable": "Aucun pager disponible pour l'association."}}}