{"fetching-text": "Getting data...", "function-operationals": {"status": {"DRAFT": "Draft", "VALIDATED": "Validated", "CLOSED": "Closed"}}, "admin": {"allowance": {"back": "Back", "configuration": {"actions": "", "barracked_amount": "Barracked amount", "barracked_amount_mobile": "", "configVersion": "Version", "creation": {"error": "Failed to create allowance configuration '{{label}}', Error code : {{code}}", "success": "Allowance configuration successfully created", "title": "Allowance configuration creation"}, "delete": {"error": "Failed to suppress the configuration", "success": "Configuration successfully deleted"}, "endDate": "End date", "label": "Version label", "label_mobile": "", "new": {"fields": {"barrackedAmount": "Barracked amount", "barrackedAmount_mobile": "Barracked", "endDate": "End date", "label": "Version label", "notBarrackedAmount": "Not Barracked amount", "notBarrackedAmount_mobile": "Not Barracked", "startDate": "Start date", "version": "Computing version", "versionName": "Version"}, "title": "New configuration"}, "not_barracked_amount": "Not barracked amount", "not_barracked_amount_mobile": "", "popup": {"delete": {"message": "Do you really want to delete the configuration <b>{{name}}</b>?", "subtitle": "of a configuration", "title": "Deletion"}}, "startDate": "Start date", "title": "Allowance configurations", "update": {"success": "Allowance successfully updated", "title": "Edit configuration", "title_view": "View configuration"}, "version": "Computing version"}, "create": "Create", "list": {"allowanceAmount": "Allowance amount", "allowanceAmount_mobile": "", "allowanceDate": "Allowance date", "duration": "Duration"}, "search": {"detail": {"title": "Search for allowance for"}, "title": "Search for allowance"}, "title": "Allowance"}, "boxes": {"form": {"globalId": "Foreign key", "isMaster": "Master box?", "name": "Name"}, "list": {"header": {"entityName": "", "globalId": "Foreign key", "name": "name"}, "title": "List of boxes", "titlemobile": "List of boxes"}, "title": "Manage boxes", "view": {"title": "Boxes", "titlemobile": "Box's detail"}}, "entities": {"form": {"armingPriority": "Armament priority", "armingPriority_mobile": "Priority", "category": "Category", "entity": "Entity", "main": "Main entity", "name": "Name", "open": "Open", "openParent": "Open upper level", "parent": "Upper level", "type": "Type", "update": {"success": "Entity updated with success"}}, "list": {"header": {"actions": "Actions", "armingPriority": "Armament priority", "armingPriority_mobile": "Priority", "category": "Category", "mainEntity": "Main Entity", "name": "Name", "parentEntity": "Parent Entity", "servicePlanType": "Service plan type", "type": "Type"}, "title": "List of entities", "titlemobile": "Entity list"}, "title": "Manage entities", "view": {"title": "Entity", "titlemobile": "Entity"}}, "export": {"creation": {"error": "Failed to create export profile '{{name}}', Error code : {{code}}", "success": "Export profile '{{name}}' successfully created", "title": "Export profile creation"}, "delete": {"error": "Failed to suppress the export profile, Error code : {{code}}", "success": "Export profile successfully deleted"}, "form": {"add-mail": "Add email", "cron": "Frequency", "data-export": "Data to export", "email": "Email", "entity": "Entity", "export-format": {"csv": "CSV Format", "format": "", "json": "JSON Format"}, "export-number-days": "Number days to export", "export-on-server": "Export on server", "export-persons": "Export persons", "export-service-plans": "Export service plans", "export-type": "Export type", "export-vehicles": "Export vehicles", "name": "Name", "remove-mail": "Remove"}, "list": {"header": {"actions": "Actions", "entity": "Entity", "last-execution": "Last execution", "last-execution-mobile": "Last exe.", "name": "Name", "next-execution": "Next execution", "next-execution-mobile": "Next exe."}, "title": "Export Profiles", "titlemobile": "Export Profil"}, "popup": {"delete": {"message": "Do you really want to delete the export profile <b>{{name}}</b>?", "subtitle": "of a export profile", "title": "Deletion"}, "force-execution": {"message": "Do you really want to force the export profil execution <b>{{name}}</b>?", "subtitle": "of a export profile", "title": "Forced execution"}}, "title": "Export Profiles", "update": {"error": "Failed to update the service plan '{{name}}', Error code : {{code}}", "success": "Export profile '{{name}}' successfully updated", "title": "Editing an export profile", "titlemobile": "Detail of an export profile"}}, "fotags": {"title": "Tags", "form": {"fields": {"name": "Name", "description": "Description"}, "new": {"success": "Tag created"}, "update": {"success": "Tag updated"}}, "list": {"columns": {"name": "Name", "description": "Description", "actions": "Actions"}, "delete": {"confirmation": {"title": "Delete tag", "message": "Do you confirm the deletion of tag {{name}}?"}}}}, "function_operational": {"close": {"error": ""}, "creation": {"error": "Failed to create operational function '{{label}}', Error code : {{code}}", "success": "Operational function '{{label}}' successfully created", "title": "Operation function creation"}, "delete": {"error": "Failed to delete operational function, Code d'erreur : {{code}}", "success": "Operational function successfully deleted"}, "form": {"id": "Id", "intervention_type": "Intervention type", "label": "Label", "portalLabel": "Portal Label", "tags": "Tags", "tagnomoreavailable": " (deleted)"}, "list": {"header": {"actions": "Actions", "entitiescount": "Entities count", "closed": "Closed", "function": "Function", "id": "ID", "intervention_type": "Intervention type", "interventiontype": "Intervention type", "label": "Label", "portalLabel": "Portal Label"}, "modelName": "Nom du modèle", "nodata": "No entity", "title": "Operational functions list", "titlemobile": "Operational functions list", "totalElements": "{{totalElements}} results"}, "orders": {"title": "Operational functions orders management", "list": {"columns": {"label": "Operational function", "order": "Order", "actions": "Update order"}}}, "popup": {"closure": {"message": "Do you really want to close the operational function <b>{{label}}</b> ?", "subtitle": "of an operational function", "title": "Closure"}, "delete": {"message": "Do you really want to delete the operational function <b>{{label}}</b> ?", "subtitle": "of an operational function", "title": "Deletion"}, "validate": {"message": "Do you really want to validate the operational function <b>{{label}}</b> ?", "subtitle": "of an operational function", "title": "Validation"}}, "update": {"error": "Failed to update operational function '{{label}}', Error code : {{code}}", "success": "Operational function '{{label}}' suyccessfully updated", "title": "Operational function update", "titlemobile": "Operational function detail"}}, "general-message": {"creation": {"error": "Failed to create general message '{{name}}', Error code : {{code}}", "success": "The general message '{{name}}' successfully updated", "title": "Creation of a general message", "titlemobile": "Creation of a general message"}, "delete": {"error": "Failed to delete general message , Error code : {{code}}", "success": "The general message was successfully deleted"}, "form": {"endDateTime": "End date and hour", "message": "Message", "mobileEndDateTime": "End date", "mobileStartDateTime": "Start date", "name": "Name", "startDateTime": "Start date and hour"}, "list": {"header": {"endDateTime": "End date", "message": "Message", "mobileEndDateTime": "End", "mobileStartDateTime": "Start", "name": "Name", "startDateTime": "Start date"}, "title": "General messages", "titlemobile": "General messages"}, "title": "General messages", "update": {"error": "Failed to update general message '{{name}}', Error code : {{code}}", "success": "General message '{{name}}' successfully updated", "title": "Edition of general message", "titlemobile": "Detail of general message"}}, "position_template": {"closure": {"error": "Failed to close the position template, Error code : {{code}}", "success": "Position template successfully closured"}, "copy": {"error": "Failed to copy position template", "new_name": "Copy - {{label}}", "success": "Position template '{{label}}' successfully copied", "title": "Copy an existing position template"}, "creation": {"error": "Failed to create position template '{{label}}', Error code : {{code}}", "success": "Position template '{{label}}' successfully created", "title": "Position template creation"}, "delete": {"error": "Failed to delete the position template, Error code : {{code}}", "success": "Position template successfully deleted"}, "form": {"buttons": {"addversion": "Add a version"}, "id": "Identifier", "intervention_type": "Intervention type", "label": "Label", "portalLabel": "Portal label"}, "list": {"header": {"actions": "Actions", "closed": "Closed", "entity": "Entity", "id": "Identifier", "intervention_type": "Intervention Type", "label": "Label", "portalLabel": "Label"}, "title": "List of position templates", "titlemobile": "List of position templates"}, "popup": {"closure": {"message": "Do you really want to close the position template <b>{{label}}</b> ?", "subtitle": "of a position template", "title": "Closure"}, "delete": {"message": "Do you really want to delete the position template <b>{{label}}</b> ?", "subtitle": "of a position template", "title": "Deletion"}}, "title": "Manage position templates", "update": {"error": "Failed to change the position template '{{label}}', Error code : {{code}}", "success": "Position template '{{label}}' successfully changed", "title": "Editing a position template", "titlemobile": "Position template detail"}, "version": {"closure": {"error": "Failed to close the position template", "success": "Position template successfully closured"}, "copy": {"label": "New Label", "message": " Do you want to copy this model version?", "target": "to", "title": "Position template Version Copy"}, "create": {"success": "Version successfully created", "title": "Version creation"}, "delete": {"success": "Version '{{label}}' deleted"}, "edit": {"information_message": "Please note that any validation may result in the deletion of future prestations from the position templates linked to the previous model.", "success": "The version has been updated", "title": "Update a version of positions", "titlemobile": "Detail of version of positions"}, "form": {"button": {"addversion": "Add a version of positions"}, "enddate": "End date", "enddate_mobile": "End", "function": {"empty": "No function", "type": {"ideal": "Ideal function", "partial": "Partial function"}}, "functions": {"empty": "", "type": {"ideal": "", "partial": ""}}, "label": "Name", "startdate": "Start date", "startdate_mobile": "Start", "version": "Version"}, "function": {"empty": "No function", "type": {"ideal": "Ideal function", "partial": "Partial function"}}, "functions": {"empty": "", "type": {"ideal": "", "partial": ""}}, "list": {"header": {"actions": "Actions", "enddate": "End date", "enddate_mobile": "End", "label": "Name", "startdate": "Start date", "startdate_mobile": "Start"}}, "popup": {"closure": {"message": "Do you really want to close the position template version <b>{{label}}</b> of {{startDate}} ?", "subtitle": "of a position template version", "title": "Closure"}, "delete": {"message": "Do you really want to delete the version <b>{{label}}</b> of {{startDate}} ?", "subtitle": "of a position template", "title": "Deletion"}}}}, "public-holiday": {"creation": {"error": "Failed to create public holiday '{{name}}', Error code : {{code}}", "success": "The public holiday '{{name}}' successfully updated", "title": "Creation of a public holiday", "titlemobile": "Creation of a public holiday"}, "delete": {"error": "Failed to delete public holiday , Error code : {{code}}", "success": "The public holiday was successfully deleted", "title": "Deletion", "subtitle": "public holidays", "message": "Are you sure you want to remove this holiday : {{name}}"}, "form": {"date": "Date", "endDateTime": "", "message": "", "mobileEndDateTime": "", "mobileStartDateTime": "", "name": "Name", "startDateTime": ""}, "list": {"header": {"date": "date", "name": "Name", "year": "Year", "actions": "Actions"}, "title": "Public holidays", "titlemobile": "Public holidays"}, "title": "Public holidays", "update": {"error": "Failed to update public holiday '{{name}}', Error code : {{code}}", "success": "Public holiday '{{name}}' successfully updated", "title": "Edition of public holiday", "titlemobile": "Edition of public holiday"}}, "service_plan": {"closure": {"error": "Failed to close the service plan, Error code : {{code}}", "success": "Service plan successfully closured"}, "creation": {"error": "Failed to create service plan '{{label}}', Error code : {{code}}", "success": "Service model '{{label}}' successfully created", "title": "Service plan creation"}, "delete": {"error": "Failed to suppress the service plan, Error code : {{code}}", "success": "Service plan successfully deleted", "title": "Deletion", "subtitle": "Of public holidays ", "message": "Do you really want to delete this holiday? : {{name}}"}, "form": {"armingDelay": "Armament delay", "armingPriority": "Armament priority", "backupGroup": "Backup group", "box": "", "elsBoxManagement": "ELS boxes management", "elsStatus": "ELS status", "elsStatusManagement": "ELS status management", "enabled": "Enabled", "entity": "Entity", "error": {"novehicles": "There is no available vehicles for entity {{entityName}}"}, "firstDay": "First day of the week", "id": "Id", "isusedasoptionalbackupgroup": "The service plan is optional or backup for these services plans: {{servicePlans}}", "label": "Service plan name in ELS", "model": "Model", "optionalGroup": "Optional group", "optionalGroupUnavailable": "(Unavailable)", "popup": {"warning": {"message": "", "title": ""}}, "portalLabel": "Radio code", "vehicle": "Vehicle"}, "list": {"entityName": "Entity name", "header": {"actions": "Actions", "armingDelay": "Armament delay", "armingPriority": "Armament prio.", "closed": "Closed", "closed-no": "No", "closed-yes": "Yes", "entity": "Entity", "id": "ID", "name": "Radio code", "servicePlanType": "Service plan type", "servicePlanType_mobile": "Type", "vehicle": "Vehicle"}, "modelName": "Model name", "mypds": "My PdS", "mypdsmobile": "My Service Plans", "title": "List of service plans", "titlemobile": "Service plan list", "totalElements": "{{totalElements}} results"}, "popup": {"closure": {"message": "Do you really want to close the service plan <b>{{name}}</b>?", "subtitle": "of a service plan", "title": "Closure"}, "delete": {"message": "Do you really want to delete the service plan <b>{{name}}</b>?", "subtitle": "of a service plan", "title": "Deletion"}}, "teams": {"create": {"success": "The team was created", "title": "Team creation"}, "delete": {"error": "Failed of deletion of the team", "success": "Team successfully deleted"}, "form": {"addperson": "+Add person", "button": {"addteam": "Add team"}, "label": "Team name", "popup": {"title": "Select person to {{<PERSON><PERSON><PERSON><PERSON>}}"}, "shortlabel": "Team"}, "list": {"header": {"actions": "Actions", "label": "Team name", "shortlabel": "Team name shown on Pds (3 characters)", "shortlabelmobile": "Team name Pds (3 char.)"}, "popup": {"delete": {"message": "Do you really want to delete the team <b>{{name}}</b>?", "subtitle": "of a team", "title": "Deletion"}}}, "update": {"success": "The team was updated", "title": "Editing a service plan", "titlemobile": "Consulting a team"}}, "update": {"error": "Failed to change the service plan '{{label}}', Error code : {{code}}", "information_message": "Warning, service plan update will cause the deletion of futures prestations assigned to it", "success": "Service plan '{{label}}' successfully changed", "title": "Editing a service plan", "titlemobile": "Service plan administrator details"}, "version": {"closure": {"error": "Failed to close service plan version, error code : {{code}}", "success": "Service plan version closed successfully"}, "create": {"error": "Failed to create the version, Error code : {{code}}", "success": "Version '{{label}}' successfully changed", "title": "Creation of a time slots version", "titlemobile": ""}, "delete": {"error": "Failed to close service plan version, error code :  {{code}}", "success": "Service plan version deleted successfully"}, "details": {"from": "From", "subtitle": "of slots", "title": "Time slots details", "to": "to"}, "edit": {"error": "Failed to change the version, Error code : {{code}}", "information_message": "Warning, service plan update will cause the deletion of futures prestations assigned to it", "success": "The version has been updated", "title": "Time slots version", "titlemobile": "Time slots version"}, "form": {"button": {"addversion": "Add a version"}, "enddate": "End date", "errors": {"modelVersionNotCreated": "The version of the model is not yet created", "total_slots": {"max": "Number of time slots should be 24 max", "min": "Number of time slots should be at least 1", "no_more_place": {"none": "It not possible to add more time slots between {{startTime}} and {{endTime}}", "plural": "It only possible to add {{total}} time slots between {{startTime}} and {{endTime}}", "singular": "It only possible to add {{total}} time slot between {{startTime}} and {{endTime}}"}, "not_enough_place": "It's not possible to had other time slots between {{startTime}} and {{endTime}}"}}, "filling": {"automatic": "Automatic", "manual": "Manual", "title": "Filling mode: "}, "label": "Name", "servicePlanType": "Service plan type", "startTime": "Start time", "startdate": "Start date", "totalSlots": "Slot", "version": "Version"}, "list": {"header": {"actions": "Actions", "enddate": "End date", "label": "Name", "split": "Splited", "startdate": "Start date", "startdate_mobile": "Start", "type": "Service type", "type_mobile": "Type"}, "popup": {"closure": {"message": "Do you really want to close the service plan version  <b>{{name}}</b> of {{startDate}} ?", "subtitle": "of a service plan version", "title": "Closure"}, "delete": {"message": "Do you really want to delete the service plan version <b>{{name}}</b> of {{startDate}} ?", "subtitle": "of a service plan version", "title": "Deletion"}}}}}, "service_plan_model": {"closure": {"error": "Failed to close the service plan model, Error code : {{code}}", "success": "Service plan model successfully closured"}, "copy": {"error": "Failed to copy service plan model", "new_name": "Copy - {{name}}", "success": "Service plan model '{{name}}' successfully copied", "title": "Copy an existing service plan model"}, "creation": {"error": "Failed to create service plan '{{name}}', Error code : {{code}}", "success": "Service plan model '{{name}}' successfully created", "title": "Service plan model creation"}, "delete": {"error": "Failed to delete the service plan model, Error code : {{code}}", "success": "Service plan model successfully deleted"}, "form": {"backup_statuses": "Use of backup's code", "buttons": {"addversion": "Add a version"}, "entity": "Entity", "exclusive": "Exclusive ?", "function_operational": "Use operational functions?", "has_vehicle": "Vehicle ?", "id": "Identifier", "intervention_type": "Intervention type", "name": "Name", "popup": {"warning": {"message": {"entity": "The attribute 'entity' as been updated but service plans exist for this model. If you confirm the modification, these service plans will be deleted", "vehicle": "The attribute 'vehicle' as been updated but service plans exist for this model. If you confirm the modification, these service plans will be deleted", "vehicletypes": "The attribute 'vehicle type' as been updated but service plans exist for this model. If you confirm the modification, these service plans will be deleted", "vehicletypes_removed": "The attribute 'vehicle type' as been updated but service plans exist for this model. Please remove vehicle for these following service plans :"}, "title": "Confirmation"}}, "vehicle_type": "Vehicle type"}, "list": {"header": {"actions": "Actions", "entity": "Entity", "id": "Identifier", "intervention": "Intervention", "name": "Name"}, "title": "List of service plan models", "titlemobile": "List of service plan models"}, "popup": {"closure": {"message": "Do you really want to close the service plan model <b>{{name}}</b> ?", "subtitle": "of a service plan model", "title": "Closure"}, "delete": {"message": "Do you really want to delete the service plan model <b>{{name}}</b> ?", "subtitle": "of a service plan model", "title": "Deletion"}}, "title": "Manage service plan models", "update": {"error": "Failed to change the service plan model '{{name}}', Error code : {{code}}", "information_message": "\nPlease note, an update of the model will lead to the deletion of future services from the service plans linked to the previous model", "success": "Service plan model '{{name}}' successfully changed", "title": "Editing a service plan model", "titlemobile": "Service plan model detail"}, "version": {"closure": {"error": "Failed to close the service plan model", "success": "Service plan model successfully closured"}, "copy": {"label": "New Label", "message": " Do you want to copy this model version?", "target": "to", "title": "Service Plan Model Version Copy"}, "create": {"success": "Version successfully created", "title": "Version creation"}, "delete": {"success": "Version '{{label}}' deleted"}, "edit": {"information_message": "Please note that any validation may result in the deletion of future prestations from the service plans linked to the previous model.", "position": {"degraded": {"title": "Degraded positions"}}, "success": "The version has been updated", "title": "Update a version of positions", "titlemobile": "Detail of version of positions"}, "form": {"button": {"addversion": "Add a version of positions"}, "enddate": "End date", "enddate_mobile": "End", "label": "Name", "startdate": "Start date", "startdate_mobile": "Start", "version": "Version"}, "list": {"header": {"actions": "Actions", "enddate": "End date", "enddate_mobile": "", "label": "Name", "startdate": "Start date", "startdate_mobile": "Start"}}, "popup": {"closure": {"message": "Do you really want to close the service plan model version <b>{{name}}</b> of {{startDate}} ?", "subtitle": "of a service plan model version", "title": "Closure"}, "delete": {"message": "Do you really want to delete the version <b>{{name}}</b> of {{startDate}} ?", "subtitle": "of a service plan model", "title": "Deletion"}}, "position": {"empty": "No position", "function": {"operational_function": "Operational Function", "operational_function_mobile": "Op. Funct.", "partialfullfill": "Degraded positions"}, "type": {"additional": "Additional positions", "complete": "Complete positions", "degraded": "Degraded positions"}}}}, "vehicles": {"els_status": {"1": "Einsatzbereit Funk", "2": "Einsatzbereit Wache", "3": "Einsatz übernommen", "4": "Am Einsatzort", "5": "S<PERSON>re<PERSON>wuns<PERSON>", "6": "Nicht einsatzbereit", "7": "Patient aufgenommen", "8": "Am Transportziel", "1c": "Alarmiert am Einsatzbereit Funk", "2c": "<PERSON><PERSON><PERSON><PERSON>", "3c": "Alarmierung bei Einsatz übernommen", "4c": "Alarmierung am Einsatzort", "7c": "Alarmierung bei Patienten transport", "8c": "Alarmierung am Transportziel"}, "form": {"currentElsStatus": "ELS actual status", "description": "Description", "elsStatusHistoryLablel": "ELS status history", "entity": "Entity", "id": "Id", "name": "Name", "registration": "Registration", "registrationmobile": "", "type": "Type"}, "list": {"header": {"actions": "Actions", "date": "Date", "description": "Description", "id": "Id", "name": "Name", "registration": "Registration", "registrationmobile": "", "servicePlanName": "Associated Service plan", "backupgroup": "Backup group", "status": "Status", "text": "Text", "type": "Type"}, "title": "List of vehicles", "titlemobile": "List of vehicles"}, "title": "Manage vehicles", "update6vehicle": "The vehicle {{idVehicle}} has changed to status: {{statusVehicle}}", "view": {"title": "Vehicle", "titlemobile": "Vehicle's detail"}}}, "allowance": {"scheduler": {"form": {"date": {"label": "Computing date or computing start date"}, "enddate": {"label": "Computing end date"}, "entity": {"label": "Entities list", "nodata": "No entity selected"}, "person": {"label": "Persons list", "nodata": "No person selected"}, "popup": {"confirm": {"message": {"date": "Computing  by date will be launched", "entity": "Computing  by date and entities will be launched", "person": "Computing  by date and persons will be launched"}, "title": "Computing allowances"}}, "submit": {"success": "Generation has been taken into account and will start soon"}}}, "search": {"allowance": "Allowance", "duration": "Duration", "firstname": "Firstname", "lastname": "Lastname", "total": {"allowance": "Total allowance:", "duration": "Total duration:"}}}, "assignments": {"types": {"EXT": "External", "VOL": "Volunteer", "PRO": "Professional"}, "primarytypes": {"PRIMARY": "Primary", "SECONDARY": "Secondary", "TECHNICAL": "Technical"}}, "audit": {"actionDateTime": "Done at", "actionType": {"ADD": "Add", "COPY_ADD": "Add by copy", "CLOSURE": "Close by copy", "COPY_CLOSURE": "Close", "COPY": "Entity copy", "CREATE": "Create", "DAY": "Copy of day", "DELETE": "Delete", "COPY_DELETE": "Delete by copy", "END": "Logas end", "MERGE": "Timeslot merge", "MERGE_DELETED": "Delete by merge", "MERGE_EXTENDED": "Extend by merge", "TEAM_CLOSURE": "Close by team", "TEAM_ADD": "Add by team", "TEAM_DELETE": "Delete by team", "FULL_AV_REM_ADD": "Add for remaining availability", "FULL_AV_REM_DELETE": "Delete for remaining availability", "FULL_AV_ADD": "Add for full availability", "FULL_AV_DELETE": "Delete for full availability", "FULL_AV_CLOSURE": "Close for full availability", "SLOT": "Copy of slot", "SPLIT": "Timeslot split", "SPLIT_ADD": "Add by split", "SPLIT_SPLITTED": "Reduce by split", "START": "Logas start", "UPDATE": "Update", "VIEW": "View", "BOOKMARKED_UPDATE": "Update favorite", "PERM_CATEGORY_UPDATE": "Update category", "PERM_CATEGORY_DELETE": "Delete category", "PERM_CATEGORY_CREATE": "Create category", "PERM_SUBCAT_UPDATE": "Update subcategory", "VALIDATE": "Validation", "VALID_ORDER_IMPACT": "Order impacted by validation", "DELETE_ORDER_IMPACT": "Order impacted by suppression", "CLOSURE_ORDER_IMPACT": "Order impacted by clôture", "UPDATE_ORDER": "Update order", "UPDATE_ORDER_IMPACT": "Order impacted", "title": "Action type"}, "allowance": "Allowance configuration", "copy_prestation": {"day": "<b>{{user}}</b> copied the day <b>{{fromDate}}</b> to <b>{{targetDate}}</b> of the service plan <b>{{servicePlanName}}</b>", "slot": "<b>{{user}}</b> copied the slot <b>{{fromDate}}</b> to <b>{{targetDate}}</b> of the service plan <b>{{servicePlanName}}</b>", "week": "<b>{{user}}</b> copied the week <b>{{fromDate}}</b> to <b>{{targetDate}}</b> of the service plan <b>{{servicePlanName}}</b>"}, "details": "Details", "function-operationals": {"list": {"title": "Operational functions", "interventiontype": "Intervention type", "label": "Name", "portallabel": "Displayed name", "status": "Status", "order": "Order", "tags": "Tags"}, "export": {"title": "Operational functions export", "entity": "Entity", "subentities": "Subordinate entity"}, "assignments": {"title": "Operational functions assignments", "entity": "Entity", "function": "Operational function", "person": "Person"}}, "impersonatedUserName": "IAM logas", "link": "+ <PERSON><PERSON>", "logas_title": "Logas connections", "model_title": "Administration of service plan models", "permamonitor": {"configdpce": {"title": "Permamonitor - POJ Configuration", "deploymentplanname": "Deployment plan", "entityname": "Entity", "categoryname": "Category", "day": {"MONDAY": "Monday", "TUESDAY": "Tuesday", "WEDNESDAY": "Wednesday", "THURSDAY": "Thursday", "FRIDAY": "Friday", "SATURDAY": "Saturday", "SUNDAY": "Sunday", "PUBLIC_HOLIDAY": "Public holiday"}, "configdpcecopy": {"title": "Permamonitor - POJ Configuration copy", "deploymentplanname": "Deployment plan", "entityname": "Source Entity", "toentityname": "Target Entity", "categoryname": "Category", "starthour": "From (target hour)", "endhour": "To (target hour)", "tostarthour": "From (target hour)", "toendhour": "To  (target hour)"}, "dayvalue": "Day", "starthour": "Hour", "optimalvalue": "Optimal", "criticalvalue": "Critical", "unacceptablevalue": "Unaacceptable"}, "deploymentplan": {"title": "Permamonitor - Deployment Plan", "name": "Name", "startdate": "Start date", "description": "Description"}, "serviceplan": {"title": "Permamonitor - Service plan", "name": "Name", "categoryname": "Category", "deploymentplanname": "Deployment plan", "subcategoryname": "Subcategory", "bookmarked": "Favorite"}}, "person": "User", "cgdisregistrationnumber": "Mat. CGDIS (Initiator)", "prestation": {"bypass_function": "By pass of FO", "date": "{{date}} form {{startDatetime}} to {{ endDatetime}}", "person": "For the person", "position": "Position", "service_plan": "Service plan", "slot": "Date", "headers": {"start": "Prestation Start", "end": "Prestation End", "position": "Position", "person": "Mat. CGDIS (impacted)", "personname": "Person (impacted)"}}, "prestation_title": "Prestation", "copyprestations": {"title": "Prestations copy", "headers": {"start": "Source slor start", "end": "Target slot start"}}, "service_plan": {"column": "PDS", "entity": "Entity name", "name": "Service plan name", "type": "Type"}, "service_plan_model": {"entity": "Entity name", "name": "Service plan model name", "type": "Interventions types"}, "service_plan_model_version": {"endDate": "End date", "model_name": "Model name", "name": "Service plan model version name", "spname": "Service plan model name", "startDate": "Start date"}, "service_plan_version": {"endDate": "End date", "name": "Service plan version name", "spname": "Service plan name", "startDate": "Start date"}, "serviceplan_title": "Administration of service plans", "slot": {"title": "Action on Service plan slot", "merge": "<b>{{user}}</b> merged slot from <b>{{fromDate}}</b> to <b>{{endDate}}</b> with slot starting at <b>{{targetDate}}</b> for service plan <b>{{servicePlanName}}</b>", "split": "<b>{{user}}</b> splitted slot from <b>{{fromDate}}</b> to <b>{{endDate}}</b> at <b>{{targetDate}}</b> for service plan <b>{{servicePlanName}}</b>", "headers": {"start": "Source slot start", "end": "Source slot end", "targetstart": "Target slot start", "targetend": "Target slot end"}}, "tabs": {"headers": {"permamonitor": "Permamonitor", "portal": "Portal", "function-operational": "Operational functions"}}, "type": {"COPY_PRESTATION": "Copy of prestations", "LOGAS": "Logas", "MODEL": "Service plan model", "PDS": "Service Plan", "PRESTATION": "Prestation", "SLOT": "Service plan timeslot", "VERSION_MODEL": "Service plan model version", "VERSION_PDS": "Service plan version", "PERM_DEPLOYMENT_PLAN": "Permamonitor - Deployment plan", "PERM_SERVICE_PLAN": "Permamonitor - Service plan", "PERM_CONFIGDPCE": "Permamonitor - POJ Configuration", "PERM_CONFIGDPCE_COPY": "Permamonitor - POJ Configuration copy", "title": "Audit Type", "ALLOWANCE_CONFIG": "Allowance configuration", "FO": "Operational functions", "FO_EXPORT": "Operational functions export"}}, "authentication": {"error": "You are not authenticated", "logout": {"success": "You have been disconnected"}, "lost": "You are no more authenticated"}, "availability_planning": {"copypopup": {"fromTo": "<li>{{start}} at {{startTime}} to {{end}} at {{endTime}}</li>", "message": "Are you sure to copy availabilities from {{fromDate}} ? Existing availabilities will be deleted.<br/>Availabilities to copy are:<br/>{{events}}", "messageNoData": "Are you sure to copy availabilities from {{fromDate}} to {{toDate}} ? Existing availabilities will be deleted.<br/>No availabilities to copy", "messageWeek": "Are you sure to copy availabilities from {{fromDate}} to {{toDate}} ? Existing availabilities will be deleted", "success": "Availabilities successfully copied", "title": "Copy availabilities"}, "deletepopup": {"message": "Do you really want to delete the availability?", "success": "Availability deleted successfully", "title": "Delete availability"}, "list-placeholder": "Type the start of your search, to filter the result", "list-placeholder-mobile": "Type the start to filter", "logas": "People available in logas:", "no_data": "No planning to display", "popup": {"form": {"display_warning": "(Please select an entity)", "enddate": "To", "entity": "Entities", "information_message": "(Ability to select those with an active service plan on these dates)", "interventiontype": "Intervention type", "professional": "Professional", "startdate": "From", "success": "Availability successfully added", "type": "Availability type", "volunteer": "Volunteer", "disclaimer": "If you wish to enter 24 hours,<br/>please enter the next day after \"To [...]\""}, "title": "I want to be available", "title_non_editable": "My availabilities", "titlemobile": ""}, "prestationpopup": {"closebutton": "Close", "enddate": "To", "entity": "Entity", "interventiontype": "Intervention type", "position": "Position", "serviceplan": "Service plan", "startdate": "From", "title": "Assignment"}, "show_prestations": "Show prestations", "title": "My planning and availabilities"}, "backup-management": {"export": "", "title-activation": "", "title-export": "", "reload": {"success": "The report generation has been taken into account. Please check the dedicated folder"}}, "chart": {"legend": {"ambulance": "Ambulance", "availability": "Reals Availabilities", "commandment": "Commandment", "complete": "Complete", "degraded": "Degraded", "dms": "DMS", "empty": "Empty", "fire": "Fire", "gis": "GIS", "incomplete": "Incomplete", "intervention": "Interventions", "mobile": {"ambulance": "Amb.", "commandment": "Commandment", "complete": "Comp.", "degraded": "Deg.", "dms": "DMS", "empty": "Emp.", "fire": "Fire", "gis": "GIS", "incomplete": "Incomp.", "nodata": "No data", "others": "Others", "partial": "Par.", "samu": "SAMU"}, "nodata": "No data", "others": "Others", "partial": "Partial", "prestation": "Prestations", "samu": "SAMU", "total": "", "totals": "Total"}}, "current-situation": {"active-backup": "Active Backup ", "add-person": "+ ", "els-status": "Stat. veh.", "fromto": "From {{startDateTime}} to {{endDateTime}}", "legend": {"barracked": "Barracked", "pro": "Professional"}, "person-not-found": "No person available", "persons": "Available persons", "ressources": "Resources", "schedule": "Schedule", "service-plan": "Service Plan", "status": "Last statut: {{date}}, statut {{statut}}", "time-slot": "Time slot", "title": "Current situation"}, "dashboard": {"default": {"welcome": "Welcome to CGDIS Portal"}, "manager": {"all-service-plan": "My service plans", "new-service-plan": "New service plan", "other-service-plan": "Others service plans", "occupancy-rate": "Occupancy rate", "subtitle": "of all my manual service plans", "title": "Occupancy rate", "today": "Today"}, "members": {"chart": {"ambulance": "Secours à personne", "ambulance-mobile": "Ambulance", "availability": "Availability", "available-hours": "Hours availables", "commandment": "Commandment", "commandment-mobile": "Commandment", "dms": "<PERSON><PERSON><PERSON>", "dms-mobile": "DMS", "exclusive": "Exclusive", "fire": "Incendie/Sauvetage", "fire-mobile": "INCSA", "gis": "Groupes d'intervention spécialisés", "gis-mobile": "GIS", "mobile": {"ambulance": "Amb.", "availability": "Avail.", "commandment": "Com.", "dms": "DMS.", "exclusive": "Exc.", "fire": "Fire", "gis": "GIS", "others": "Other", "planning": "Planning", "professional": "Professional serv.", "samu": "SAMU", "tooltip": "Hours worked"}, "noData": "No data", "other": "Other", "others": "Other", "others-mobile": "Autres", "planning": "Planning", "professional": "Professional service", "professional-mobile": "Pro. Serv.", "samu": "SAMU", "samu-mobile": "SAMU", "tooltip": "Hours worked"}, "summary": {"availability": "Avail:", "month": "This month", "perm": "Perm:", "week": "This week"}, "title": "My planning & availabilities"}, "news": {"button": "See more", "title": "News"}}, "permamonitor": {"title": "Permamonitor", "title-admin": "Permamonitor Admin", "title-pds-config": "Permamonitor PDS Configuration", "title-poj-config": "Permamonitor POJ Configuration", "title-deployment-plan-create": "Permamonitor POJ Configuration", "title-deployment-plan-update": "Édition d'un Plan d'Armement", "category-selector-label": "Select category", "pds-version-selector-label": "Select PDS version", "pds-entity-selector-label": "Select an entity", "filter": {"title": "Filtres", "label": {"is-primary": "Favorites", "zone": "Zones", "period": "Period", "evolution": "Evolution", "actual-period": "Live", "is-sauvetage": "Type", "type": "Type", "localisation": "Localisation", "all-selector": "All"}}, "admin": {"pds": {"table": {"toasts": {"added-favorite": "Le PdS a bien été marqué en favoris", "deleted-category": "Le PdS n'est plus associé à la catégorie", "removed-favorite": "Le PdS a bien été retiré des favoris", "updated-category": "Le PdS a bien été associé à une catégorie", "updated-subcategory": "Le PdS a bien été associé à une sous-catégorie"}, "pds": "Pds", "category": "Category", "subcategory": "Sous-categorie", "favorite": "<PERSON><PERSON><PERSON>", "favorite-true": "O<PERSON>", "favorite-false": "Non", "update-success": "Plan de service modifiée avec succès"}}, "pa": {"create-form": {"toast": "Le Plan d'Armement a été créé!", "loading": "Création et copie de la dernière configuration en cours - cela peut prendre un peu de temps", "name": "Nom", "description": "Description", "back": "Annuler", "start-date": "Date de début"}, "update-form": {"toast": "Le plan d'armement a bien été mise a jour.", "toast-warning": "Attention, la modification peut entraîner des modifications de configuration. Pensez à les vérifier!", "name": "Nom", "description": "Description", "back": "Annuler", "start-date": "Date de début"}, "table": {"version-number": "Numéro version", "description": "Description", "name": "Nom", "start-date": "Date début", "end-date": "Date fin", "actions": "Actions"}}, "poj": {"popup": {"copy": {"success": "The slot(s) have been successfully copied", "entity": {"title": "Configuration copy", "fromentity": "Source entity:", "toentity": "Target entity:", "success": "The slots have been successfully copied", "warning": "Warning: copying the configuration will overwrite the existing configuration for the target entity."}}, "title": "<PERSON><PERSON>", "toast": "Copier avec succès!", "startTime": "<PERSON><PERSON><PERSON> le <PERSON>ré<PERSON>u", "endTime": "Jusqu'au créneau", "creation": {"title": "Configuration creation", "titlenoeditright": "Configuration missing", "message": "No configuration exists for this entity.<br/><br/>On confirmation, a configuration will be created and initialised at 0 for this entity.<br/><br/>Please take this message into account and modify the parameters if necessary.", "messagenoeditright": "No configuration exists for this entity.", "yes": "Confirm", "yesnoeditright": "Close", "no": "Cancel"}}, "filters": {"zones-title": "Zones", "groups-title": "Groups", "cis-title": "CIS/GIS", "gis-title": "GIS"}, "table": {"no-cis-note": "Aucune configuration CIS/GIS existante", "time-title": "Tranche horaire", "days": {"monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>", "sunday": "<PERSON><PERSON><PERSON>", "holidays": "Jours Fériés"}}, "update-success-toast": "Valeur changée!", "group-overriden-update-success-toast": "The value has been reset to the sum of the CIS values", "zone-overriden-update-success-toast": "The value has been reset to the sum of the Group and CIS values.", "maxvalue-success-toast": "Max value is {{maxValue}}"}}, "view": {"table": {"information": {"title": "Slot information", "box": {"chef-de-section": "Chef de Section", "maschiniste": "Chef de Section", "chef-binome-1": "Chef de Section", "equipier-binome-1": "Chef de Section", "chef-binome-2": "Chef de Section", "equipier-binome-2": "Chef de Section"}}}, "map": {"information": {"title": "Information on hover..."}, "back-to-zones-button": "Back to zones", "no-config-available": "Aucune configuration existante", "statuses": {"optimal": "Acceptable", "critical": "Critique", "ideal": "Optimal", "unacceptable": "Non-Acceptable"}}, "poj": {"table": {"date": "Date", "armed": "ARM ", "armedAverage": "MOY", "armedMin": "MIN", "pojPercent": "%"}, "chart": {"next24": "Next 24h", "last7": "Last 7 days", "next7": "Next 7 days", "last30": "Last 30 days"}}}}, "rici": {"title": "RICI 2.0", "common": {"validate-cancel-buttons": {"label-validate": "Valider", "label-cancel": "Annuler"}}, "sim-list": {"title": "Gestion des cartes Sim", "table": {"headers": {"iccid": "ICCID", "msisdn": "MSISDN", "pin": "PIN", "status": "Status", "pager-id": "PagerID", "operations": "Opérations"}}}, "popup": {"delete": {"title": "Suppression - Carte SIM", "note": "Veuillez confirmer la suppression de la carte SIM", "number": "<PERSON><PERSON><PERSON><PERSON>"}, "deactivate": {"title": "Suppression impossible - désactiver la carte SIM", "last-pager-id": "Ancien pagerID associé"}, "reactivate": {"title": "Réactiver la carte SIM?", "last-pager-id": "Ancien pagerID associé"}, "upload": {"title": "Import CSV - Carte(s) SIM", "note": "Veuillez déposer votre fichier à importer"}}, "ranges": {"list": {"title": "Gestion des RIC Ranges", "headers": {"name": "Nom de la plage RIC", "type": "Type de Plage", "start": "D<PERSON>but", "end": "Fin", "allocation": "Allocation", "rate": "Taux d'occupation", "actions": "Actions"}}, "add": {"title": "Création d'une plage RIC"}, "edit": {"title": "Edition d'une plage RIC"}, "single": {"title": "Plage RIC"}, "forms": {"update-form": {"toast": "RIC Range updated successfully."}}, "popup": {"validate": {"title": "Validate RIC Range", "message": "This RIC range is associated with alert groups. Validating will trigger pager programming. Do you want to continue?", "success-toast": "RIC Range validated and pager programming triggered."}}}, "schemas": {"form": {"edit": {"title": "Edit a RIC Schema", "toast": "RIC Schema updated successfully."}}, "popup": {"validate": {"title": "Validate RIC Schema", "message": "This RIC schema is associated with alert groups. Validating will trigger pager programming. Do you want to continue?", "success-toast": "RIC Schema validated and pager programming triggered."}}}}, "date": {"days": {"friday": "friday", "monday": "monday", "saturday": "saturday", "sunday": "sunday", "thursday": "thursday", "tuesday": "tuesday", "wednesday": "wednesday"}, "duration": {"dashboard": "{{hours}}h{{minutes}}", "dayshoursminutes": "{{days}}j {{hours}}h {{minutes}}m", "hours": "{{hours}}h", "hoursminutes": "{{hours}}h {{minutes}}m", "minutes": "{{hours}}h {{minutes}}m", "hoursminutesseconds": "{{hours}}h {{minutes}}m {{seconds}}s", "monthsdayshoursminutes": "{{months}}m {{days}}j {{hours}}h {{minutes}}m", "yearsmonthsdayshoursminutes": "{{years}}a {{months}}m {{days}}j {{hours}}h {{minutes}}m"}, "months": {"1": {"abr": "jan.", "full": "january"}, "2": {"abr": "feb.", "full": "february"}, "3": {"abr": "mar.", "full": "march"}, "4": {"abr": "apr.", "full": "april"}, "5": {"abr": "may", "full": "may"}, "6": {"abr": "jun.", "full": "june"}, "7": {"abr": "jul.", "full": "july"}, "8": {"abr": "aug.", "full": "august"}, "9": {"abr": "sept.", "full": "september"}, "10": {"abr": "oct.", "full": "octobre"}, "11": {"abr": "nov.", "full": "november"}, "12": {"abr": "dec.", "full": "december"}}, "two-columns-view": {"close": "<PERSON><PERSON><PERSON> le calendrier", "open": "<PERSON><PERSON><PERSON><PERSON> le calendrier"}}, "default": {"button": {"back": "Back", "cancel": "Cancel", "delete": "Delete", "logas": "Connect to logas", "scan": "Scan QR code", "submit": "Submit", "validate": "Validate"}, "construction": {"message": "Your app is growing up. Come back soon.", "title": "In building"}, "day_more": "D+1", "expansion-filters": {"title": "Filters"}, "no": "No", "page": {"accessdenied": "You don't have access to this page", "notfound": "Page not found"}, "placeholder": {"search": "Search...", "select-time-slot": "Select a time slot"}, "popup": {"fromto": {"from": "Start date", "to": "End date"}}, "search": "Search", "table": {"noresult": "No data to display", "total": "result(s)"}, "yes": "Yes"}, "entities": {"empty": "Without assignment", "type": {"CIS": "EMERGENCY CENTER", "CS": "SUPPORT CENTER", "GROUP": "GROUPS", "GS": "SPECIAL GROUPS", "NATIONAL": "NATIONAL", "SAMU": "SAMU", "UNKNOWN": "UNKNOWN", "ZONE": "ZONE", "DIRECTION": "DIRECTION", "MY_ENTITIES": "MY SP"}}, "error": {"badgateway": "Timeout occurred. Please contact your administrator", "entity": {"export": "You haven't the rights to export persons for this entity", "update": "Can not update the type of this entity because it's parent of other ones"}, "export": {"default": "Error occurs during export generation", "no_data_to_export": "Please, select at least one type of data to export", "no_export_type": "Please, select at least one type of export"}, "form": {"permamonitor-poj-copy-id": {"ComparableAfterAnother": "L'heure choisi doit être après l'heure de début"}, "DatetimeAfterAnother": "The end date should be after start date", "DatetimeNotEquals": "Dates should not be identical", "EmailWithTld": "Wrong email address", "cron": {"minutes": "Please select a minute (mot 0)", "hours": "Please select hour", "days": "Please select at least 1 day"}, "Emails": "Please, enter valid email addresses", "FunctionOperationalTagCreateName": "Name is already used", "FunctionOperationalTagUpdateName": "Name is already used", "FutureDate": "Please select a future date", "FutureDatetime": "Please, select a date in the future", "Issi": "Wrong ISSI number", "NotBlank": "Mandatory field", "NotEmpty": "Mandatory field", "NotNull": "Mandatory field", "Pager": "Wrong pager number", "PagerMobile": "Wrong pager mobile number", "PhoneNumber": "Wrong phone number", "RequiredIfBooleanValue": "Mandatory field", "ServicePlanArmingDelayRange": "The value of armament delay must be between 60 and 900 seconds", "ServicePlanArmingDelayRangeBarracked": "The value of armament delay must be between 60 and 900 seconds", "ServicePlanArmingDelayRangeNotBarracked": "The value of armament delay must be between 60 and 1200 seconds", "ServicePlanMinimumArmingDelayRangeBarracked": "The value of armament delay must be between 60 and 900 seconds", "ServicePlanModelFullEditable": "Field is mandatory", "ServicePlanModelVersionUniqueLabel": "The name of the model version already exists", "ServicePlanUniqueLabel": "The name of the service plan already exists", "ServicePlanUpdateModel": "The selected model is incompatible with this service plan. The start or end dates of the model do not match that of the service plan. Please select another model or correct the date (s).", "ServicePlanUpdateModelWithPrestations": "Cannot modify the model as long as prestations are planned on this service plan", "ServicePlanVehicleMandatory": "Mandatory field", "ServicePlanVersionUniqueLabel": "The label of the version already exists", "ServicePlanVersionUniqueStartdate": "A version already exists with this start date", "Size": "The size must be between {{args1}} and {{args0}}", "SplitSlotTimeValidator": "You should select a valid hour", "UpdateServicePlanArmingDelayRangeBarracked ": "The value of armament delay must be between 60 and 900 seconds", "UpdateServicePlanArmingDelayRangeNotBarracked": "The value of armament delay must be between 60 and 1200 seconds", "VolunteerAvailabilityCheckAssignment": "This person is not assigned to some entities between these dates and for this availability type", "VolunteerAvailabilityUpdateStartDate": "The start date must be in the future or after previous start date", "PermDeploymentPlanNameExists": "Le nom du plan d'armement doit être unique.", "PermDeploymentPlanStartTimeUpdate": "La nouveau date de dépard doit être après la date de création du dernier plan d'armement et avant la date du prochain.", "PermDeploymentPlanCreationDate": "La date de début doit être après la date de début du dernier plan d'armement existant.", "availability-full-calendar-popup": {"DatetimeAfterAnother": "The end date should be after start date", "DatetimeNotEquals": "Times should not be the same"}, "datetimeDateNotNull": "Date is mandatory", "invalid": "The form is not valid", "max": "The value is too large", "maxlength": "Please enter a maximum of {{requiredLength}} characters", "min": "The value is too small", "minlength": "Please enter at least {{requiredLength}} characters", "required": "Mandatory field", "service-plan": {"split": {"inpast": "The split can't be in the past", "notinslotrange": "The split time must be after the slot start time and before the slot end time."}, "team": {"label": {"exist": "The team name already exists for this service plan"}, "members": {"duplicate": "You cannot choose one person for several positions"}, "shortlabel": {"exist": "The short label already exists for this service plan"}}}}, "person": {"already_logas": "", "export": "The export period is too long (max 6 months)", "no_assignments": "This person is not assigned to any entity, his contact information is not modifiable."}, "prestation": {"allowance": {"configuration": {"pastorcurrent": "Configuration is past or current", "samestartdate": "Configuration already exist woth this start date"}}, "copy": "Cannot copy this or these prestations, because the version of the service plan or model is different on the desired date", "exclusive": "These prestations cannot be created. One or more people are already assigned to an exclusive service plan.", "nothing-copy": "No prestation to copy"}, "service_plan": {"assignteam": {"empty": "The team is empty"}, "box-free": "The selected box is already used by another service plan", "person": {"already_assigned": "This person is already assigned to this time slot"}, "timeslot": {"merge": {"default": "Slots merge has not been performed", "service_plan_different": "Slots merge has not been performed: slots are one different service plan", "slot_already_started": "Slots merge has not been performed: slot already started"}}, "vehicle-free": "The selected vehicle is already used by another service plan", "version": {"copy": "The version could not be copied", "exist_with_start_date": "A version already exists with this start date"}}, "service_plan_model": {"update": {"forbidden": "Update this service plan model is not possible"}, "version": {"copy": "The version could not be copied", "dates": "Cannot update this version with these dates because they are inconsistent with one or more previous versions", "exist_with_start_date": "A version already exists with this start date", "notfound": "No version exists for the selected service plan model"}}, "technical_error": "Technical error. contact your administrator", "timeout": "The request is taking too long to complete or you have been disconnected. If the problem persists, please reconnect", "userrights": {"delete": "This permission is inalienable to the role. Can not delete it"}, "volunteeravailability": {"delete": {"prestations_already_assigned": "Can not delete availability: at least one service exists"}, "duplicate": {"error": "From {{startDay}}/{{startMonth}}/{{startYear}} at {{startHour}}:{{startMinute}} to {{endDay}}/{{endMonth}}/{{endYear}} at {{endHour}}:{{endMinute}}", "message_pro": "A similar professional availability already exists. Please, edit or delete it.", "message_vol": "A similar volunteer availability already exists. Please, edit or delete it.", "title": "Warning"}, "update": {"prestations_already_assigned": "Unable to update the availability: at least one service exists"}}, "rici": {"simcard": {"alreadyassociated": "The SIM card is already associated to a pager, please select another one", "alreadyexists": {"ICCID": "A SIM card with this ICCID already exists.", "MSISDN": "A SIM card with this MSISDN already exists."}}, "pager": {"alreadyexists": {"SERIAL_NUMBER": "The serial number is already present in the system, please enter a new one.", "PAGER_ID": "The Pager ID is already present in the system, please enter a new one."}}, "person": {"already_has_pager_assigned": "The user already has a pager assigned, please select a new one."}, "ric": {"already_used": "The RIC is already used.", "already_used_by_alert_group": "The RIC is already used by an alert group.", "is_not_number": "The RIC value is not a valid number.", "is_divisible_by_8": "The RIC value cannot be divisible by 8.", "outside_of_entity_range": "The RIC value is outside of the entity range.", "no_available_ric_in_range": "No available RIC found in any of the ranges for this entity.", "no_active_range_for_entity": "No active RIC ranges found for this entity."}}}, "error-management": {"delete": {"success": "The message has been deleted"}, "list": {"header": {"actions": "Actions", "cause": "Cause", "creationdate": "Date", "message": "Message", "objectidentifier": "ID", "status": "Status", "type": "Type"}}, "resend": {"error": "Error during refresh", "success": "Error successfully refreshed"}, "status": {"CREATED": "Created", "DONE": "Done", "RESENT": "Resent"}, "title": "Error management", "type": {"ASSIGNEMENT_QUEUE_NOTIFICATION": "Assignment queue notification", "DELETE_ONE_VEHICLE_HISTORIC": "Delete expired vehicle status", "ERROR_MESSAGE_TO_IGNORE": "Error message to ignore", "INTERNAL_PROCESS": "Internal error", "NOTIFIER_EMAIL": "Email notification", "NOTIFIER_MANAGER": "Notification manager", "PROCESS_DELETE_BACKUP_GROUPS": "Delete backup groups (Process)", "PROCESS_DELETE_PERSON_FROM_MSNAV": "MSNAV - Person deletion (process)", "PROCESS_DELETE_PERSON_IN_ELS": "ELS - Person deletion (gateway)", "PROCESS_DELETE_PERSON_IN_LEVESO": "LEVESO - Person deletion (gateway)", "PROCESS_EXPORT_BACKUP_112": "Export backup groups (Process)", "PROCESS_EXPORT_MAIL": "Export mail", "PROCESS_INSERT_APTITUDES_FROM_XLSX": "Create aptitudes from XLSX file (Process)", "PROCESS_LUXDOK_INSERT_INTERVENTION": "LUXDOK - Insert intervention (process)", "PROCESS_READ_APTITUDES_FROM_XLSX": "Read aptitudes from XLSX file (Process)", "PROCESS_REFRESH_BACKUP_GROUPS": "Backup groups update (process)", "PROCESS_REFRESH_ENTITIES": "Entities update (process)", "PROCESS_REFRESH_PERSON_ASSIGNMENTS": "Refresh people assigments (process)", "PROCESS_REFRESH_VEHICLE": "Vehicles update (process)", "PROCESS_SERVICE_PLAN_EXPORT_CSV": "CSV Export of service plans (process)", "PROCESS_SERVICE_PLAN_EXPORT_ONE_CSV": "CSV Export of service plans (process)", "PROCESS_SERVICE_PLAN_SEND_FILLING_EXPORT": "Process Service Plan Send Filling Export", "PROCESS_SERVICE_PLAN_UPDATE": "ELS - Service plan update (process)", "PROCESS_SERVICE_PLAN_UPDATE_ALL": "ELS - All service plans update (process)", "PROCESS_UPDATE_APTITUDES_FROM_GEPS": "GEPS - Aptitudes update", "PROCESS_UPDATE_ENTITY_LEVESO": "LEVESO - Update entity (Process)", "PROCESS_UPDATE_PERSON_ELS": "ELS - People update (process)", "PROCESS_UPDATE_PERSON_FROM_MSNAV": "MSNAV - People update from MSNAV (process)", "PROCESS_UPDATE_PERSON_GEPS": "GEPS - People update", "PROCESS_UPDATE_PERSON_LEVESO": "LEVESO - Update person (Process)", "PROCESS_UPDATE_PERSON_LUXDOK": "LUXDOK - People update", "PROCESS_UPDATE_PERSON_MSNAV": "MSNAV - People update to MSNAV (process)", "PROCESS_UPDATE_PERSON_PAGERMGMT": "Update person's pager (Process)", "PROCESS_UPDATE_REMARKS_FROM_GEPS": "GEPS - Remarks update", "REFRESH_BACKUP_GROUPS": "Backup groups update", "REFRESH_ENTITIES": "Entities update", "REFRESH_ONE_BACKUP_GROUPS": "Refresh Backup Groups", "REFRESH_VEHICLES": "Vehicles update", "SEND_ALL_ASSIGNMENT_CLOSURE": "Send Closure Notifications for Assignments", "SEND_ALL_PRESTATION_QUEUE": "Send Notifications for Pending Prestations", "SEND_ONE_PRESTATION_QUEUE": "Send Notification for a Pending Prestation", "SERVICE_PLAN_DELETE": "ELS - Service plan deletion", "SERVICE_PLAN_OPTIONAL_BACKUP_PUSH": "ELS - Envoi de la suppression des groupes de backup et optionnel", "SERVICE_PLAN_PUSH": "ELS - Service plan update", "SMSI_EXPORT": "SMSI Export", "SMSI_SERVICE_PLAN_FILLING": "Fill Service Plan with SMSI", "UPDATE_ENTITY_LEVESO": "LEVESO - Update Entity", "UPDATE_PERSON_ELS": "ELS - People update", "UPDATE_PERSON_GEPS": "GEPS - People update", "UPDATE_PERSON_LEVESO": "LEVESO - Update Person", "UPDATE_PERSON_LUXDOK": "LUXDOK - People update", "UPDATE_PERSON_MSNAV": "MSNAV - People update", "UPDATE_PERSON_PAGERMGMT": "Update Person's Pager", "UPDATE_VEHICLE_TACTICAL_STATE": "Update Vehicle Tactical State to ELS", "UPDATE_VEHICLE_TACTICAL_STATE_INPUT": "Update Vehicle Tactical State by ELS", "GET_SLOTS_FOR_ELS": "Get slots for ELS", "ONE_PRESTATIONS_ALLOWANCE": "Generate allowance", "PROCESS_DELETE_BOX": "Delete box", "PROCESS_DELETE_ONE_BACKUP_GROUP": "Delete backup group", "PROCESS_FUNCTION_OPERATIONAL_STATUS_UPDATE": "Update function operational status", "PROCESS_REFRESH_BOX": "Update box (process)", "PROCESS_VEHICLE_UPDATE_NOTICE": "Update vehicle notice to ELS (process)", "REFRESH_BOXES": "Update boxes", "UPDATE_VEHICLE_NOTICE_ELS": "Update vehicle notice to ELS"}}, "file": {"upload": {"error": {"title": "File {{fileName}} ignored", "size": "The file is too big The size must be in {{minSize}} MB and {{maxSize}} Mb", "type": "The file is of the wrong type.<br/>The authorised types are: {{allowedTypes}}", "maxitems": "The maximum number of items is {{maxItems}}."}}}, "general_availability": {"legend": {"barracked": "Barracked", "professional": "Professional availability", "volunteer": "Volunteer availability"}, "no_data": "No data for this day", "title": "Availability of the week"}, "general_information": {"active_assignment": {"list": {"allClosedAssignment": "Show closed assignments", "endDate": "End date", "entityName": "Name", "primaryType": {"list": {"PRIMARY": "Primary", "SECONDARY": "Secondary", "TECHNICAL": "Technical"}, "title": "Primary type"}, "startDate": "Start date", "type": {"list": {"EXT": "EXT", "PRO": "PRO", "VOL": "VOL"}, "title": "StatuS"}}, "title": "Active assignments"}, "activity": {"allowance": "Real allowance", "allowance_mobile": "Real allow", "availabilities": {"duration": "Duration", "end": "End date", "entities_availability": "Entities", "entities_availability_with_barracked": "Entities / Barracked", "intervention_types": "Intervention types", "start": "Start date", "start_mobile": "Start", "title": "Availabilities"}, "day": "DAY", "detail": {"availabilities": "Reals availa.", "availabilities_mobile": "", "interventions": "Interventions", "interventions_mobile": "Interventions", "prestations": "Reals prestations", "prestations_mobile": "Reals presta.", "totals": "Total"}, "hours": "HOURS", "interventions": {"alarm": "Date", "end": "End date", "end_mobile": "End", "function_name": "Function name", "keyword": "Keyword", "mission_number": "Mission number", "number": "Number", "number_mobile": "<PERSON><PERSON>", "radio": "Radio call name", "start": "Start date", "start_mobile": "Start", "title": "Interventions"}, "month": "Month", "periode": "Periode", "prestations": {"duration": "Compensated duration", "end": "End date", "end_mobile": "", "portal_label": "Service plan", "position_label": "Position", "realDuration": "Real duration", "realDuration_mobile": "Real durat.", "start": "Start", "start_mobile": "Start", "title": "Prestations"}, "range": "Range", "semester": "<PERSON><PERSON><PERSON>", "title": "My Activity", "title_logas": "Activity", "totals": {"allowance": "Allowance", "allowance_mobile": "Allow", "details": {"barrackedAmount": "Barracked", "entity": "Entity", "notBarrackedAmount": "Not Barracked", "proDuration": "Professional duration", "proDuration_mobile": "Pro. duration", "realDuration": "Real duration", "sum": "Sum", "total": "Total", "volDuration": "Volunteer duration", "volDuration_mobile": "Vol. duration"}, "duration": "Duration", "label": "Label", "nbPerson": "", "title": "Totals"}, "type": "Status", "week": "Week", "year": "Year"}, "address": {"city": "City", "country": "Country", "number": "N°", "street": "Street", "subtitle": "Address {{number}}", "title": "Postal address", "zip_code": "Postal Code"}, "aptitudes": {"list": {"aptitude": "Aptitude", "description": "Description", "endDate": "End date", "isImported": "This datas were imported. For further informations, please contact DMS-STP secretary", "startDate": "Start date", "status": {"title": "Status"}, "userComment": "Comment"}, "title": "Aptitudes"}, "availability": {"type": {"ambulance": "Secours à personne", "commandment": "Commandment", "dms": "<PERSON><PERSON><PERSON>", "fire": "Incendie/Sauvetage", "gis": "Groupes d'intervention spécialisés", "other": "Other", "others": "", "samu": "SAMU"}}, "bank_account": {"bank_code": "Bank code", "bic": "BIC", "iban": "IBAN", "title": "Bank details"}, "contact_information": {"alert_email": "Send email alert on", "alert_email_notification": "Send email notification on", "alert_email_error_notification": "Use an other email for error notification", "alert_email_error_notification_email": "Email for error notification", "alert_mobile": "Send mobile alert on", "general": "Contacts", "operational": "Operational Contacts", "private": {"email": "Private email", "mobile": "Private mobile", "phone": "Private phone"}, "professional": {"email": "Professional email", "mobile": "Professional mobile", "phone": "Professional phone"}, "ric": "RIC", "ricMobile": "RIC Mobile", "warning_no_mail": "Warning ! There is no mail ! Please enter one email adress!", "warning_no_mail_mobile": "No mail", "warning_one_mail": ""}, "diplomas": {"list": {"comment": "Comment", "ident": "Identification", "name": "Name", "type": "Type", "validFrom": "<PERSON>id from", "validUntil": "Valid until"}, "title": "Diplomas"}, "driver_license": {"list": {"category": "Category", "endDate": "End date", "startDate": "Start date"}, "title": "Driving licenses"}, "function_operational": {"title": "Operational functions"}, "general": "General", "managerial_occupations": {"list": {"header": {"allClosedOccupations": "Show closed occupations", "assignment": "Assignment", "assignment_description": "Assignment description", "code": "Code", "endDate": "End date", "external": "External", "external_desktop_reduced": "", "external_mobile": "Ext", "label": "Name", "nomination": "Nomination date", "professional": "Professional", "professional_desktop_reduced": "PRO", "professional_mobile": "Pro", "startDate": "Start date", "status": {"list": {"ONGOING": "Ongoing", "RESIGNED": "Resigned", "REVOKED": "Revoked", "SUSPENDED": "Suspended"}, "title": "Status"}, "type": "Type", "volunteer": "Volunteer", "volunteer_desktop_reduced": "VOL", "volunteer_mobile": "VOL"}}, "title": "Managerial employment"}, "medical": "Medical Information", "medical-information": {"legend": {"apt": "Apt", "apt_with_restriction": "Apt with restrictions", "inapt": "Inapt", "temporary_inaptitude": "Temporary inaptitude"}}, "medical-report": {"aptitude": "Aptitude", "empty": "No report", "new-aptitudes": "New aptitude(s) ", "new-restrictions": "New restriction(s)", "no-aptitude": "No aptitude ", "no-restriction": "No restriction ", "remark": "Remark(s) :", "restriction": "Restriction", "start": "Start", "statut": "Statut", "title": "Medical report of {{ publicationDate }}"}, "operational": "Operational", "operational_contact": {"email": "Email", "issi": "", "mobile": "Mobile", "pager": "Pager", "pager_mobile": "Pager mobile", "phone": "", "send_alarm_clock": "Send Alarmdepesch"}, "operational_dates": {"list": {"header": {"endDate": "End Date", "startDate": "Start Date", "type": {"list": {"PROFESSIONAL": "Professional", "VOLUNTEER": "Volunteer"}, "title": "Type"}}}, "title": "Operationals Dates"}, "operational_functions": {"list": {"granted": "This function is not authorized for this center", "label": "Operational functions"}, "title": "Operational functions"}, "operational_grades": {"list": {"header": {"allClosedGrade": "", "allClosedGrades": "Show closed grades", "code": "Code", "decreeDate": "(de)Decree Date", "echelon": "Echelon", "endDate": "(de)End date", "gradeType": {"list": {"DMS": "DMS", "GIS": "GIS", "INCSASAP": "INCSA/SAP"}, "title": "Status"}, "professional": "Professional", "professional_desktop_reduced": "PRO", "professional_mobile": "PRO", "psupp": "Support firefighter", "samu": "<PERSON><PERSON>", "samu_mobile": "EXT", "startDate": "(de)Start date", "title": "Title", "volunteer": "Volunteer", "volunteer_desktop_reduced": "VOL", "volunteer_mobile": "VOL"}}, "title": "Operational Grades"}, "operational_occupations": {"list": {"header": {"code": "Code", "endDate": "End date", "entity": "Entity", "external": "External", "external_desktop_reduced": "", "external_mobile": "Ext", "label": "Name", "nomination": "Nomination date", "professional": "Professional", "professional_desktop_reduced": "PRO", "professional_mobile": "Pro", "startDate": "Start date", "status": {"list": {"ONGOING": "Ongoing", "RESIGNED": "Resigned", "REVOKED": "Revoked", "SUSPENDED": "Suspended"}, "title": "Status"}, "type": "Type", "volunteer": "Volunteer", "volunteer_desktop_reduced": "VOL", "volunteer_mobile": "VOL"}}, "title": "Operational employment"}, "operational_volunteer_internship": {"duration": "Duration", "endDate": "End date", "startDate": "Start date", "title": "Operational Volunteer internship"}, "operational_young_firefighter": {"firstName": "First name", "first_tutor": "First tutor", "lastName": "Last name", "parental_consent": "Parental consent", "phoneNumber": "Phone number", "second_tutor": "Second tutor", "title": "Operational Young Firefighter"}, "person_medical_information": {"remark": "Remark", "title": "Medical information"}, "personal_information": {"birthdate": "Birth date", "birthplace": "Place of birth", "both": "All", "cgdisrn": "CGDIS registration number", "code": "Naming code", "firstname": "First name", "foreign": "Foreign ID number", "gender_f": "Female", "gender_i": "Undefined", "gender_m": "Male", "hiring": "CGDIS start date", "iam": "IAM number", "is_candidate": "Candidate", "is_external": "Extern", "is_intern": "Intern", "is_operational_firefighter": "Operational firefighter", "is_operational_firefighter_short": "<PERSON>.ope", "is_professional": "Professional", "is_professional_adm_tech": "Administrative and technical professional", "is_professional_adm_tech_short": "Pro.admTech", "is_professional_operational": "Operational professional", "is_professional_operational_short": "Pro.ope", "is_professional_tech": "Technical professional", "is_retired": "Retired", "is_samu": "<PERSON><PERSON>", "is_support_firefighter": "Support firefighter", "is_support_firefighter_short": "<PERSON><PERSON>sup", "is_technical": "Technical", "is_veteran": "Veteran", "is_volunteer": "<PERSON><PERSON><PERSON>", "is_volunteer_tech": "Technical volunteer", "is_young_firefighter": "Young firefighter", "is_young_firefighter_short": "Y.firefighter", "lastname": "Last name", "nationalRegistrationNumber": "", "person_title": "Title", "rnrpp": "RNRPP number", "sex": "Sex", "status": "Status", "tic": "TIC", "title": "Personal information", "vacation": "CGDIS end date", "hiring_date": "Hiring date in CGDIS"}, "restrictions": {"list": {"description": "Description", "startDate": "Start date", "startDate_mobile": "Start", "type": "Type", "userComment": "Comment"}, "title": "Restrictions"}, "roles": {"title": "Roles in the application"}, "suspension": {"list": {"endDate": "End date", "startDate": "Start date"}, "title": "Suspensions"}}, "i18n": {"error": {"timeout": "Timeout"}}, "intervention": {"types": {"ambulance": "Rescue to person", "ambulance_mobile": "Ambulance", "commandment": "Commandment", "commandment_mobile": "COM", "dms": "DMS", "dms_mobile": "DMS", "fire": "Fire", "fire_mobile": "Fire", "gis": "GIS", "gis_mobile": "GIS", "others": "Others", "others_mobile": "Others", "samu": "SAMU", "samu_mobile": "SAMU"}}, "layout": {"navigation": {"footer": {"copyright": "{{year}} Corps Grand-Ducal Incendie & Secours. All rights reserved.", "copyrightmobile": "{{year}} CGDIS. All rights reserved.", "link": {"conditions": "Terms of use", "confidential": "Confidentiality"}}, "menu": {"items": {"admin": {"box": "Boxes", "boxes": "", "entities": "Entities", "export": "Export Profiles", "function_operational": "F.O. Management", "function_operational_order": "F.O. Order Management", "general-message": "General messages", "position_template": "F.O. Position templates Management", "public-holiday": "Public holiday", "service_plan": "Service plans", "service_plan_model": "Service plan models", "fotags": "F.O. Tags Management", "vehicles": "Vehicles"}, "administration": "Administrative", "allowance": {"configuration": "Configuration", "search": "Allowances global view", "settings": "Allowance configuration", "title": "Allowance management"}, "audit": {"list": {"empty": "No data", "title": "Audit"}}, "backup-112-management": "", "current_situation": "Current situation", "fos": "Operational Functions", "my_planning": "Edit my availabilities", "news": "News", "optional_backup_group_management": {"list": {"empty": "", "title": ""}, "title": ""}, "organizational": "Organizational", "people_management": {"function": "Operational Functions Management", "list": {"empty": "No visible member", "title": "Members list of the entity"}, "title": "People management", "volunteer_availabilities_logas": "Manage members availabilities"}, "people_medical_information": {"list": {"empty": "No visible member", "title": "Member medical information viewing"}, "title": "Member medical information viewing"}, "performances": "My Planning & availabilities", "permamonitor": {"menu": "Permamonitor", "dashboards": "Dashboards", "administration": "Administration", "config": "Configuration"}, "plan_travail": "My Workplans", "service_plan": "My Service plans", "sync": {"audit_management": "Audit management", "error_management": "Error management", "logas": "Logas access", "scheduler_management": "Scheduler management", "title": "System"}, "user_rights": {"permissions": "Permissions", "roles": "Roles", "title": "User rights"}, "vehicules": "My Vehicles", "rici": {"menu": "RICI"}}, "title": "<PERSON><PERSON>"}, "profile": {"myplanning": "My Planning", "mypreferences": {"assignment_notification": "Activate assignment notifications:  ", "assignment_notification_mobile": "Assignment notifcation:  ", "availability_notification": "Activate availability notifications on incomplete slot:  ", "availability_notification_mobile": "Availability notification:  ", "calendar_days": "Number of days by week:", "closed_calendar": "Hidden calendar by default:", "error_notification": "Activate error notifications:  ", "error_notification_mobile": "Error notifications:  ", "export-person-full": "Global entity's person export", "export-person-notechnical": "Global entity's person export without technical", "export-person-technical": "Global entity's person export only technical", "export-prestations": "Semi-annual prestation exports", "export-prestations-first-semester": "{{year}} 1st Semi-annual prestation export", "export-prestations-range": "Specifique period prestation export", "export-prestations-second-semester": "{{year}} 2nd Semi-annual prestation export", "first-day": {"1": "Mon", "2": "<PERSON><PERSON>", "3": "Wed", "4": "<PERSON>hu", "5": "<PERSON><PERSON>", "6": "Sat", "7": "Sun"}, "first_day_of_week": "First day of the week", "fisrt_day_of_week": "Frist day of the week:", "full_availability": "Add prestation on all the availability by default:", "full_availability_mobile": "Prestation on all the avail:", "input_geps_error_notification": "Activate GEPS error notification:  ", "input_geps_error_notification_mobile": "", "input_geps_notification": "Activate GEPS notification:  ", "input_geps_notification_mobile": "", "language": "Language:", "notification": "Notification preferences", "other_prestations": "Activate the display of multiple prestations for person:", "other_prestations_mobile": "Activate the display of multiple prestations for person:", "partial_availability": "Activate the display of distinctions on partial availability:", "partial_availability_mobile": "Partial availability:", "planning": "Availability planning preferences", "prestation_notification": "Activate prestation notification: ", "prestation_notification_mobile": "Activate prestation notification: ", "profil": "Profil preferences", "serviceplan": "Service Plan preferences", "show_prestation": "Display prestation by default: ", "show_prestation-mobile": "Prestations by <PERSON><PERSON><PERSON><PERSON>: ", "title": "My Preferences", "update-error": "Error during the update of your preference", "update-success": "Preference updated with success", "update-vehicle": "Vehicle status udaptes preference", "update-vehicle-6": "Activate notifications for statut 6:", "update-vehicle-6_mobile": "Notifications for statut 6: ", "update-vehicle-6c": "Activate notifications for statut 6c:", "update-vehicle-6c_mobile": "Notifications for statut 6c: "}, "myprofile": "My Profile"}, "title": "<strong>CGDIS</strong> Portal", "titleTest": " <strong>CGDIS</strong> Portal Test"}}, "logas": {"connected": "You're connected as {{userName}}", "info": "To connect in logas, please select one person below :", "logout": "Back to initial profil", "successful": "You're connected", "warning": "Warning, you're using logas mode"}, "login": {"field": {"password": "Password", "username": "IAM number"}, "successful": "You are connected"}, "managerial_occupations": {"list": {"header": {"assignment": "", "assignment_description": "", "code": "", "endDate": "", "external": "", "label": "", "nomination": "", "professional": "", "startDate": "", "status": {"list": {"ONGOING": "", "RESIGNED": "", "REVOKED": "", "SUSPENDED": ""}, "title": ""}, "type": "", "volunteer": ""}}, "title": ""}, "mobile-enrollment": {"ask": "Do you want to enroll your mobile phone ?", "title": "Mobile enrollment"}, "news": {"detail": {"button": "VSee more news", "title": "News detail"}, "list": {"empty": "No news available", "title": "News for Portail CGDIS"}}, "no_data": "No data available", "operational_dates": {"list": {"header": {"endDate": "", "startDate": "", "type": {"list": {"PROFESSIONAL": "", "VOLUNTEER": ""}, "title": ""}}}, "title": ""}, "operational_functions": {"popup": {"message": "By disabling this operational function, some of the person's prestations may be modified.<br>Continue ?", "subtitle": "Operational function disabling", "title": "Warning"}}, "operational_grades": {"list": {"header": {"code": "", "decreeDate": "", "echelon": "", "endDate": "", "gradeType": {"list": {"DMS": "", "GIS": "", "INCSASAP": ""}, "title": ""}, "professional": "", "psupp": "", "samu": "", "startDate": "", "title": "", "volunteer": ""}}, "title": ""}, "operational_volunteer_internship": {"duration": "", "endDate": "", "startDate": ""}, "operational_young_firefighter": {"firstName": "", "first_tutor": "", "lastName": "", "parental_consent": "", "phoneNumber": "", "second_tutor": ""}, "optional_backup_group_management": {"list": {"allgroups": "", "allgroupsmobile": "", "cgdisregistrationnumber": "", "members": "", "name": "", "select_entity": "", "title": ""}, "title": ""}, "people_management": {"functions": {"allpersons": "Also show functions in subordinate entities", "allpersonsmobile": "Show subordinate entities", "ambulance": "Ambulance", "availability": "Relation Status", "cgdisregistrationnumber": "CGDIS registration number", "cgdisregistrationnumbermobile": "", "commandment": "", "create": {"success": "The function has been added with success"}, "deletion": "", "deletion_message": "", "details": {"cgdisRegistrationNumber": "CGDIS registration number", "firstName": "First name", "lastName": "Last name"}, "dms": "", "fire": "Fire", "firstname": "Firstname", "function_operational": "Operational functions", "function_operational_mobile": "Operational func.", "function_operational_tag": "Tag", "function_operational_tag_mobile": "Tag", "gis": "", "granted": "Granted ?", "lastname": "Lastname", "other": "Other", "person_number_ext": "Number of external", "person_number_ext_mobile": "EXT", "person_number_pro": "Number of professional", "person_number_pro_mobile": "PRO", "person_number_vol": "Number of volunteer", "person_number_vol_mobile": "VOL", "person_number_total_distinct": "Real total", "person_number_total_distinct_mobile": "Total", "person_number_vol_tec": "Volunteer tech.", "person_number_vol_tec_mobile": "VOL Tech", "person_number_pro_tec": "Professional tech.", "person_number_pro_tec_mobile": "PRO Tech", "samu": "", "select_entity": "Select an entity", "status": "Status", "summary": "Summary", "summaryheader": {"totalpersons": "Firefighters", "totalfos": "Functions"}, "table": {"filterName": "Name", "no_data": "No FO to assigned", "status": "Status", "assignmenttype": "Assignment type", "vehicletype": "Vehicle type", "withoutfunctions": "Persons Without functions"}, "technical": "Show technical assignments", "title": "Functions", "type": "", "update": {"information_message": "Please note, an update (authorization or deletion) of an operational function will lead to the deletion of future services linked to this function only", "success": "The function has been updated with success"}}, "general_contact": {"update": {"success": "Administrative contact successfully updated"}}, "operational_contact": {"update": {"success": "Operational contact successfully updated"}}, "title": "People Management"}, "people_medical_information": {"functions": {"allpersons": "Also show people in subordinate entities", "allpersonsmobile": "Show subordinate entities", "apt": "Apt", "apt_with_restriction": "", "aptitude": "Aptitude", "aptitudes": "Aptitudes / Validity", "aptitudesmobile": "", "cgdisregistrationnumber": "CGDIS registration number", "cgdisregistrationnumbermobile": "", "expired": "Expired", "firstname": "Firstname", "inapt": "Inapt", "lastname": "Lastname", "noSuspensionEndDate": "Indefinitely suspended", "restrictions": "Restrictions", "restrictionsmobile": "", "status": "Statut", "suspensionEndDate": "Suspension until", "suspensionToggle": "", "suspensions": "", "technical": "Show technical assignments", "temporary_inapt": "Temporary inapt", "toggle_filters": "Filters", "undefined": "Undefined", "validity_from": "Validity from ", "validity_until": "Expire after", "without_aptitudes": "Without aptitudes"}}, "planning": {"status": {"availability": "Available", "planning": "Planning", "professional": "Professional"}}, "prestation": {"assignteam": {"success": "{{nbPrestations}} prestation(s) has been created"}, "copy": {"popup": {"assignment": "Assignment", "assignment-message": "These prestations could not be copied because the persons concerned are no longer assigned to the entity of the service plan on the date of copying", "confirm": {"copy-day-message": " Do you want to copy <b>{{ dayName }}, {{ dayNumber }} {{ monthName }} </b>?", "copy-week-message": "Do you want to copy week <b>from {{ dateFrom }} to {{ dateTo }} </b>?", "target-date": "To", "title": "Validation"}, "day": "Date", "day-message": "Error occured during copy of prestations of :", "function": "Operational Function", "function-message": "These services could not be copied because the persons concerned no longer have the operational functions necessary on the date of copying", "message": "The following prestations could not be copied", "slotnotexist": "Slot", "slotnotexist-message": "These prestations could not be copied because the slot doesn't exist on the date of copying", "title": "Warning"}, "success": "Prestations was successfully copied"}}, "scheduler-management": {"description": {"backup_optional_group_push_request": "Request to update all optional and backup groups at ELS", "entities_push_request": "Requestto update all entities at ELS", "export_backup_112": "Export of all backup 112 files", "person_assignments_push_request": "Request to update all person assignment at LUXDOK and MSNAV", "prestation_queue_all": "Sending notifications of prestations that are put on hold", "prestationsallowancesall": "Compute allowances for date (or dates range), entities, persons", "service_plan_csv_export_all": "Export of all service plans in the file system", "service_plan_update_all_requestall": "SendING of all current service plans at ELS", "vehicle_box_push_request": "", "vehicle_push_request": "Request to update all vehicles at ELS"}, "error": "The task could not be restarted. Please contact an administrator.", "header": {"action": "Action", "cron": "Frequency", "description": "Description", "name": "Name"}, "name": {"backup_optional_group_push_request": "Push Grp. Opt. et Back. ELS", "entities_push_request": "Push Entities ELS", "export_backup_112": "Export 112 backup", "person_assignments_push_request": "Push Person assignment MSNAV/LuxDok", "prestation_queue_all": "Prestations notifications", "prestationsallowancesall": "Generate allowances", "service_plan_csv_export_all": "Export CSV", "service_plan_update_all_requestall": "Push SP ELS", "vehicle_box_push_request": "", "vehicle_push_request": "Push Vehicles ELS"}, "popup": {"message": "Are you sure you want to start this synchronization ?", "subtitle": "Start of synchronization", "title": "Warning"}, "success": "Task successfully restarted !", "title": "Scheduler management"}, "service-plan": {"popup": {"split": {"success": "Slot has been splited"}}}, "service_plan": {"add": {"error": {"nopersonselected": "Select a person"}}, "add_people": "+ Add person", "add_people_mobile": "+ Person", "address": "Address", "armingDelay": "Armament delay", "armingPriority": "Armament priority", "automatic_filling": "Automatic filling", "availability-details-between": "From {{startDate}} to {{endDate}}, available for {{duration}}", "availability-details-between-mobile": "From {{startDate}} to {{endDate}}", "availability-details-from": "From {{startDate}}, available for {{duration}}", "availability-details-from-mobile": "From {{startDate}}", "availability-details-until": "Until {{endDate}}, available for {{duration}}", "availability-details-until-mobile": "Until {{endDate}}", "availability-duration": "available for {{duration}}", "availability-duration-after": "after, available for {{duration}}", "availability-duration-before": "before, available for {{duration}}", "backupGroup": "Backup group", "button": {"validate": {"default": "Validate", "professional": "<PERSON><PERSON><PERSON> (professional)", "volunteer": "<PERSON><PERSON><PERSON> (volunteer)"}}, "city": "City", "country": "Country", "day_selector": "{{ date }}", "details": "Service Plan details", "details-link": "+ Details", "entity": "Entity", "exclusive": "Exclusive", "filter": {"all": "All", "archived": "Archived", "current": "Currents", "future": "Futures", "mypds": "My PdS"}, "filter-link": "+ Filters", "filter-link-toclose": "- Filters", "filterTitle": "Filters", "full-availability-long": "For 12h", "full-availability-long-mobile": "12h:", "full-availability-short": "For full availability", "full-availability-short-mobile": "Avail:", "job": {"available": "Availa<PERSON> (volunteer)", "available-mobile": "Available (vol)", "available-pro": "Available (professional)", "available-pro-mobile": "Available (pro)", "unaffected": "Unaffected", "unavailable": "Unavailable", "unavailable-prestation": "Prestation in progress"}, "legend": {"status": {"barracked": "Barracked", "complete": "Complete", "degraded": "Degraded", "empty": "Empty", "incomplete": "Incomplete", "nodata": "No data", "partial": "Partial"}}, "manual_filling": "Manual filling", "manual_synchronization_els": "+ Synchronize to ELS", "member": {"popup": {"tab": {"header": {"counter": {"tooltip": "{{counterLeft}} person(s) on {{counterRight}} have the following functions"}}}}, "prestations": {"between": "Between {{start}} and {{end}}", "from": "From {{start}}", "until": "Until {{end}}"}}, "model": "Model", "no_data": "No data available", "no_functions_vol": "Not allow for this function (Volunteer)", "no_functions_pro": "Not allow for this function (Professional)", "optionalGroup": "Optional group", "partial_functions_vol": "Partial functions (Volunteer)", "partial_functions_pro": "Partial functions (Professional)", "popup": {"add": {"error": {"nopersonselected": "Select a person"}, "professional-prestation": "Prestation pro."}, "delete": {"prestation": {"message": "Do you really want to delete the assignment of <b>{{firstname}} {{lastname}}</b> ?", "subtitle": "of an assignment", "success": "Prestation successfully deleted", "title": "Deletion", "with-availability": "Delete the associated availability"}}, "exclusive": {"message": "Please note, the service plan selected is <b> exclusive </b>. All current non-additional services for this person will be canceled. Would you like to continue?", "title": "Warning"}, "export": {"csv": {"message": "Please select the start date and the end date of the export", "success": "Export created"}, "pdf": {"message": "Please select the start date and the end date of the export", "success": "Export created"}, "send-filling": {"message": "Please select the start date and the end date of the export to send", "success": "The export request has been taken into account and will be sent"}}, "no_functions": {"message": "Please note that the person <b> doesn't have</b> required functions for this position. Would you like to continue?", "title": "Warning, missing functions"}}, "popupTitle": "Service plan", "positions": "Positions in service :", "prestations-availability": "Avails :", "schedule_selector": {"merge": {"popup": {"message": "You will merge the slot form {{fromStartTime}}h to {{fromEndTime}}h with that of {{toStartTime}}h to {{toEndTime}}h", "title": "Merge 2 slots", "warningmessage": "<b>Please note, the availability of the people below does not cover all the prestations or these people are unavailable:</b>"}}, "split": {"popup": {"closedVersionName": "The closed version", "message": "You will split the slot form {{startTime}}h to {{endTime}}h. <br/> Please enter the start hour of the new slot:", "nextVersionName": "The next version", "splittedVersionName": "The splitted version", "title": "Split of slot"}}}, "semester_selector": "(de)<PERSON><PERSON>ter {{semesterNumber}} {{year}}", "servicePlanType": "Service plan type :", "team": "Team", "title": "My service Plan", "title_one": "My service Plan", "vehicle": "Vehicle", "versions": {"copy": {"label": "New name", "message": " Do you want to copy this version?", "target": "to", "title": "Service Plan Version Copy"}, "dates": {"default": "From {{startMonth}}/{{startDay}}/{{startYear}}  to {{endMonth}}/{{endDay}}/{{endYear}}", "from": "From {{start<PERSON>onth}}/{{startDay}}/{{startYear}}", "label": "Version:", "month": "Month: {{month}} {{year}}", "year": "Year: {{year}}"}, "empty": "No service plan available", "filling": {"automatic": "Automatic", "manual": "Manual", "title": "Filling mode: "}, "registration": "No registration", "type": {"barracked": "Quartered", "barracked-mobile": "Qua.", "not_barracked": "Not quartered", "professional": "Professional", "title": "Type: "}, "vehicle": "Vehicle: "}, "week_selector": "From {{ startDate }} to {{ endDate }}", "zipCode": "Zip code"}, "tooltip": {"add": "Add", "add-position": "Add a position", "audit": "View audit", "availability": "Consulte  availability", "closure": "Close", "copy": "Copy", "delete": "Delete", "edit": "Edit", "export": {"csv": "Export in CSV", "default": "Export", "pdf-landscape": "Export in PDF (landscape)", "pdf-new-format": "Export en PDF (Portail)", "pdf-portrait": "Export in PDF (portrait)", "prestation": "Export prestations summary", "prestations": "Exporter prestations", "prestations-detail": "Export prestation detail", "prestations-penalty": "Export prestation penalties", "prestations-penalty-explain": "This export takes into account only these filters : intervention type Other, Commandment, DMS, GIS et status PROFESSIONAL", "prestations-summary": "Export prestation summary", "send-service-plan-filling": "Send plan by mail"}, "force-export": "Force the execution", "info-availabilities-list": "La liste est limitée à 50 noms apparents mais en saisissant le nom souhaité dans la barre de recherche, celui ci sera filtré automatiquement et sera présent dans la liste de recherche si existant", "logout": "Logout", "management": "Management", "new": "New", "next": "Next", "previous": "Previous", "refresh": "Refresh", "reload": "Reload", "service-plan": {"export": {"prestationspdf": "Export the week in PDF"}, "merge": "Merge the slot", "more": "More options", "split": "Split the slot"}, "validate": "Validate"}, "user-rights": {"permission": {"ROLE_PERMISSION_PERMAMONITOR": {"description": "", "name": "Permamonitor"}, "ROLE_PERMISSION_PERMAMONITOR_ADMIN_VIEW": {"description": "", "name": "Permamonitor Admin"}, "ROLE_PERMISSION_112_BACKUP": {"description": "Allow access to backup 112 menu", "name": "Access to backup 112 screen"}, "ROLE_PERMISSION_112_BACKUP_ACTIVATE": {"description": "Allow activation of backup 112's mode", "name": "Permission to activate backup 112's mode"}, "ROLE_PERMISSION_112_BACKUP_EXPORT": {"description": "Allow export of backup 112", "name": "Permission to export backup 112"}, "ROLE_PERMISSION_112_BACKUP_VIEW": {"description": "Allow view of backup 112's mode information", "name": "Access to backup 112's mode information"}, "ROLE_PERMISSION_ADMIN": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "description": "Allow access to the administrative menu", "name": "Access to the administrative screens"}, "ROLE_PERMISSION_ADMIN_ALLOWANCE_SETTINGS": {"description": "Allow the edition of the allowance", "name": "Permission to edit the allowance"}, "ROLE_PERMISSION_ADMIN_AUDIT": {"description": "Consult the list of data manipulation ", "name": "Permission to Consult the list of data manipulation "}, "ROLE_PERMISSION_ADMIN_AUDIT_PERMAMONITOR": {"description": "Consult the list of Permamonitor data manipulation", "name": "Permission to Consult the list of Permamnonitor data manipulation "}, "ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL": {"description": "Consult the list of Portal data manipulation ", "name": "Permission to Consult the list of Portal  data manipulation "}, "ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_PRESTATIONS": {"description": "Consult the list of prestations data manipulation ", "name": "Permission to Consult the list of prestations data manipulation "}, "ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_SERVICE_PLAN": {"description": "Consult the list of service plan data manipulation ", "name": "Permission to Consult the list of service plan data manipulation "}, "ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_SP_MODEL": {"description": "Consult the list of model data manipulation ", "name": "Permission to Consult the list of model data manipulation "}, "ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_LOGAS": {"description": "Consult the list of logas data manipulation ", "name": "Permission to Consult the list of logas data manipulation "}, "ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_ALLOWANCE": {"description": "Consult the list of allowance data manipulation ", "name": "Permission to Consult the list of allowance data manipulation "}, "ROLE_PERMISSION_ADMIN_BOX": {"description": "Allow access of the box list", "name": "Permission to access to the list of boxes"}, "ROLE_PERMISSION_ADMIN_BOX_UPDATE": {"description": "Allow update of the box", "name": "Allow updating of the box"}, "ROLE_PERMISSION_ADMIN_BOX_VIEW": {"description": "Allow viewing of the box list", "name": "Permission to view the list of boxes"}, "ROLE_PERMISSION_ADMIN_ENTITIES": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "dependency": "Accessing the menu of entities necessarily requires permission to view the list of entities", "description": "Allow access to the entity menu", "name": "Access the list of entities"}, "ROLE_PERMISSION_ADMIN_ENTITIES_UPDATE": {"description": "Allow updating the list of entities", "name": "Permission to update the list of entities"}, "ROLE_PERMISSION_ADMIN_ENTITIES_VIEW": {"description": "Allow viewing of the list of entities", "name": "Permission to view the list of entities"}, "ROLE_PERMISSION_ADMIN_EXPORT_PROFILE": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "dependency": "The rights of access to the administration screens of the export profiles necessarily imply the right of access to the viewing of the export profiles.", "description": "Allow access to the administration of export profiles", "name": "Access the administration of export profiles"}, "ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_CREATE": {"description": "Allow creation of export profile", "name": "Permission to create the export profile"}, "ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_DELETE": {"description": "Allow deletion of export profile", "name": "Permission to delete the export profile"}, "ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_UPDATE": {"description": "Allow updating of export profile", "name": "Permission to update the export profile"}, "ROLE_PERMISSION_ADMIN_EXPORT_PROFILE_VIEW": {"description": "Allow viewing of export profile screens", "name": "Permission to view the export profile screens"}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "dependency": "The rights of access to the operational function administration screens necessarily imply the right of access to the viewing of operational functions.", "description": "Allow access to the administration of operational functions", "name": "Access the administration of operational functions"}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_CLOSE": {"description": "Allow closing of operational function ", "name": "Permission to close operational function "}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_CREATE": {"description": "Allow creation of operational function ", "name": "Permission to create operational function "}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_DELETE": {"description": "Allow deletion of operational function ", "name": "Permission to delete operational function "}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_UPDATE": {"description": "Allow updating of operational function ", "name": "Permission to update operational function "}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_VALIDATE": {"description": "Allow validating of operational function ", "name": "Permission to validate operational function "}, "ROLE_PERMISSION_ADMIN_FUNCTION_OPERATIONAL_VIEW": {"description": "Allow viewing of operational function screens", "name": "Permission to view operational function screens"}, "ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "dependency": "The rights of access to the general message administration screens necessarily imply the right of access to the viewing of general messages.", "description": "Allow access to the administration of general messages", "name": "Access the administration of general messages"}, "ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_CREATE": {"description": "Allow creation of general message ", "name": "Permission to create general message "}, "ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_DELETE": {"description": "Allow deletion of general message ", "name": "Permission to delete general message "}, "ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_UPDATE": {"description": "Allow updating of general message ", "name": "Permission to update general message "}, "ROLE_PERMISSION_ADMIN_GENERAL_MESSAGE_VIEW": {"description": "Allow viewing of general message screens", "name": "Permission to view general message screens"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "dependency": "The rights of access to the model and service plan admin screens necessarily imply the right of access to versions of the models and service plans.", "description": "Allow access to the service plan template management menu", "name": "Access the administrative screens for managing service plan models"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_CLOSURE": {"description": "Allow closure of service plan model", "name": "Permission to close service plan model "}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_CREATE": {"dependency": "Modification, closure and deletion permissions are granted independently.", "description": "Allow create of service plan model ", "name": "Permission to creation service plan model "}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_DELETE": {"description": "Allow deletion of service plan model ", "name": "Permission to delete service plan model "}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_OPY": {"description": "Allow copy of service plan model ", "name": "Permission to copy service plan model "}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_UPDATE": {"description": "Allow updating of service plan model ", "name": "Permission to update service plan model"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_UPDATE_BACKUP": {"description": "Allow updating of service plan model backup status", "name": "Permission to update service plan model backup status"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "description": "Allow access to the menu of service plan model versions", "name": "Access the menu of service plan model versions"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_CLOSURE": {"description": "Allow closure of service plan model versions", "name": "Permission to close service plan model versions"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_COPY": {"dependency": "A person who has the right to copy the models, must by default have permission to modify the service plans. The permissions for creation, modification, closure and deletion are granted independently.", "description": "Allow copy of service plan model versions", "name": "Permission to copy service plan model versions"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_CREATE": {"description": "Allow creation of service plan model versions", "name": "Permission to create service plan model versions"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_DELETE": {"description": "Allow deletion of service plan model versions", "name": "Permission to delete service plan model versions"}, "ROLE_PERMISSION_ADMIN_MODEL_PDS_VERSION_UPDATE": {"dependency": "A person who has the right to modify the models must by default have permission to modify the service plans. The permissions for creation, modification, closure and deletion are granted independently.", "description": "Allow updating of service plan model versions", "name": "Permission to update service plan model versions"}, "ROLE_PERMISSION_ADMIN_PDS": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "dependency": "The rights of access to the model and service plan admin screens necessarily imply the right of access to versions of the models and service plans.", "description": "Authorize access to the service plan management menu", "name": "Access the service plan management administrative screens"}, "ROLE_PERMISSION_ADMIN_PDS_CLOSURE": {"description": "Allow closure of a service plan", "name": "Permission to close a service plan"}, "ROLE_PERMISSION_ADMIN_PDS_CREATE": {"dependency": "Modification, closure and deletion permissions are granted independently.", "description": "Allow creation of a service plan", "name": "Permission to create a service plan"}, "ROLE_PERMISSION_ADMIN_PDS_DELETE": {"description": "Allow deletion of a service plan", "name": "Permission to delete a service plan"}, "ROLE_PERMISSION_ADMIN_PDS_ELS_MANUAL_SYNCHRO": {"description": "Allow manual synchronization of a service plan", "name": "Permission to synchronize manually a service plan"}, "ROLE_PERMISSION_ADMIN_PDS_TEAM": {"description": "Allow access to the service plan team menu", "name": "Access the service plan team menu"}, "ROLE_PERMISSION_ADMIN_PDS_TEAM_CREATE": {"description": "Allow creation of service plan teams", "name": "Permission to create service plan teams"}, "ROLE_PERMISSION_ADMIN_PDS_TEAM_DELETE": {"description": "Allow deletion of service plan teams", "name": "Permission to delete service plan teams"}, "ROLE_PERMISSION_ADMIN_PDS_TEAM_UPDATE": {"description": "Allow updating of service plan teams", "name": "Permission to update service plan teams"}, "ROLE_PERMISSION_ADMIN_PDS_TEAM_VIEW": {"description": "Allow access to service plan teams", "name": "Permission to consult service plan teams"}, "ROLE_PERMISSION_ADMIN_PDS_UPDATE": {"description": "Allow updating of a service plan", "name": "Permission to update a service plan"}, "ROLE_PERMISSION_ADMIN_PDS_UPDATE_NATIONAL": {"description": "Allow updating of an entire service plan", "name": "Permission to update a service plan (National Level)"}, "ROLE_PERMISSION_ADMIN_PDS_UPDATE_ZONAL": {"description": "Allow updating of part of the service plan", "name": "Permission to update a service plan (Zonal Level)"}, "ROLE_PERMISSION_ADMIN_PDS_VEHICLE_UPDATE": {"description": "Authorize the updating of a vehicle from a service plan", "name": "Permission to update the vehicle from a service plan"}, "ROLE_PERMISSION_ADMIN_PDS_VERSION": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "description": "Allow access to the service plan versions menu", "name": "Access the menu of service plan versions"}, "ROLE_PERMISSION_ADMIN_PDS_VERSION_CLOSURE": {"description": "Allow closure of service plan versions", "name": "Permission to close service plan versions"}, "ROLE_PERMISSION_ADMIN_PDS_VERSION_COPY": {"deletion-dependency": "A person who has permission to copy versions of service plans must have permissions to modify, create and colt versions of service plans", "description": "Allow copy of service plan versions", "name": "Permission to copy service plan versions"}, "ROLE_PERMISSION_ADMIN_PDS_VERSION_CREATE": {"description": "Allow creation of service plan versions", "name": "Permission to create service plan versions"}, "ROLE_PERMISSION_ADMIN_PDS_VERSION_DELETE": {"description": "Allow deletion of service plan versions", "name": "Permission to delete service plan versions"}, "ROLE_PERMISSION_ADMIN_PDS_VERSION_UPDATE": {"description": "Allow updating of service plan versions", "name": "Permission to update service plan versions"}, "ROLE_PERMISSION_ADMIN_PDS_VERSION_VIEW": {"description": "Allow viewing of service plan versions", "name": "Permission to view service plan versions"}, "ROLE_PERMISSION_ADMIN_PDS_VIEW": {"description": "Allow viewing of service plan management screens", "name": "Permission to view service plan screens"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "dependency": "The rights of access to the position template admin screens necessarily imply the right of access to versions of the position templates.", "description": "Allow access to the service plan template management menu", "name": "Access the administrative screens for managing position templates"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_CLOSURE": {"description": "Allow closure of position template", "name": "Permission to close position template "}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_CREATE": {"dependency": "Modification, closure and deletion permissions are granted independently.", "description": "Allow create of position template ", "name": "Permission to creation position template "}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_DELETE": {"description": "Allow deletion of position template ", "name": "Permission to delete position template "}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_OPY": {"description": "Allow copy of position template ", "name": "Permission to copy position template "}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_UPDATE": {"description": "Allow updating of position template ", "name": "Permission to update position template"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_UPDATE_BACKUP": {"description": "Allow updating of position template backup status", "name": "Permission to update position template backup status"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "description": "Allow access to the menu of position template versions", "name": "Access the menu of position template versions"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_CLOSURE": {"description": "Allow closure of position template versions", "name": "Permission to close position template versions"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_COPY": {"dependency": "A person who has the right to copy the models, must by default have permission to modify the service plans. The permissions for creation, modification, closure and deletion are granted independently.", "description": "Allow copy of position template versions", "name": "Permission to copy position template versions"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_CREATE": {"description": "Allow creation of position template versions", "name": "Permission to create position template versions"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_DELETE": {"description": "Allow deletion of position template versions", "name": "Permission to delete position template versions"}, "ROLE_PERMISSION_ADMIN_POSITION_TEMPLATE_VERSION_UPDATE": {"dependency": "A person who has the right to modify the models must by default have permission to modify the service plans. The permissions for creation, modification, closure and deletion are granted independently.", "description": "Allow updating of position template versions", "name": "Permission to update position template versions"}, "ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "dependency": "The rights of access to the public holiday administration screens necessarily imply the right of access to the viewing of public holidays.", "description": "Allow access to the administration of public holidays", "name": "Access the administration of public holidays"}, "ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_CREATE": {"description": "Allow creation of public holiday ", "name": "Permission to create public holiday "}, "ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_DELETE": {"description": "Allow deletion of public holiday ", "name": "Permission to delete public holiday "}, "ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_UPDATE": {"description": "Allow updating of public holiday ", "name": "Permission to update public holiday "}, "ROLE_PERMISSION_ADMIN_PUBLIC_HOLIDAY_VIEW": {"description": "Allow viewing of public holiday screens", "name": "Permission to view public holiday screens"}, "ROLE_PERMISSION_ADMIN_SCHEDULER": {"description": "See the list of scheduler information", "name": "Permission to see the list of scheduler information"}, "ROLE_PERMISSION_ADMIN_SCHEDULER_ELS": {"description": "See the list of ELS scheduler information", "name": "Permission to see the list of ELS scheduler information"}, "ROLE_PERMISSION_ADMIN_SCHEDULER_BACKUP112": {"description": "See the list of Export Backup 112 scheduler information", "name": "Permission to see the list of Export Backup 112 scheduler information"}, "ROLE_PERMISSION_ADMIN_SCHEDULER_EXPORTCSVCIS": {"description": "See the list of CSV export of all CIS scheduler information", "name": "Permission to see the list of CSV export of all CIS scheduler information"}, "ROLE_PERMISSION_ADMIN_SCHEDULER_INTERNAL": {"description": "See the list of  Internal scheduler information", "name": "Permission to see the list of  Internal scheduler information"}, "ROLE_PERMISSION_ADMIN_SCHEDULER_ASSIGNMENT": {"description": "See the list of  assignments update scheduler information", "name": "Permission to see the list of  assignments update scheduler information"}, "ROLE_PERMISSION_ADMIN_SCHEDULER_ALL": {"description": "See the list of all schedulers information", "name": "Permission to see the list of all schedulers information"}, "ROLE_PERMISSION_ADMIN_VEHICLES": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "dependency": "Accessing the vehicle menu necessarily requires permission to view the list of vehicles.", "description": "Allow access to the vehicle menu", "name": "Access the vehicle list"}, "ROLE_PERMISSION_ADMIN_VEHICLES_ID_VIEW": {"description": "Allow viewing of vehicle identifiers", "name": "Permission to view vehicle identifiers"}, "ROLE_PERMISSION_ADMIN_VEHICLES_STATUS_VIEW": {"description": "Allow viewing of vehicle status history", "name": "Permission to view the history of vehicle statuses"}, "ROLE_PERMISSION_ADMIN_VEHICLES_VIEW": {"description": "Allow viewing of the vehicle list", "name": "Permission to view the list of vehicles"}, "ROLE_PERMISSION_ADMIN_VEHICLES_VIEW_ALL": {"description": "Allow viewing of the vehicle list without restriction", "name": "Allow viewing of the vehicle list without restriction"}, "ROLE_PERMISSION_ALLOWANCE": {"dependency": "Access to allowance management screen", "description": "Authorize the access to allowance management screen", "name": "Permission to access to allowance management screen"}, "ROLE_PERMISSION_ALLOWANCE_AUDIT": {"description": "Allow the viewing of the allowance audit", "name": "Permission to view the allowance audit"}, "ROLE_PERMISSION_ALLOWANCE_CONFIGURATION": {"dependency": "Access to allowance configuration management screen", "description": "Authorize the access to allowance configuration management screen", "name": "Permission to access to allowance configuration management screen"}, "ROLE_PERMISSION_ALLOWANCE_CONFIGURATION_CREATE": {"description": "Allow the creation of allowance configuration", "name": "Permission to create allowance configuration"}, "ROLE_PERMISSION_ALLOWANCE_CONFIGURATION_DELETE": {"description": "Allow the deletion of allowance configuration", "name": "Permission to delete allowance configuration"}, "ROLE_PERMISSION_ALLOWANCE_CONFIGURATION_UPDATE": {"description": "Allow the update of allowance configuration", "name": "Permission to update allowance configuration"}, "ROLE_PERMISSION_ALLOWANCE_CONFIGURATION_VIEW": {"description": "Access the allowance configuration list screen", "name": "Permission to view the allowance configuration list screen"}, "ROLE_PERMISSION_ALLOWANCE_SEARCH": {"description": "Access the allowance search screen", "name": "Permission to view the allowance search screen"}, "ROLE_PERMISSION_ALLOWANCE_VIEW": {"description": "Access the allowance list screen", "name": "Permission to view the allowance list screen"}, "ROLE_PERMISSION_CURRENT_SITUATION": {"description": "Allow viewing of the current situation", "name": "Permission to view the current situation"}, "ROLE_PERMISSION_DASHBOARD_MANAGER": {"description": "Allow access to the responsible dashboard", "name": "Access the Responsible dashboard"}, "ROLE_PERMISSION_DASHBOARD_MEMBER": {"description": "Allow access to the member dashboard", "name": "Access the Member dashboard"}, "ROLE_PERMISSION_DASHBOARD_MEMBER_WORKING_PLAN": {"description": "Allow access to the work plans menu", "name": "Access the work plan screen"}, "ROLE_PERMISSION_GENERATE_API_DESCRIPTION": {"description": "Allow generation of API description", "name": "Permission to generate API description"}, "ROLE_PERMISSION_GENERATE_PRESTATIONS_ALLOWANCES": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "description": "Allow access to allowances computing", "name": "Access allowances computing"}, "ROLE_PERMISSION_GLOBAL_ACCESS_LOGAS": {"description": "Allow access to connect in logas", "name": "Permission to connect in logas"}, "ROLE_PERMISSION_GLOBAL_EXPORT_PRESTATIONS": {"description": "Allow the semi-annual prestation export for all CGDIS Member", "name": "Allow the semi-annual prestation export for all CGDIS Member"}, "ROLE_PERMISSION_IMPERSONATED": {"description": "Allow to logas", "name": "Permission to logas"}, "ROLE_PERMISSION_MY_PROFILE_ACTIVITY_EXPORT_PRESTATIONS": {"description": "Allow export prestations on activity tab of the profile of the logged in user", "name": "Allow logged in user export prestations"}, "ROLE_PERMISSION_MY_PROFILE_ACTIVITY_EXPORT_PRESTATIONS_PENALTY": {"description": "Allow export penalties on activity tab of the profile of the logged in user", "name": "Allow logged in user export penalties"}, "ROLE_PERMISSION_MY_PROFILE_ACTIVITY_VIEW": {"description": "Allow access to the activity tab of the profile of the logged in user", "name": "Access profile - my activities"}, "ROLE_PERMISSION_MY_PROFILE_GENERAL_CONTACT_UPDATE": {"description": "Authorize the updating of the administrative and operational details of the person connected.", "name": "Permission to update general contact information for the profile"}, "ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW": {"description": "Allow access to the general tab of the profile of the logged in user", "name": "Access profile - general tab"}, "ROLE_PERMISSION_MY_PROFILE_GENERAL_VIEW_DIPLOMAS": {"description": "Allow access to the diplomas on the profile of the logged in user", "name": "Access profile - diplomas"}, "ROLE_PERMISSION_MY_PROFILE_MEDICAL_VIEW": {"description": "Allow access to the medical tab of the profile of the logged in user", "name": "Access profile - medical tab"}, "ROLE_PERMISSION_MY_PROFILE_MOBILE_GENERATE_QRCODE": {"description": "Allow QR code generation", "name": "Permission to generate a QR code"}, "ROLE_PERMISSION_MY_PROFILE_OPERATIONAL_VIEW": {"description": "Allow access to the operational tab of the profile of the logged in user", "name": "Access profile - operational tab"}, "ROLE_PERMISSION_MY_PROFILE_PREFERENCES_UPDATE": {"description": "Allow updating the preferences of the logged in user", "name": "Permission to update preferences"}, "ROLE_PERMISSION_MY_PROFILE_PREFERENCES_VIEW": {"dependency": "Accessing the preferences screen necessarily requires permission to modify the preferences.", "description": "Allow access to the preferences of the logged in user", "name": "Access preferences"}, "ROLE_PERMISSION_MY_PROFILE_VIEW": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "dependency": "Access to the profile menu necessarily requires permission to modify the information that may be.", "description": "Allow access to the profile of the logged in user", "name": "Access profile"}, "ROLE_PERMISSION_MY_PROFILE_VIEW_FUNCTION_OPERATIONAL": {"description": "Allow access to the function operationals on the profile of the logged in user", "name": "Access profile - function operationals"}, "ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT": {"dependency": "Access to optional backup group management screen", "description": "Authorize the access to optional backup group management screen", "name": "Permission to access to optional backup group management screen"}, "ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT_VIEW": {"dependency": "View the optional backup group management list", "description": "Authorize the view the optional backup group management list", "name": "Permission to view the optional backup group management list"}, "ROLE_PERMISSION_PAGER_UPDATE": {"ROLE_PERMISSION_GLOBAL_ACCESS_LOGAS": {"description": "", "name": ""}, "ROLE_PERMISSION_IMPERSONATED": {"description": "", "name": ""}, "ROLE_PERMISSION_MY_PROFILE_MOBILE_GENERATE_QRCODE": {"description": "", "name": ""}, "ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_OPTIONAL_BACKUP_GROUP_MANAGEMENT_VIEW": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_ADD_PERSON": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_ADD_PERSON_FULL_AVAILABILITY": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_ADD_PERSON_VIEW_UNAFFECTED": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_ADD_TEAM": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_DELETE_PRESTATION": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_EXPORT": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_EXPORT_PDF": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_SEND_FILLING": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_SPLIT_TIMESLOT": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_FILL_VIEW_ALL_AVAILABILITIES": {"description": "", "name": ""}, "ROLE_PERMISSION_PDS_LIST": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_PDS_LIST_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_ERROR_MANAGEMENT": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT": {"deletion-dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS_PENALTY": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_EXPORT": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW_DIPLOMAS": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_MEDICAL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_OPERATIONAL_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE_SEND_ALARM_CLOCK": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_FUNCTION_OPERATIONAL": {"dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ROLE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_YOUNG_ALL": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_YOUNG_FIREFIGHTER": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MEDICAL_INFORMATION_LIST": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_RIGHTS": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_USER_RIGHTS_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_RIGHTS_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_RIGHTS_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_COPY": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_CREATE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS": {"deletion-dependency": "", "dependency": "", "description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_COPY": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_CREATE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_DELETE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_UPDATE": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_VIEW": {"description": "", "name": ""}, "column": "", "dependency": "", "description": "Allow update of GSM Pager (RW)", "entity": "", "name": "Allow update of  GSM Pager (RW)", "no-permissions": "", "no-search-result": "", "search-permission": "", "select-permission": ""}, "ROLE_PERMISSION_PAGER_VIEW": {"description": "Allow view of GSM Pager (RO)", "name": "Allow view of GSM Pager (RO)"}, "ROLE_PERMISSION_PDS_FILL_": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "dependency": "Accessing the menu of service plan details necessarily requires permission to view the details of service plans. Permission for modification is granted independently.", "description": "Authorize access to the operational details of a service plan.", "name": "Access operational detail of service plans"}, "ROLE_PERMISSION_PDS_FILL_ADD_PERSON": {"description": "Authorize the modification of a service plan. Adding or replacing a person on a service plan. <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to add a person to a service plan"}, "ROLE_PERMISSION_PDS_FILL_ADD_PERSON_FULL_AVAILABILITY": {"description": "Authorize the modification of a service plan. Adding or replacing a person on a service plan for the entire availability of the person. <br /> <b> All access and rights are within the limit of his / her entity (ies) </ b>", "name": "Permission to add a person on a service plan on full availability"}, "ROLE_PERMISSION_PDS_FILL_ADD_PERSON_VIEW_UNAFFECTED": {"description": "Authorize viewing the tab of unaffected persons on popup filling service plan", "name": "Permission to view unaffected persons tab"}, "ROLE_PERMISSION_PDS_FILL_ADD_TEAM": {"description": "Authorize the addition of a full team on a time slot of a service plan", "name": "Permission to add a full team on a time slot of a service plan"}, "ROLE_PERMISSION_PDS_FILL_DELETE_PRESTATION": {"description": "Authorize the closing of a service on a position of a service plan. <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to close a service on a service plan"}, "ROLE_PERMISSION_PDS_FILL_EXPORT": {"description": "Authorize the export of the services of a service plan in CSV format", "name": "Permission to export the services of a service plan in CSV format"}, "ROLE_PERMISSION_PDS_FILL_EXPORT_PDF": {"description": "Authorize the export of weekly services from a service plan in PDF format", "name": "Permission to export weekly services from a service plan in PDF format"}, "ROLE_PERMISSION_PDS_FILL_SEND_FILLING": {"description": "Permission to export weekly services from a service plan in PDF format", "name": "Permission to export weekly services from a service plan in PDF format"}, "ROLE_PERMISSION_PDS_FILL_SPLIT_TIMESLOT": {"description": "Authorize the split of a time slot on a service plan. </br>", "name": "Permission to split a time slot from a service plan"}, "ROLE_PERMISSION_PDS_FILL_VIEW": {"description": "Allow viewing the filling of a service plan. <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to view the filling of a service plan"}, "ROLE_PERMISSION_PDS_FILL_VIEW_ALL_AVAILABILITIES": {"description": "Allow viewing of all availabilities for a service plan", "name": "Permission to view all availabilities for a service plan"}, "ROLE_PERMISSION_PDS_LIST": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "dependency": "Accessing the menu of the service plans list necessarily requires permission to view the list of service plans. Permission for modification is granted independently.", "description": "Allow access to the list of service plans.", "name": "Access the operational menu of service plans"}, "ROLE_PERMISSION_PDS_LIST_VIEW": {"description": "Allow viewing of the list of service plans. <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to view the operational list of service plans"}, "ROLE_PERMISSION_PDS_STATE_VIEW": {"description": "Allow viewing the status information about service plan (ie: optional/backup groups unavailable) <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to view  status information about service plan (ie: optional/backup groups unavailable)"}, "ROLE_PERMISSION_PREFERENCES_UPDATE_VEHICLE": {"description": "", "name": ""}, "ROLE_PERMISSION_PROFILE_TIC_LOGAS_VIEW": {"description": "Allow viewing TIC on person's profile in members list<br /><b>All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to view TIC on person's profile in members list"}, "ROLE_PERMISSION_PROFILE_TIC_VIEW": {"description": "Allow viewing TIC on MyProfile screen", "name": "Permission to view TIC on MyProfile screen"}, "ROLE_PERMISSION_ERROR_MANAGEMENT": {"description": "Allow access to the error management menu", "name": "Access errors management screens"}, "ROLE_PERMISSION_ERROR_MANAGEMENT_PAGER": {"description": "Allow access to the Pager error management", "name": "Access Pager errors"}, "ROLE_PERMISSION_ERROR_MANAGEMENT_ELS": {"description": "Allow access to the ELS error management", "name": "Access ELS errors"}, "ROLE_PERMISSION_ERROR_MANAGEMENT_FO": {"description": "Allow access to the FO error management", "name": "Access FO errors"}, "ROLE_PERMISSION_ERROR_MANAGEMENT_INTERNAL": {"description": "Allow access to the Interne error management", "name": "Access Interne errors"}, "ROLE_PERMISSION_ERROR_MANAGEMENT_LEVESO": {"description": "Allow access to the Leveso error management", "name": "Access Leveso errors"}, "ROLE_PERMISSION_ERROR_MANAGEMENT_GEPS": {"description": "Allow access to the GEPS error management", "name": "Access GEPS errors"}, "ROLE_PERMISSION_ERROR_MANAGEMENT_MSNAV": {"description": "Allow access to the MSNAV error management", "name": "Access MSNAV errors"}, "ROLE_PERMISSION_ERROR_MANAGEMENT_LUXDOK": {"description": "Allow access to the LUXDOK error management", "name": "Access LUXDOK errors"}, "ROLE_PERMISSION_ERROR_MANAGEMENT_EXPORT": {"description": "Allow access to the Exports error management", "name": "Access Exports errors"}, "ROLE_PERMISSION_SYSTEM_MANAGEMENT": {"description": "Allow access to the portal system menu", "name": "Access the system menu"}, "ROLE_PERMISSION_USER_MANAGEMENT": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "description": "Allow access to the portal user management menu", "name": "Access the user management menu"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "dependency": "Accessing the people management menu necessarily requires permission to view the information of people. Permission for modification is granted independently.", "description": "Allow access to the people management menu", "name": "Access the people management menu"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS": {"description": "Allow export persons prestations summary. <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to export persons prestations summary of people information"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_EXPORT_PRESTATIONS_PENALTY": {"description": "Allow export persons penalties summary. <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to export persons penalties summary of people information"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_ACTIVITY_VIEW": {"description": "Allow viewing of people's activities. <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to view the My Activities tab of people information"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_EXPORT": {"description": "Allow the export in CSV format of the list of people and their personal information. <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to export personal information in CSV format"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW": {"description": "Allow viewing of general information of people. <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to view the general tab of personal information"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_GENERAL_VIEW_DIPLOMAS": {"description": "Allow viewing of diplomas of people. <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to view the diplomas in personal information"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_MEDICAL_VIEW": {"description": "Allow viewing of people's medical information. <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to view the medical tab of personal information"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_OPERATIONAL_VIEW": {"description": "Allow viewing of operational information of people. <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to view the operational tab of personal information"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE": {"description": "Authorize the updating of general information of the persons in his entity.", "name": "Permission to update general personal information"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_UPDATE_SEND_ALARM_CLOCK": {"dependency": "Updating the 'Alarmdepesch' parameter necessarily entails the right to update personal information of persons", "description": "Authorize the update of the 'Alarmdepesch' parameter of the personal information of the persons of its entity.", "name": "Permission to update the 'Alarmdepesch' parameter for people"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW": {"description": "Allow viewing of the list of people and their personal information. <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to view personal information"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ALL": {"description": "View every person and their personal information. <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to view every person in member list."}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_FUNCTION_OPERATIONAL": {"dependency": "Update the function operational necessarilty entails the right to view personal information of persons", "description": "Authorize the view of function operationals of the personal information of the persons of its entity", "name": "Permisssion to view the function operationals for people"}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_ROLE": {"description": "View only young roles of person in logas. <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to view roles of person in logas."}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_YOUNG_ALL": {"description": "", "name": ""}, "ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_YOUNG_FIREFIGHTER": {"description": "View only young firefighter and their personal information. <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to view only firefighter in member list."}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "dependency": "Accessing the menu of operational functions necessarily requires permission to view the list of operational functions. Permission for modification is granted independently.", "description": "Allow access to the menu of operational functions", "name": "Access the operational functions management menu"}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "dependency": "Accessing the menu of operational functions mapping necessarily requires permission to view the operational functions mapping screen. Permission for modification is granted independently.", "description": "Allow access to the menu of operational functions mapping", "name": "Access the menu for managing operational functions"}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_UPDATE": {"description": "Authorize the update of the operational functions configuration", "name": "Permission to update the configuration of operational functions"}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_VIEW": {"description": "Allow viewing of operational function settings", "name": "Permission to view the mapping of operational functions"}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_UPDATE": {"description": "Allow updating of operational functions", "name": "Permission to update the list of operational functions"}, "ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW": {"description": "Allow viewing of operational functions. <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to view the list of operational functions"}, "ROLE_PERMISSION_USER_MEDICAL_INFORMATION_LIST": {"description": "Allow viewing of people aptitudes and restrictions.", "name": "Permission to view the medical information tab"}, "ROLE_PERMISSION_USER_RIGHTS": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "dependency": "Accessing the user rights menu necessarily requires permission to view the list of user rights.", "description": "Allow access to the user rights menu", "name": "Access the user rights menu"}, "ROLE_PERMISSION_USER_RIGHTS_DELETE": {"description": "Allow the deletion of user rights", "name": "Permission to delete user rights"}, "ROLE_PERMISSION_USER_RIGHTS_UPDATE": {"description": "Allow the update of user rights", "name": "Permission to update user rights"}, "ROLE_PERMISSION_USER_RIGHTS_VIEW": {"description": "Allow the view of list of user rights", "name": "Permission to view the list of user rights"}, "ROLE_PERMISSION_VEHICLE_UPDATE_STATUS_6_VIEW": {"description": "", "name": ""}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "dependency": "Accessing the member dashboard menu necessarily requires permission to view the member dashboard. Permissions for creation, modification, closure and deletion are granted independently.", "description": "Allow access to the CGDIS member dashboard", "name": "Access the CGDIS member dashboard"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_COPY": {"description": "Allow copying of weekly or daily availability", "name": "Permission to copy an availability"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_CREATE": {"description": "Authorize the creation of an availability. <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to create an availability"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_DELETE": {"description": "Allow the deletion of availability", "name": "Permission to delete an availability"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS": {"deletion-dependency": "The deletion of this permission necessarily involves the deletion of other permissions", "dependency": "Accessing the volunteer availability management menu necessarily requires permission to view availability. Permissions for creation, modification and deletion are granted independently.", "description": "Access the volunteer availability management menu", "name": "Access the availability management of volunteers"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_COPY": {"description": "Allow the copy of volunteer availability", "name": "Permission to copy volunteer availability"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_CREATE": {"description": "Allow the creation of volunteer availability", "name": "Permission to create volunteer availability"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_DELETE": {"description": "Allow the deletion of volunteer availability", "name": "Permission to delete volunteer availability"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_UPDATE": {"description": "Allow the update of volunteer availability", "name": "Permission to update volunteer availability"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_LOGAS_VIEW": {"description": "Access the volunteer availability management screen", "name": "Permission to view the availability of volunteer"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_UPDATE": {"description": "Allow update of availability", "name": "Permission to update availability"}, "ROLE_PERMISSION_VOLUNTEER_AVAILABILITY_VIEW": {"description": "Allow viewing of the CGDIS member dashboard <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Permission to view the CGDIS member dashboard"}, "column": "Permission", "dependency": "This permission necessarily results in permission to other rights and / or the deletion of this permission necessarily results in the removal of other permissions", "entity": "Entity", "no-permissions": "No granted role", "no-search-result": "No result", "role": {"ROLE_ADMIN": {"description": "", "name": ""}, "ROLE_ASTREINTE_AUDIT": {"description": "", "name": ""}, "ROLE_AUDIT": {"description": "", "name": ""}, "ROLE_CENTER_CHIEF": {"description": "", "name": ""}, "ROLE_CHEF_COMPAGNIE": {"description": "", "name": ""}, "ROLE_DCOCGO_ASSISTANT": {"description": "", "name": ""}, "ROLE_DCOCGO_COORDINATOR": {"description": "", "name": ""}, "ROLE_DCOCSU_CHEFDESALLE": {"description": "", "name": ""}, "ROLE_DCO_DATA_ELS": {"description": "", "name": ""}, "ROLE_DCO_VOLUNTEER": {"description": "", "name": ""}, "ROLE_DIRECTOR": {"description": "", "name": ""}, "ROLE_DMS_COORDINATOR_SAMU": {"description": "", "name": ""}, "ROLE_HELPDESK": {"description": "", "name": ""}, "ROLE_INFS_SECRETARIAT": {"description": "", "name": ""}, "ROLE_MANAGEMENT_EXPORT": {"description": "", "name": ""}, "ROLE_MEMBER": {"description": "", "name": ""}, "ROLE_MONITEUR_JEUNES": {"description": "", "name": ""}, "ROLE_PAGER_MANAGEMENT": {"description": "", "name": ""}, "ROLE_PERMANENCE_MANAGEMENT": {"description": "", "name": ""}, "ROLE_PERMANENCE_MANAGEMENT_GLOBAL": {"description": "", "name": ""}, "ROLE_PERMANENCE_MANAGEMENT_LIGHT": {"description": "", "name": ""}, "ROLE_PERSON_AUDIT": {"description": "", "name": ""}, "ROLE_PERSON_AUDIT_GLOBAL": {"description": "", "name": ""}, "ROLE_PERSON_MANAGEMENT": {"description": "", "name": ""}, "ROLE_PERSON_MANAGEMENT_GLOBAL": {"description": "", "name": ""}, "ROLE_VEHICLE_AUDIT": {"description": "", "name": ""}, "ROLE_VEHICLE_MANAGEMENT": {"description": "", "name": ""}, "ROLE_ZONE_CHIEF": {"description": "", "name": ""}, "column": "", "dependency": "", "entity": "", "entityinheritance": "", "no-permissions": "", "no-search-result": "", "search-permission": "", "select-permission": ""}, "search-permission": "Search a granted role ...", "select-permission": "Select a role", "added-permission": "Rôle ajouter!", "removed-permission": "Rôle supprimer!"}, "role": {"ROLE_ADMIN": {"description": "An administrator can perform <b> all </b> the administrative and operational actions of the portal. <br/> He can also perform audit and technical actions", "name": "Administrator"}, "ROLE_ADMIN_OPERATIONAL": {"description": "An administrator can perform <b> all </b> the operational actions of the portal.", "name": "Operational Administrator"}, "ROLE_ASTREINTE_AUDIT": {"description": "On-call audit", "name": "On-call audit"}, "ROLE_AUDIT": {"description": "An auditor has <b> consultation access </b> only and on all operational data of the portal", "name": "Auditor"}, "ROLE_CENTER_CHIEF": {"description": "A center manager accesses second level administrative and operational functionalities (Service plan management, management of operational functions). <br /> He can view the list of entities, vehicles and user rights of the portal. <Br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Center chief"}, "ROLE_CHEF_COMPAGNIE": {"description": "Company Chief", "name": "Company Chief"}, "ROLE_DAF_COMPTABILITE": {"description": "Description not currently available", "name": "DAF Accounting"}, "ROLE_DCOCGO_ASSISTANT": {"description": "(en) DCO CGO Assistant", "name": "(en) DCO CGO Assistant"}, "ROLE_DCODCO_ADMINISTRATIF": {"description": "DCO DCO Administrative", "name": "DCO DCO Administrative"}, "ROLE_DCOCGO_COORDINATOR": {"description": "(en) DCO CGO Coordinateur", "name": "(en) DCO CGO Coordinateur"}, "ROLE_DMS_OFFICIER_SANTE_CSU": {"description": "(en) DMS CSU Officier Santé", "name": "(en) DMS CSU Officier Santé"}, "ROLE_DCOCSU_CHEFDESALLE": {"description": "(en) DCO CSU Chef de salle", "name": "(en) DCO CSU Chef de salle"}, "ROLE_DCOCSU_CADRE": {"description": "(en) DCO CSU Cadre", "name": "(en) DCO CSU Cadre"}, "ROLE_DCOCSU_REFERENT": {"description": "(en) DCO CSU Référent", "name": "(en) DCO CSU Référent"}, "ROLE_DCOCSU_REGULATEUR": {"description": "(en) DCO CSU Régulateur", "name": "(en) DCO CSU Régulateur"}, "ROLE_DCOCSU_DISPATCHER": {"description": "(en) DCO CSU Dispatcher", "name": "(en) DCO CSU Dispatcher"}, "ROLE_DCOCSU_SUPPORT_IT": {"description": "(en) DCO CSU Support IT", "name": "(en) DCO CSU Support IT"}, "ROLE_DCO_DATA_ELS": {"description": "DCO Data ELS", "name": "DCO Data ELS"}, "ROLE_DCO_VOLUNTEER": {"description": "DCO Volunteer department", "name": "DCO Volunteer department"}, "ROLE_DIRECTOR": {"description": "Descritpion not currently available", "name": "Director"}, "ROLE_DMS_COORDINATOR_SAMU": {"description": "Descritpion not currently available", "name": "DMS Coordinateur SAMU"}, "ROLE_FO_GEST": {"description": "Operational functions manager", "name": "Operational functions manager"}, "ROLE_FO_GEST_LIGHT": {"description": "Operational functions manager light", "name": "Operational functions manager light"}, "ROLE_HELPDESK": {"description": "DML TIC Helpdesk", "name": "DML TIC Helpdesk"}, "ROLE_INFS_SECRETARIAT": {"description": "(en) INFS Secretariat", "name": "(en) INFS Secretariat"}, "ROLE_INFS_FORM_SPEC": {"description": "(en) INFS Formation Spécialisée", "name": "(en) INFS Formation Spécialisée"}, "ROLE_INFS_STAGE_GEST": {"description": "(en) INFS Gestionnaire Stages Pratiques", "name": "(en) INFS Gestionnaire Stages Pratiques"}, "ROLE_MANAGEMENT_EXPORT": {"description": "Description not currently available", "name": "Export Manager"}, "ROLE_MEMBER": {"description": "A member can access the operational functionalities of the portal. <br /> <b> All access and rights are within the limit of his / her entity (s) </b> ", "name": "Member"}, "ROLE_MONITEUR_JEUNES": {"description": "Youth monitor", "name": "Youth monitor"}, "ROLE_PAGER_MANAGEMENT": {"description": "(en) DML TIC Gestionnaire des pagers", "name": "(en) DML TIC Gestionnaire des pagers"}, "ROLE_PERMANENCE_MANAGEMENT": {"description": "A permanence manager has access to administrative and operational functions relating only to service plans. <br /> He can view and make modifications to service plans. <br /> <b> All access rights and rights are within the limits of its entity (ies) </b>", "name": "Permanence Manager"}, "ROLE_PERMANENCE_MANAGEMENT_GLOBAL": {"description": "A permanence manager has access to administrative and operational functions relating only to service plans. <br /> He can view and make modifications to service plans. <br /> <b> All access rights and rights are within the limits of its entity (ies) </b>", "name": "Global Permanence Manager"}, "ROLE_PERMANENCE_MANAGEMENT_LIGHT": {"description": "A permanence manager has access to administrative and operational functions relating only to service plans. <br /> He can view and make modifications to service plans. <br /> <b> All access rights and rights are within the limits of its entity (ies) </b>", "name": "Permanence Manager Light"}, "ROLE_PERSON_AUDIT": {"description": "Description not currently available", "name": "People Auditor"}, "ROLE_PERSON_AUDIT_GLOBAL": {"description": "Description not currently available", "name": "Global People Auditor"}, "ROLE_PERSON_MANAGEMENT": {"description": "Description not currently available", "name": "People Manager"}, "ROLE_PERSON_MANAGEMENT_GLOBAL": {"description": "Description not currently available", "name": "Global People Manager"}, "ROLE_VEHICLE_AUDIT": {"description": "A vehicle auditor can view the entire fleet of emergency vehicles", "name": "Vehicle auditor"}, "ROLE_VEHICLE_MANAGEMENT": {"description": "A vehicle manager can initiate vehicle synchronization.", "name": "Vehicle manager"}, "ROLE_ZONE_CHIEF": {"description": "A zone chief accesses first and second level administrative functions (Management of service plan model, management of service plan, management of operational functions). <br /> He can view the list of entities, vehicles and portal user rights. <br /> <b> All access and rights are within the limit of its entity (ies) </b>", "name": "Zone chief"}, "column": "Role", "dependency": "This permission necessarily gives permission to other rights.", "entity": "Entity(ies)", "entityinheritance": "Entity inheritance", "no-permissions": "No granted permission", "no-search-result": "No result", "search-permission": "Search an granted permission ...", "select-permission": "Select a permission"}, "vehicles": {"title": "", "type": {"AMB": "", "DL": "", "FR": "", "HLF": "", "LF": "", "TMF": ""}}, "warnings": {"message": {"VEHICLE_TYPE_UPDATE": ""}}, "working_plan": {"new": {"title": ""}, "title": ""}}, "vehicles": {"title": "My Vehicles", "type": {"AMB": "", "DL": "", "FR": "", "HLF": "", "LF": "", "TMF": ""}}, "warning_allowances": "Warning, the allowances amount is based on the prestations until D-1", "warnings": {"message": {"VEHICLE_TYPE_UPDATE": "Vehicle type has changed in ELS but this vehicle is used in one service plan", "backup-group-unavailable": "The backup group {{group?.name}} is not available {{servicePlan.portalLabel}}", "backup-mode-activated": "The backup mode CSU112 is activated", "group-unavailable-for-plans": "The group {{group}} is unavailable for plan(s) {{servicePlan}}", "optional-group-unavailable": "The optional group {{group?.name}} is not available {{servicePlan.portalLabel}}", "pleaserotatedevice": "The display is optimised for landscape mode. Please rotate your device."}}, "working_plan": {"new": {"title": "Working plan creation"}, "title": "Phase 2 - Working plan"}}