import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnD<PERSON>roy,
  OnInit,
  ViewEncapsulation,
} from '@angular/core';
import { OperationalContactService } from '@app/general-information/operational-contact/operational-contact.service';
import { PersonGeneralInformation } from '@app/model/person/person-general-information.model';
import { of, Subject, Subscription } from 'rxjs';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { OperationalVolunteerInternshipService } from '@app/general-information/operational-volunteer-internship/operational-volunteer-internship.service';
import { switchMap, takeUntil } from 'rxjs/operators';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { LogAsService } from '@app/security/log-as.service';

@Component({
  selector: 'cgdis-portal-general-information-operational-tab',
  templateUrl: './general-information-operational-tab.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [OperationalContactService],
  encapsulation: ViewEncapsulation.None,
})
export class GeneralInformationOperationalTabComponent
  implements OnInit, OnDestroy
{
  @Input() personId: number;

  @Input() logas: boolean;

  showInternship: boolean;

  isYoungFirefighter: boolean;
  isSupportFirefighter: boolean;
  hasGrade: boolean;

  isMobile = false;

  protected canAccessFunctionOperational = false;

  private subscriptions: Subscription[] = [];

  private _unsubscribe$ = new Subject<void>();

  constructor(
    private operationalContactService: OperationalContactService,
    private cd: ChangeDetectorRef,
    private userService: ConnectedUserService,
    private logAsService: LogAsService,
    private volunteerInternshipService: OperationalVolunteerInternshipService,
    private breakpointObserver: BreakpointObserver,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
    this.subscriptions.forEach((subscription: Subscription) =>
      subscription.unsubscribe(),
    );
  }

  ngOnInit(): void {
    if (this.logas) {
      this.userService
        .hasAnyRolesObservable([
          'ROLE_PERMISSION_USER_MANAGEMENT_LOGAS_VIEW_FUNCTION_OPERATIONAL',
        ])
        .pipe(
          takeUntil(this._unsubscribe$),
          switchMap((hasRole) =>
            hasRole === true
              ? this.logAsService.canAccessFunctionOperational(this.personId)
              : of(false),
          ),
        )
        .subscribe((hasRole) => {
          this.canAccessFunctionOperational = hasRole;
          this.cd.markForCheck();
        });
    } else {
      this.canAccessFunctionOperational = true;
    }
    this.subscriptions.push(
      this.volunteerInternshipService
        .checkIfExists(this.personId)
        .subscribe((value) => {
          this.showInternship = value;
          this.cd.markForCheck();
        }),
      this.operationalContactService
        .get(this.personId)
        .subscribe((value: PersonGeneralInformation) => {
          this.isYoungFirefighter = value.youngFirefighter;
          this.isSupportFirefighter = value.supportFirefighter;
          this.hasGrade = value.hasGrade;
          this.cd.markForCheck();
        }),
    );
  }
}
