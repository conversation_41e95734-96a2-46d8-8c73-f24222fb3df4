<cgdis-portal-spinner [loading]="loading"></cgdis-portal-spinner>
<div *ngIf="!loading && detailsPrestation.length>0; else noData" class="row justify-content-center">
  <div *ngIf="isMobile; else desktopView" class="col-sm-12">
      <table *ngFor="let oneDetails of detailsPrestation" class="mobile-table">
        <tbody>
        <tr>
          <td>
            <b>{{ 'general_information.activity.totals.details.entity' | translate }}</b>
          </td>
          <td>
            {{ oneDetails.entityName }}
          </td>
        </tr>
        <ng-container *ngIf="isProfessional != null && isProfessional">
          <tr>
            <td>
              <b>{{ 'general_information.activity.totals.details.realDuration' | translate }}</b>
            </td>
            <td>
              {{ convertSecondsToDuration(oneDetails.duration) }}
            </td>
          </tr>
          <tr>
            <td>
              <b>{{ 'general_information.activity.totals.details.total' | translate }}</b>
            </td>
            <td>
              <span>{{ 'general_information.activity.totals.details.sum' | translate }}: N/A</span><br />
              <span>{{ 'general_information.activity.totals.details.barrackedAmount' | translate }}: N/A</span><br />
              <span>{{ 'general_information.activity.totals.details.notBarrackedAmount' | translate }}: N/A</span>
            </td>
          </tr>
        </ng-container>
        <ng-container *ngIf="isProfessional != null && !isProfessional">
          <tr>
            <td>
              <b>{{ 'general_information.activity.totals.details.realDuration' | translate }}</b>
            </td>
            <td>
              {{ convertSecondsToDuration(oneDetails.duration) }}
            </td>
          </tr>
          <tr>
            <td>
              <b>{{ 'general_information.activity.totals.details.total' | translate }}</b>
            </td>
            <td>
              <span>{{ 'general_information.activity.totals.details.sum' | translate }}
                : {{
                  {
                    hasAllowances: oneDetails.hasAllowance,
                    allowances: oneDetails.allowance
                  } | allowance
                }}</span><br />
              <span>{{ 'general_information.activity.totals.details.barrackedAmount' | translate }}
                : {{
                  {
                    hasAllowances: oneDetails.hasAllowanceBarracked,
                    allowances: oneDetails.allowanceBarracked
                  } | allowance
                }}</span><br />
              <span>{{ 'general_information.activity.totals.details.notBarrackedAmount' | translate }}
                : {{
                  {
                    hasAllowances: oneDetails.hasAllowanceNoBarracked,
                    allowances: oneDetails.allowanceNoBarracked
                  } | allowance
                }}</span><br />
            </td>
          </tr>
        </ng-container>
        <ng-container *ngIf="isProfessional == null">
          <tr>
            <td>
              <b
                *ngIf="isMobile; else desktopProDuration">{{ 'general_information.activity.totals.details.proDuration_mobile' | translate }}</b>
              <ng-template #desktopProDuration>
                <b>{{ 'general_information.activity.totals.details.proDuration' | translate }}</b>
              </ng-template>
            </td>
            <td>
              {{ convertSecondsToDuration(oneDetails.durationPro) }}
            </td>
          </tr>
          <tr>
            <td>
              <b
                *ngIf="isMobile; else desktopVolDuration">{{ 'general_information.activity.totals.details.volDuration_mobile' | translate }}</b>
              <ng-template #desktopVolDuration>
                <b>{{ 'general_information.activity.totals.details.volDuration' | translate }}</b>
              </ng-template>
            </td>
            <td>
              {{ convertSecondsToDuration(oneDetails.durationVol) }}
            </td>
          </tr>
          <tr>
            <td>
              <b>{{ 'general_information.activity.totals.details.total' | translate }}</b>
            </td>
            <td>
              <span>{{ 'general_information.activity.totals.details.sum' | translate }}
                : {{
                  {
                    hasAllowances: oneDetails.hasAllowance,
                    allowances: oneDetails.allowance
                  } | allowance
                }}</span><br />
              <span>{{ 'general_information.activity.totals.details.barrackedAmount' | translate }}
                : {{
                  {
                    hasAllowances: oneDetails.hasAllowanceBarracked,
                    allowances: oneDetails.allowanceBarracked
                  } | allowance
                }}</span><br />
              <span>{{ 'general_information.activity.totals.details.notBarrackedAmount' | translate }}
                : {{
                  {
                    hasAllowances: oneDetails.hasAllowanceNoBarracked,
                    allowances: oneDetails.allowanceNoBarracked
                  } | allowance
                }}</span><br />
            </td>
          </tr>
        </ng-container>
        </tbody>
      </table>
    </div>


  <ng-template #desktopView>
    <table class="col-10">
      <thead style="background-color: white;color:  black;">
      <tr>
        <th class="pb-4">
          <b>{{'general_information.activity.totals.details.entity' | translate}}</b>
        </th>
        <ng-container *ngIf="isProfessional != null && isProfessional">

          <th class="pb-4">
            <b>{{'general_information.activity.totals.details.realDuration' | translate}}</b>
          </th>
          <th class="pb-4">
            <b>{{'general_information.activity.totals.details.total' | translate}}</b>
          </th>
        </ng-container>
        <ng-container *ngIf="isProfessional != null && !isProfessional">

          <th class="pb-4">
            <b>{{'general_information.activity.totals.details.realDuration' | translate}}</b>
          </th>
          <th class="pb-4">
            <b>{{'general_information.activity.totals.details.total' | translate}}</b>
          </th>
        </ng-container>
        <ng-container *ngIf="isProfessional==null">
          <th class="pb-4">
              <b>{{'general_information.activity.totals.details.proDuration' | translate}}</b>
          </th>
          <th class="pb-4">
              <b>{{'general_information.activity.totals.details.volDuration' | translate}}</b>
          </th>
          <th class="pb-4">
            <b>{{'general_information.activity.totals.details.total' | translate}}</b>
          </th>
        </ng-container>
      </tr>
      </thead>
      <tbody>
      <tr style=" border-bottom: 1px solid #e9eef5"
          *ngFor="let oneDetails of detailsPrestation">
        <td>{{ oneDetails.entityName }}</td>
        <ng-container *ngIf="isProfessional != null && isProfessional">

          <td>{{ convertSecondsToDuration(oneDetails.duration) }}</td>
          <td>
            <span>{{'general_information.activity.totals.details.sum' | translate}}: N/A</span><br/>
            <span>{{'general_information.activity.totals.details.barrackedAmount' | translate}}: N/A</span><br/>
            <span>{{'general_information.activity.totals.details.notBarrackedAmount' | translate}}: N/A</span>
          </td>
        </ng-container>
        <ng-container *ngIf="isProfessional != null && !isProfessional">


          <td>{{ convertSecondsToDuration(oneDetails.duration) }}</td>
          <td>
          <span>{{'general_information.activity.totals.details.sum' | translate}}
            : {{ {hasAllowances: oneDetails.hasAllowance, allowances:oneDetails.allowance} | allowance }}</span><br/>
            <span>{{'general_information.activity.totals.details.barrackedAmount' | translate}}
              : {{ {hasAllowances: oneDetails.hasAllowanceBarracked, allowances:oneDetails.allowanceBarracked} | allowance }}</span><br/>
            <span>{{'general_information.activity.totals.details.notBarrackedAmount' | translate}}
              : {{ {hasAllowances: oneDetails.hasAllowanceNoBarracked, allowances:oneDetails.allowanceNoBarracked} | allowance }}</span><br/>
          </td>
        </ng-container>
        <ng-container *ngIf="isProfessional==null">

          <td>{{ convertSecondsToDuration(oneDetails.durationPro) }}</td>
          <td>{{ convertSecondsToDuration(oneDetails.durationVol) }}</td>
          <td>
          <span>{{'general_information.activity.totals.details.sum' | translate}}
            : {{ {hasAllowances: oneDetails.hasAllowance, allowances:oneDetails.allowance} | allowance }}</span><br/>
            <span>{{'general_information.activity.totals.details.barrackedAmount' | translate}}
            : {{ {hasAllowances: oneDetails.hasAllowanceBarracked, allowances:oneDetails.allowanceBarracked} | allowance }}</span><br/>
            <span>{{'general_information.activity.totals.details.notBarrackedAmount' | translate}}
            : {{ {hasAllowances: oneDetails.hasAllowanceNoBarracked, allowances:oneDetails.allowanceNoBarracked} | allowance }}</span><br/>
          </td>
        </ng-container>
      </tr>
      </tbody>
    </table>
  </ng-template>
</div>

<ng-template #noData>
  <span [translate]="'no_data'" class="p-5 d-flex"></span>
</ng-template>
