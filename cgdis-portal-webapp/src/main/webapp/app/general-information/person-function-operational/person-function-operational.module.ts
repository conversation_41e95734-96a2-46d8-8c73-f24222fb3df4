import { NgModule } from '@angular/core';
import { SharedModule } from '@app/common/shared/shared.module';
import { CommonModule } from '@angular/common';
import { FormModule } from '@app/common/modules/form-module/form.module';
import { SimpleTableModule } from '@app/common/modules/simple-table/simple-table.module';
import { DefaultFormTemplateModule } from '@app/common/template/default-form-template/default-form-template.module';
import { ConfigModule } from '@eportal/core';
import { TileGroupModule } from '@app/common/modules/tile-group/tile-group.module';
import { InputModule } from '@app/common/modules/input/input.module';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { ReactiveFormsModule } from '@angular/forms';
import { PersonFunctionOperationalComponent } from '@app/general-information/person-function-operational/person-function-operational.component';
import { PersonFunctionOperationalTableComponent } from './person-function-operational-table/person-function-operational-table.component';
import { PersonFunctionOperationalService } from '@app/general-information/person-function-operational/person-function-operational.service';
import { AssignmentService } from '@app/common/shared/services/assignment.service';
import { ScrollTableModule } from '@app/common/modules/scroll-table/scroll-table.module';
import { TabsListModule } from '@app/common/modules/tabs-list/tabs-list.module';
import { ScrollTableService } from '@app/common/modules/scroll-table/scroll-table.service';
import { PersonFunctionOperationalTableColumnLabelComponent } from '@app/general-information/person-function-operational/person-function-operational-table/columns/label/person-function-operational-table-column-label.component';
import { PersonFunctionOperationalTableColumnInterventionTypeComponent } from '@app/general-information/person-function-operational/person-function-operational-table/columns/interventiontype/person-function-operational-table-column-intervention-type.component';
import {
  PersonFunctionOperationalTableColumnCountEntitiesComponent
} from '@app/general-information/person-function-operational/person-function-operational-table/columns/count/person-function-operational-table-column-count-entities.component';
import {
  PersonFunctionOperationalTableDetailComponent
} from '@app/general-information/person-function-operational/person-function-operational-table/person-function-operational-table-detail.component';

@NgModule({
  imports: [
    SharedModule,
    CommonModule,
    FormModule,
    SimpleTableModule,
    DefaultFormTemplateModule,
    ConfigModule,
    TileGroupModule,
    InputModule,
    DatatableModule,
    EpDatatableModule,
    ReactiveFormsModule,
    ScrollTableModule,
    TabsListModule,
    PersonFunctionOperationalTableColumnLabelComponent,
    PersonFunctionOperationalTableColumnInterventionTypeComponent,
    PersonFunctionOperationalTableColumnCountEntitiesComponent,
    PersonFunctionOperationalTableDetailComponent,
  ],
  declarations: [
    PersonFunctionOperationalComponent,
    PersonFunctionOperationalTableComponent,
  ],
  exports: [PersonFunctionOperationalComponent],
  providers: [
    AssignmentService,
    PersonFunctionOperationalService,
    ScrollTableService,
  ],
})
export class PersonFunctionOperationalModule {}
