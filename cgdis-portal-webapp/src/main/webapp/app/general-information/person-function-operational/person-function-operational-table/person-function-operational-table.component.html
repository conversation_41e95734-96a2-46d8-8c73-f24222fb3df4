
<cgdis-portal-cgdisdatatable
  [autoMobileFilters]="true"
  [datatableService]="personFunctionOperationalDatatableTableService"
>


  <ng-template #template let-row="row">
    <cgdis-portal-person-function-operational-table-detail
      [personId]="personId"
      [functionOperationalWithAssignments]="row"
    ></cgdis-portal-person-function-operational-table-detail>
  </ng-template>

  <cgdis-portal-person-function-operational-table-column-label
    cgdisDatatableColumn
    [service]="personFunctionOperationalDatatableTableService"
  ></cgdis-portal-person-function-operational-table-column-label>

  <cgdis-portal-person-function-operational-table-column-intervention-type
    cgdisDatatableColumn
    [service]="personFunctionOperationalDatatableTableService"
  ></cgdis-portal-person-function-operational-table-column-intervention-type>

  <cgdis-portal-person-function-operational-table-column-count-entities
    cgdisDatatableColumn
    [service]="personFunctionOperationalDatatableTableService"
  ></cgdis-portal-person-function-operational-table-column-count-entities>

</cgdis-portal-cgdisdatatable>

<!--<ng-template #templateNoData>-->
<!--  <ul>-->
<!--    <span [translate]="'default.table.noresult'"></span>-->
<!--  </ul>-->
<!--</ng-template>-->
