<ep-datatable-column [columnName]="'functionOperational'" [flexGrow]="2">
  <ng-template epDatatableHeader>
    {{ 'admin.function_operational.list.header.function' | translate }}
  </ng-template>
  <ng-template epDatatableCell let-row="row">
    {{ cast(row).functionOperational.portalLabel }}
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-text-filter [filterName]="'functionOperationalPortalLabel'"
                                        [allowClear]="true"
                                        [datatableService]="service"></cgdis-portal-datatable-text-filter>
  </ng-template>
</ep-datatable-column>
