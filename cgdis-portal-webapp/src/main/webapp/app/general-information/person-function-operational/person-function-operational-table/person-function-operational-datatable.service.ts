import { Injectable } from '@angular/core';
import { UntypedFormBuilder } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import { RestService } from '@eportal/core';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { ToastService } from '@app/common/shared/toasts/CGDISToastService';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { FunctionOperationalWithEntitiesCount } from '@app/model/function-operational.model';
import { Observable } from 'rxjs';
import { Entity } from '@app/model/entity.model';

@Injectable()
export class PersonFunctionOperationalDatatableTableService extends CgdisDatatableService<FunctionOperationalWithEntitiesCount> {
  private baseUrl = ['person-function-operational'];

  constructor(
    private alertService: ToastService,
    private restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService, true);
  }

  getAll(personId: number) {
    super.initDataResourceList(
      this.restService.all(...this.baseUrl, 'byPerson', String(personId)),
    );
  }

  getEntities(
    personId: number,
    functionOperationalId: number,
  ): Observable<Entity[]> {
    return this.restService
      .all(
        ...this.baseUrl,
        'byPerson',
        String(personId),
        'fos',
        String(functionOperationalId),
        'entities',
      )
      .get();
  }
}
