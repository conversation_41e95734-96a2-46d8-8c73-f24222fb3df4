import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
} from '@angular/core';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { SharedModule } from '@app/common/shared/shared.module';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { FunctionOperationalWithEntitiesCount } from '@app/model/function-operational.model';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';

@Component({
  selector: 'cgdis-portal-person-function-operational-table-column-label',
  standalone: true,
  imports: [DatatableModule, EpDatatableModule, SharedModule],
  templateUrl:
    './person-function-operational-table-column-label.component.html',
  styleUrl: './person-function-operational-table-column-label.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () => PersonFunctionOperationalTableColumnLabelComponent,
      ),
    },
  ],
})
export class PersonFunctionOperationalTableColumnLabelComponent extends CgdisDatatableColumnComponent<FunctionOperationalWithEntitiesCount> {
  @Input() service: CgdisDatatableService<FunctionOperationalWithEntitiesCount>;
}
