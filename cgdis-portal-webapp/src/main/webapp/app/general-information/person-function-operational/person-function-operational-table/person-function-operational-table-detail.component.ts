import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { FunctionOperationalWithEntitiesCount } from '@app/model/function-operational.model';
import { PersonFunctionOperationalDatatableTableService } from '@app/general-information/person-function-operational/person-function-operational-table/person-function-operational-datatable.service';
import { Entity } from '@app/model/entity.model';
import { NgIf } from '@angular/common';

@Component({
  selector: 'cgdis-portal-person-function-operational-table-detail',
  standalone: true,
  imports: [NgIf],
  templateUrl: './person-function-operational-table-detail.component.html',
  styleUrl: './person-function-operational-table-detail.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PersonFunctionOperationalTableDetailComponent implements OnInit {
  @Input() personId: number;

  @Input()
  functionOperationalWithAssignments: FunctionOperationalWithEntitiesCount;

  protected entities: Entity[] = [];

  constructor(
    private personFunctionOperationalDatatableTableService: PersonFunctionOperationalDatatableTableService,
    private cd: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    this.personFunctionOperationalDatatableTableService
      .getEntities(
        this.personId,
        this.functionOperationalWithAssignments.functionOperational.tecid,
      )
      .subscribe({
        next: (entities) => {
          this.entities = entities;
          this.cd.markForCheck();
        },
      });
  }
}
