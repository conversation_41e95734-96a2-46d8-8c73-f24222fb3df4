<ep-datatable-column [columnName]="'interventionType.label'" [flexGrow]="2">
  <ng-template epDatatableHeader>
    {{ 'admin.function_operational.list.header.interventiontype' | translate }}
  </ng-template>
  <ng-template epDatatableCell let-row="row">
    {{ cast(row).functionOperational.interventionType | interventionTypeLabel }}
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-select-filter [filterName]="'interventionType.tecid'"
                                          [allowClear]="true"
                                          [possibleValues]="interventionTypes"
                                          [datatableService]="service"></cgdis-portal-datatable-select-filter>
  </ng-template>
</ep-datatable-column>
