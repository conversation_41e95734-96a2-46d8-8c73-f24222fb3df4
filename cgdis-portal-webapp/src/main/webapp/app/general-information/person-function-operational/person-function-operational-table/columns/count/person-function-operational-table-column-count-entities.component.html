<ep-datatable-column [columnName]="'entitiesCount'" [flexGrow]="2">
  <ng-template epDatatableHeader>
    {{ 'admin.function_operational.list.header.entitiescount' | translate }}
  </ng-template>
  <ng-template epDatatableCell let-row="row">
    {{ cast(row).entitiesCount }}
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-text-filter [filterName]="'entitiesCount'"
                                        [allowClear]="true"
                                        [datatableService]="service"></cgdis-portal-datatable-text-filter>
  </ng-template>
</ep-datatable-column>
