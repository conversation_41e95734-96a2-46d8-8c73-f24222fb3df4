import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
} from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { FunctionOperationalWithEntitiesCount } from '@app/model/function-operational.model';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector:
    'cgdis-portal-person-function-operational-table-column-count-entities',
  standalone: true,
  imports: [DatatableModule, EpDatatableModule, TranslateModule],
  templateUrl:
    './person-function-operational-table-column-count-entities.component.html',
  styleUrl:
    './person-function-operational-table-column-count-entities.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () => PersonFunctionOperationalTableColumnCountEntitiesComponent,
      ),
    },
  ],
})
export class PersonFunctionOperationalTableColumnCountEntitiesComponent extends CgdisDatatableColumnComponent<FunctionOperationalWithEntitiesCount> {
  @Input() service: CgdisDatatableService<FunctionOperationalWithEntitiesCount>;
}
