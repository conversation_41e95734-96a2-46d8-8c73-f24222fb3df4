import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { Subscription } from 'rxjs';
import { BreakpointObserver } from '@angular/cdk/layout';

import { Entity } from '@app/model/entity.model';
import { PersonFunctionOperationalDatatableTableService } from '@app/general-information/person-function-operational/person-function-operational-table/person-function-operational-datatable.service';
import { UntypedFormControl } from '@angular/forms';
import { take } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-person-function-operational-table',
  templateUrl: './person-function-operational-table.component.html',
  styles: [],
  providers: [PersonFunctionOperationalDatatableTableService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PersonFunctionOperationalTableComponent
  implements OnInit, OnDestroy, OnChanges
{
  @Input() entities: Entity[];

  @Input() entityIds: number[];

  @Input() personId: number;

  @Input() logas: boolean;

  /**
   * Call an update has been done on position
   * @type {EventEmitter<void>}
   */
  @Output() onFunctionOperationalUpdated = new EventEmitter<any>();

  showFilter = false;

  public loading = false;
  subscriptions: Subscription[] = [];

  formControl = new UntypedFormControl();

  numberOfFilters: number;

  constructor(
    private cdRef: ChangeDetectorRef,
    private breakpointObserver: BreakpointObserver,
    public personFunctionOperationalDatatableTableService: PersonFunctionOperationalDatatableTableService,
  ) {}

  ngOnInit() {
    this.formControl.setValue(this.entityIds);
    this.loadDatas();
    this.cdRef.markForCheck();

    this.personFunctionOperationalDatatableTableService
      .canExecuteFirstSearch()
      .pipe(take(1))
      .subscribe(() => {
        this.numberOfFilters =
          this.personFunctionOperationalDatatableTableService.getNumberOfFilters(
            ['entityIds'],
          );
      });
  }

  loadDatas(): void {
    this.loading = true;
    if (this.personId && this.entityIds) {
      this.personFunctionOperationalDatatableTableService.getAll(this.personId);
      this.stopLoading();
    }
  }

  updateFilterNumber() {
    this.numberOfFilters =
      this.personFunctionOperationalDatatableTableService.getNumberOfFilters([
        'entityIds',
      ]);
  }

  stopLoading() {
    setTimeout(() => {
      this.loading = false;
      this.cdRef.markForCheck();
    }, 500);
  }

  ngOnDestroy(): void {
    // Unsubscribe to all subscriptions
    for (let subscription of this.subscriptions) {
      subscription.unsubscribe();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.loadDatas();

    if (changes.entityIds) {
      this.formControl.setValue(this.entityIds);
    }
  }
}
