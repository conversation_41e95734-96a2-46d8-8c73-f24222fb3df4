import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { FunctionOperationalWithEntitiesCount } from '@app/model/function-operational.model';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { TranslateModule } from '@ngx-translate/core';
import { InterventionTypeLabelPipe } from '@app/common/pipes/translation/intervention-type-label.pipe';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { InterventionTypesService } from '@app/common/shared/services/intervention-types.service';

@Component({
  selector:
    'cgdis-portal-person-function-operational-table-column-intervention-type',
  standalone: true,
  imports: [
    DatatableModule,
    EpDatatableModule,
    TranslateModule,
    InterventionTypeLabelPipe,
  ],
  templateUrl:
    './person-function-operational-table-column-intervention-type.component.html',
  styleUrl:
    './person-function-operational-table-column-intervention-type.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () => PersonFunctionOperationalTableColumnInterventionTypeComponent,
      ),
    },
  ],
})
export class PersonFunctionOperationalTableColumnInterventionTypeComponent
  extends CgdisDatatableColumnComponent<FunctionOperationalWithEntitiesCount>
  implements OnInit
{
  @Input() service: CgdisDatatableService<FunctionOperationalWithEntitiesCount>;
  interventionTypes: FieldOption<number>[];

  constructor(private interventionTypeService: InterventionTypesService) {
    super();
  }

  ngOnInit(): void {
    this.interventionTypeService.getAll().subscribe((types) => {
      this.interventionTypes = types.map((type) => ({
        I18NLabel: `intervention.types.${type.label}`,
        value: type.tecid,
      }));
    });
  }
}
