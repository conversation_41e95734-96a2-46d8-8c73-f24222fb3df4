import { first } from 'rxjs/operators';
import { APP_INITIALIZER, NgModule } from '@angular/core';
import {
  BrowserModule,
  HAMMER_GESTURE_CONFIG,
  Title,
} from '@angular/platform-browser';
import { FormsModule } from '@angular/forms';
import { HashLocationStrategy, LocationStrategy } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

import { initConfig, initError } from './config/production.config';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import {
  HTTP_INTERCEPTORS,
  HttpClient,
  HttpClientModule,
} from '@angular/common/http';
import {
  AlertModule,
  ConfigModule,
  ConfigService,
  DatetimeModule,
  ERROR_MESSAGE_SERVICE,
  ErrorModule,
  ErrorService,
  RestModule,
} from '@eportal/core';
import { CgdisPortalMainComponent } from './layout/main/main.component';
import { SharedModule } from './common/shared/shared.module';
import { DatepickerModule } from './common/modules/datepicker/datepicker.module';
import { RouterModule } from '@angular/router';
import { InterventionTypesService } from './common/shared/services/intervention-types.service';
import { EntityService } from './common/shared/services/entity.service';
import { ToastService } from './common/shared/toasts/CGDISToastService';
import { OperationalFunctionService } from './common/shared/services/operational-function.service';
import { InputModule } from './common/modules/input/input.module';
import { NgxWebstorageModule } from 'ngx-webstorage';
import { AdminVehicleService } from './common/shared/services/admin-vehicle.service';
import { DatepickerConfigurationsService } from './common/shared/services/datepicker-configurations.service';
import { ServicePlanTypeService } from './common/shared/services/service-plan-type.service';
import { ErrorManagementModule } from './common/modules/error-management/error-management.module';
import { FormModule } from './common/modules/form-module/form.module';
import { SlidersModule } from './common/modules/sliders/sliders.module';
import { PlanningService } from './common/shared/services/planning.service';
import { SecurityModule } from './security/security.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';

import { ConnectedUserService } from './security/connected-user.service';
import { AuthenticationService } from './security/authentication.service';
import { ErrorMessageService } from './common/modules/error-management/services/error-message.service';
import { NewsHammerConfig } from './news/news-hammer.config';
import { DevelopmentModule } from './development/development.module';
import { ExportProfileService } from './common/shared/services/export-profile.service';
import { LoginModule } from './login/login.module';
import { AppComponent } from './app.component';
import { SvgTempComponent } from './layout/main/svg-temp/svg-temp.component';
import { AuthGuard } from './security/guards/auth.guard';
import { RoleGuard } from './security/guards/role.guard';
import { AssignmentService } from '@app/common/shared/services/assignment.service';
import { CustomTranslateLoader } from '@app/CustomTranslateLoader';
import { TranslationService } from '@app/security/translation.service';
import { AptitudesInformationFilterService } from '@app/general-information/aptitudes-information/aptitudes-information-filter.service';
import { RestrictionInformationFilterService } from '@app/general-information/restrictions-information/restriction-information-filter.service';

import { environment } from '@env/environment';
import { AppMobileComponent } from './app-mobile.component';
import { AppSwitchComponent } from './app-switch.component';
import { MobileGuard } from '@app/security/guards/mobile.guard';
import { TokenLoginService } from '@app/login/token-login.service';
import { Observable, Subscriber } from 'rxjs';
import { MobileTokenInterceptor } from '@app/security/interceptor/mobile-token-interceptor';
import { NothingInterceptor } from '@app/security/interceptor/nothing-interceptor';
import { ToastrModule } from 'ngx-toastr';
import { BoxService } from '@app/common/shared/services/box.service';
import { VehicleService } from '@app/common/shared/services/vehicle.service';

declare var Fingerprint: any;

export function createTranslateLoader(
  http: HttpClient,
  translationService: TranslationService,
) {
  return new CustomTranslateLoader(
    http,
    './assets/i18n/',
    '.json',
    translationService,
  );
}

export function executeLoading(
  configService: ConfigService,
  connectedUserService: ConnectedUserService,
  authenticationService: AuthenticationService,
  errorMessage: ErrorMessageService,
): Observable<void> {
  let loading = false;
  let loaded = false;
  let loadDetail = (subscriber: Subscriber<void>) => {
    connectedUserService
      .loadConnectedUser()
      .pipe(first())
      .subscribe(
        (value) => {
          loaded = true;
          errorMessage.enableMessages();
          subscriber.next();
        },
        (error) => {
          loaded = true;
          // purge errorCode message
          sessionStorage.clear();

          errorMessage.enableMessages();
          authenticationService.logout(true, false);
          subscriber.next();
        },
      );
  };

  return new Observable((subscriber) => {
    configService.config.subscribe((newConfig) => {
      loading = true;
      if (newConfig != undefined) {
        const afterLoadConfiguration = () =>
          connectedUserService
            .loadConfiguration()
            .pipe(first())
            .subscribe(
              () => loadDetail(subscriber),
              () => loadDetail(subscriber),
            );

        connectedUserService
          .loadConfiguration()
          .pipe(first())
          .subscribe(
            () => afterLoadConfiguration(),
            () => afterLoadConfiguration(),
          );
      }
    });
  });
}

export function onSuccessAuthenticate(
  configService: ConfigService,
  connectedUserService: ConnectedUserService,
  authenticationService: AuthenticationService,
  tokenLoginService: TokenLoginService,
  errorMessage: ErrorMessageService,
  resolve: any,
) {
  const url = window.localStorage.getItem('url');
  const clientId = window.localStorage.getItem('clientId');
  const redirectUri = window.localStorage.getItem('redirectUri');
  const grantType = window.localStorage.getItem('grantType');
  const code = window.localStorage.getItem('code');
  if (url != undefined && code != undefined) {
    tokenLoginService
      .loadTokenFromAuthorization(url, clientId, redirectUri, grantType, code)
      .pipe(first())
      .subscribe({
        next: () => {
          window.localStorage.removeItem('url');
          window.localStorage.removeItem('clientId');
          window.localStorage.removeItem('redirectUri');
          window.localStorage.removeItem('grantType');
          window.localStorage.removeItem('code');
          executeLoading(
            configService,
            connectedUserService,
            authenticationService,
            errorMessage,
          )
            .pipe(first())
            .subscribe({
              next: () => {
                resolve();
              },
              error: () => {
                resolve();
              },
            });
        },
        error: (err) => {
          window.localStorage.removeItem('url');
          window.localStorage.removeItem('clientId');
          window.localStorage.removeItem('redirectUri');
          window.localStorage.removeItem('grantType');
          window.localStorage.removeItem('code');
          authenticationService.goToLoginPage();
          resolve();
        },
      });
  } else if (tokenLoginService.hasToken()) {
    tokenLoginService
      .loadTokenFromRefreshToken()
      .pipe(first())
      .subscribe({
        next: () => {
          executeLoading(
            configService,
            connectedUserService,
            authenticationService,
            errorMessage,
          )
            .pipe(first())
            .subscribe({
              next: () => {
                resolve();
              },
              error: () => {
                resolve();
              },
            });
        },
        error: (err) => {
          authenticationService.goToLoginPage();
          resolve();
        },
      });
  } else {
    authenticationService.goToLoginPage();
    resolve();
    // throw new Error('No url, code or refresh token found');
  }
}

export function askFaceTouchId(
  configService: ConfigService,
  connectedUserService: ConnectedUserService,
  authenticationService: AuthenticationService,
  tokenLoginService: TokenLoginService,
  errorMessage: ErrorMessageService,
  resolve: any,
  delay = 1000,
) {
  setTimeout(() => {
    Fingerprint.isAvailable(
      function (type: any) {
        Fingerprint.show(
          {
            description: 'Scan your face or fingerprint',
          },
          function (msg: any) {
            onSuccessAuthenticate(
              configService,
              connectedUserService,
              authenticationService,
              tokenLoginService,
              errorMessage,
              resolve,
            );
          },
          function (msg: any) {
            authenticationService.goToFaceTouchIDErrorPage();
            resolve();
          },
        );
      },
      function (msg: any) {
        authenticationService.goToFaceTouchIDErrorPage();
        resolve();
      },
      { title: 'Scan your fingerprint please' },
    );
  }, delay);
}

export function onAppInit1(
  configService: ConfigService,
  connectedUserService: ConnectedUserService,
  authenticationService: AuthenticationService,
  tokenLoginService: TokenLoginService,
  errorMessage: ErrorMessageService,
): () => Promise<any> {
  return (): Promise<any> => {
    const getParameterByName = (name: string) => {
      const url = window.location.href;
      name = name.replace(/[\[\]]/g, '\\$&');
      const regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
        results = regex.exec(url);
      if (!results) return null;
      if (!results[2]) return '';
      return decodeURIComponent(results[2].replace(/\+/g, ' '));
    };

    return new Promise<void>((resolve, reject) => {
      if (
        environment.application === 'mobile' ||
        getParameterByName('env') === 'mobile'
      ) {
        const url = window.localStorage.getItem('url');
        const code = window.localStorage.getItem('code');
        if (
          (url != undefined && code != undefined) ||
          tokenLoginService.hasToken()
        ) {
          askFaceTouchId(
            configService,
            connectedUserService,
            authenticationService,
            tokenLoginService,
            errorMessage,
            resolve,
          );
        } else {
          authenticationService.goToLoginPage();
          resolve();
          // throw new Error('No url, code or refresh token found');
        }
      } else {
        executeLoading(
          configService,
          connectedUserService,
          authenticationService,
          errorMessage,
        )
          .pipe(first())
          .subscribe({
            next: () => {
              resolve();
            },
            error: () => {
              resolve();
            },
          });
      }
      // }
      // })
    });
  };
}

const APP_ROUTES = [
  {
    path: '',

    children: [
      {
        path: '',
        component: CgdisPortalMainComponent,
        loadChildren: () =>
          import('./layout/layout.module').then((m) => m.LayoutModule),
      },
    ],
  },
  {
    path: 'mobile-login',
    loadChildren: () =>
      import('./login/login.module').then((m) => m.LoginModule),
    canActivate: [MobileGuard],
  },
  {
    path: 'general-availability',
    loadChildren: () =>
      import('./general-availability/general-availability.module').then(
        (m) => m.GeneralAvailabilityModule,
      ),
    canActivate: [AuthGuard, RoleGuard],
    data: {
      expectedRoles: ['ROLE_PERMISSION_PDS_FILL_VIEW_ALL_AVAILABILITIES'],
    },
  },
];

@NgModule({
  imports: [
    BrowserModule,
    NgxWebstorageModule.forRoot(),

    HttpClientModule,
    FormsModule,
    BrowserAnimationsModule,
    ConfigModule.forRoot(),
    ErrorModule.forRoot(),
    AlertModule,
    ToastrModule.forRoot({
      positionClass: 'toast-bottom-right',
      maxOpened: 10,
      newestOnTop: false,
      timeOut: 5000,
      enableHtml: false,
      autoDismiss: true,
      //messageClass: string=;
      //titleClass: string;
      closeButton: true,
      // ,animate: string = 'flyRight';
    }),
    RouterModule.forRoot(APP_ROUTES, {
      useHash: true,
      paramsInheritanceStrategy: 'always',
    }),
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: createTranslateLoader,
        deps: [HttpClient, TranslationService],
      },
    }),
    DatetimeModule.forRoot(),
    SharedModule.forRoot(),
    DatepickerModule.forRoot(),
    RestModule.forRoot(),
    InputModule,
    DevelopmentModule,
    ErrorManagementModule.forRoot(),

    FormModule.forRoot(),
    SlidersModule,
    SecurityModule.forRoot(),
    NgbModule,
    LoginModule,
  ],
  declarations: [
    AppComponent,
    CgdisPortalMainComponent,
    SvgTempComponent,
    AppMobileComponent,
    AppSwitchComponent
  ],
  providers: [
    {
      provide: HAMMER_GESTURE_CONFIG,
      useClass: NewsHammerConfig,
    },
    {
      provide: APP_INITIALIZER,
      useFactory: initConfig,
      deps: [ConfigService],
      multi: true,
    },
    {
      provide: LocationStrategy,
      useClass: HashLocationStrategy,
    },
    {
      provide: APP_INITIALIZER,
      useFactory: initError,
      deps: [ErrorService],
      multi: true,
    },
    {
      provide: APP_INITIALIZER,
      useFactory: onAppInit1,
      deps: [
        ConfigService,
        ConnectedUserService,
        AuthenticationService,
        TokenLoginService,
        ERROR_MESSAGE_SERVICE,
      ],
      multi: true,
    },

    // {
    //   provide: HTTP_INTERCEPTORS,
    //   useClass: EPortalErrorInterceptor,
    //   multi: true
    // },
    InterventionTypesService,
    AssignmentService,
    OperationalFunctionService,
    EntityService,

    ToastService,
    AdminVehicleService,
    VehicleService,
    DatepickerConfigurationsService,
    ServicePlanTypeService,
    BoxService,
    PlanningService,
    ExportProfileService,
    AptitudesInformationFilterService,
    RestrictionInformationFilterService,
    Title,
    TokenLoginService,
    {
      provide: HTTP_INTERCEPTORS,
      useClass:
        environment.application === 'mobile'
          ? MobileTokenInterceptor
          : NothingInterceptor,
      multi: true,
    },
  ],
  exports: [TranslateModule],
  bootstrap: [AppSwitchComponent],
})
export class AppModule {}
