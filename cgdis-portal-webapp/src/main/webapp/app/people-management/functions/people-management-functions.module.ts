import { NgModule } from '@angular/core';
import { PeopleManagementFunctionsComponent } from './people-management-functions.component';
import { SharedModule } from '../../common/shared/shared.module';
import { EntityService } from '../../common/shared/services/entity.service';
import { NgxSelectModule } from 'ngx-select-ex';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DatatableModule } from '../../common/modules/datatable/datatable.module';
import { SimplePopupModule } from '../../common/modules/popup/simple-popup.module';
import { EpDatatableModule } from '@eportal/components';
import { PageTemplateModule } from '../../common/template/page-template/page-template.module';
import { PeopleManagementFunctionSummaryComponent } from './people-management-function-summary/people-management-function-summary.component';
import { PeopleManagementFunctionTableComponent } from './people-management-function-table/people-management-function-table.component';
import { MatExpansionModule } from '@angular/material/expansion';
import { PeopleManagementFunctionsService } from '@app/people-management/functions/people-management-functions.service';
import { PeopleManagementFunctionTablePersonComponent } from './people-management-function-table/people-management-function-table-person/people-management-function-table-person.component';
import { PeopleManagementFunctionTableFunctionsComponent } from './people-management-function-table/people-management-function-table-functions/people-management-function-table-functions.component';
import { PeopleManagementFunctionTableResultComponent } from './people-management-function-table/people-management-function-table-result/people-management-function-table-result.component';
import { ScrollTableModule } from '@app/common/modules/scroll-table/scroll-table.module';
import { TabsListModule } from '@app/common/modules/tabs-list/tabs-list.module';
import { ScrollTableService } from '@app/common/modules/scroll-table/scroll-table.service';
import { FormModule } from '@app/common/modules/form-module/form.module';
import { NgxTrimDirectiveModule } from 'ngx-trim-directive';
import { InputModule } from '@app/common/modules/input/input.module';
import { PaginationModule } from '@app/common/modules/pagination/pagination.module';
import { AssignmentService } from '@app/common/shared/services/assignment.service';
import { PerformanceRowComponent } from '@app/common/modules/performance-row/performance-row.component';
import { PeopleManagementFunctionSummaryHeaderComponent } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-header/people-management-function-summary-header.component';
import { PeopleManagementFunctionSummaryFilterFoComponent } from '@app/people-management/functions/people-management-function-summary/filters/people-management-function-summary-filter-fo.component';
import { PeopleManagementFunctionSummaryFiltersComponent } from '@app/people-management/functions/people-management-function-summary/filters/people-management-function-summary-filters.component';
import { PeopleManagementFunctionSummaryFilterInterventionTypeComponent } from '@app/people-management/functions/people-management-function-summary/filters/people-management-function-summary-filter-intervention-type.component';
import { PeopleManagementFunctionSummaryTagsComponent } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-tags/people-management-function-summary-tags.component';
import { EntityFilterModule } from '@app/common/modules/entity-filter/entity-filter.module';
import { ExpansionPanelComponent } from '@app/common/modules/expansion-panel/expansion-panel.component';
import { ExpansionPanelContentDirective } from '@app/common/modules/expansion-panel/expansion-panel-content.directive';
import { PeopleManagementFunctionByTypeComponent } from './people-management-function-bytype/people-management-function-by-type.component';
import { PeopleManagementFunctionByTypeIncsaComponent } from './people-management-function-bytype/incsa/people-management-function-by-type-incsa.component';
import { PeopleManagementFunctionByTypeTableComponent } from './people-management-function-bytype/people-management-function-by-type-table.component';
import { PeopleManagementFunctionByTypeDmsComponent } from '@app/people-management/functions/people-management-function-bytype/dms/people-management-function-by-type-dms.component';
import { PeopleManagementFunctionByTypeSamuComponent } from '@app/people-management/functions/people-management-function-bytype/samu/people-management-function-by-type-samu.component';
import { PeopleManagementFunctionByTypeOthersComponent } from '@app/people-management/functions/people-management-function-bytype/other/people-management-function-by-type-others.component';
import { PeopleManagementFunctionByTypeGisComponent } from '@app/people-management/functions/people-management-function-bytype/gis/people-management-function-by-type-gis.component';
import { PeopleManagementFunctionByTypeAmbulanceComponent } from '@app/people-management/functions/people-management-function-bytype/ambulance/people-management-function-by-type-ambulance.component';
import { PeopleManagementFunctionByTypeCommandmentComponent } from '@app/people-management/functions/people-management-function-bytype/commandment/people-management-function-by-type-commandment.component';
import { PeopleManagementFunctionTableFiltersComponent } from './people-management-function-table/people-management-function-table-filters/people-management-function-table-filters.component';
import { ExpansionFiltersContentDirective } from '@app/common/modules/expansion-filters/expansion-filters-content.directive';
import { ExpansionFiltersAloneComponent } from '@app/common/modules/expansion-filters/expansion-filters-alone.component';
import { PeopleManagementFunctionTableFiltersStatusComponent } from '@app/people-management/functions/people-management-function-table/people-management-function-table-filters/status/people-management-function-table-filters-status.component';
import { PeopleManagementFunctionTableFiltersAssignmentComponent } from './people-management-function-table/people-management-function-table-filters/assignment/people-management-function-table-filters-assignment.component';
import { PeopleManagementFunctionTableFiltersFunctionsComponent } from './people-management-function-table/people-management-function-table-filters/functions/people-management-function-table-filters-functions.component';
import { PeopleManagementFunctionTableFiltersVehicleComponent } from './people-management-function-table/people-management-function-table-filters/vehicle/people-management-function-table-filters-vehicle.component';
import { TranslateModule } from '@ngx-translate/core';
import { FilterTemplateComponent } from '@app/common/modules/filter-template/filter-template.component';
import { ToggleComponent } from '@app/common/modules/toggle/toggle.component';
import { GeneralInformationModule } from '@app/general-information/general-information.module';
import { PeopleManagementFunctionSummaryFunctionsComponent } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-functions/people-management-function-summary-functions.component';
import { PmfsfPortalLabelColumnComponent } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-functions/columns/portallabel/pmfsf-portal-label-column.component';

@NgModule({
  imports: [
    SharedModule,
    NgxSelectModule,
    FormsModule,
    DatatableModule,
    SimplePopupModule,
    EpDatatableModule,
    PageTemplateModule,
    MatExpansionModule,
    ScrollTableModule,
    TabsListModule,
    ReactiveFormsModule,
    FormModule,
    NgxTrimDirectiveModule,
    InputModule,
    PaginationModule,
    EntityFilterModule,
    PerformanceRowComponent,
    EntityFilterModule,
    ExpansionPanelComponent,
    ExpansionPanelContentDirective,
    ExpansionFiltersAloneComponent,
    ExpansionFiltersContentDirective,
    TranslateModule,
    FilterTemplateComponent,
    ToggleComponent,
    GeneralInformationModule,
    PeopleManagementFunctionSummaryFunctionsComponent,
    PmfsfPortalLabelColumnComponent,
  ],
  providers: [
    EntityService,
    PeopleManagementFunctionsService,
    ScrollTableService,
    AssignmentService,
  ],
  declarations: [
    PeopleManagementFunctionsComponent,
    PeopleManagementFunctionSummaryComponent,
    PeopleManagementFunctionTableComponent,
    PeopleManagementFunctionTablePersonComponent,
    PeopleManagementFunctionTableFunctionsComponent,
    PeopleManagementFunctionTableResultComponent,
    PeopleManagementFunctionSummaryHeaderComponent,
    PeopleManagementFunctionSummaryFiltersComponent,
    PeopleManagementFunctionSummaryFilterFoComponent,
    PeopleManagementFunctionSummaryFilterInterventionTypeComponent,
    PeopleManagementFunctionSummaryTagsComponent,
    PeopleManagementFunctionByTypeComponent,
    PeopleManagementFunctionByTypeIncsaComponent,
    PeopleManagementFunctionByTypeSamuComponent,
    PeopleManagementFunctionByTypeOthersComponent,
    PeopleManagementFunctionByTypeDmsComponent,
    PeopleManagementFunctionByTypeGisComponent,
    PeopleManagementFunctionByTypeAmbulanceComponent,
    PeopleManagementFunctionByTypeCommandmentComponent,
    PeopleManagementFunctionByTypeTableComponent,
    PeopleManagementFunctionTableFiltersComponent,
    PeopleManagementFunctionTableFiltersStatusComponent,
    PeopleManagementFunctionTableFiltersAssignmentComponent,
    PeopleManagementFunctionTableFiltersFunctionsComponent,
    PeopleManagementFunctionTableFiltersVehicleComponent,
  ],
})
export class PeopleManagementFunctionsModule {}
