import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { Entity } from '@app/model/entity.model';
import { PeopleManagementFunctionTableParameter } from '@app/people-management/functions/people-management-function-table/people-management-function-table-parameter';
import { PeopleManagementFunctionsService } from '@app/people-management/functions/people-management-functions.service';
import { InterventionType } from '@app/model/intervention-type.model';

@Component({
  selector: 'cgdis-portal-people-management-function-by-type-table',
  templateUrl: './people-management-function-by-type-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeopleManagementFunctionByTypeTableComponent implements OnChanges {
  @Input() titleKey: string;

  @Input() entity: Entity;

  @Input() interventionType: InterventionType;

  tableParameter: PeopleManagementFunctionTableParameter = {
    interventionTypeId: 0,
    entityId: 0,
  };

  constructor(
    private peopleManagementFunctionService: PeopleManagementFunctionsService,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    this.tableParameter = null;
  }

  contentDisplayed(displayed: boolean) {
    if (displayed) {
      this.tableParameter = {
        // functionsOperationals: response,
        interventionTypeId: this.interventionType.tecid,
        entityId: this.entity.tecid,
      };
    }
  }
}
