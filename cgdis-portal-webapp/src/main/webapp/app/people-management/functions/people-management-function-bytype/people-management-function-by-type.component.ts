import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { Entity } from '@app/model/entity.model';
import { InterventionTypesService } from '@app/common/shared/services/intervention-types.service';
import { InterventionType } from '@app/model/intervention-type.model';
import { map } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-people-management-function-by-type',
  templateUrl: './people-management-function-by-type.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeopleManagementFunctionByTypeComponent implements OnChanges {
  @Input() entity: Entity;

  @Input() subentities = false;

  protected interventionTypes: InterventionType[] = [];

  protected loading = false;

  constructor(
    private interventionTypesService: InterventionTypesService,
    private cd: ChangeDetectorRef,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.entity) {
      if (this.entity) {
        this.interventionTypes = [];
        this.loadInterventyonTypes();
      } else {
        this.interventionTypes = [];
      }
    }
  }

  private loadInterventyonTypes() {
    this.interventionTypesService
      .getCurrentForEntity(this.entity.tecid, this.subentities)
      .pipe(
        map((interventionTypes: InterventionType[]) =>
          interventionTypes
            .map((interventionType) => {
              return {
                order: this.getOrder(interventionType),
                type: interventionType,
              };
            })
            .sort((a, b) => a.order - b.order),
        ),
      )
      .subscribe((interventionTypes) => {
        this.interventionTypes = interventionTypes.map(
          (interventionType) => interventionType.type,
        );
        this.loading = false;
        this.cd.markForCheck();
      });
  }

  private getOrder(interventionType: InterventionType) {
    switch (interventionType.label) {
      case 'commandment':
        return 1;
      case 'fire':
        return 2;
      case 'ambulance':
        return 3;
      case 'gis':
        return 4;
      case 'dms':
        return 5;
      case 'samu':
        return 6;
      case 'others':
        return 7;
      default:
        return 100;
    }
  }
}
