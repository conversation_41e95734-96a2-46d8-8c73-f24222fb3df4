@for (interventionType of interventionTypes; track interventionType.label) {
  @switch (interventionType.label) {
    @case ('fire') {
      <cgdis-portal-people-management-function-by-type-incsa
        [entity]="entity"
        [interventionType]="interventionType"
      ></cgdis-portal-people-management-function-by-type-incsa>
    }
    @case ('samu') {
      <cgdis-portal-people-management-function-by-type-samu
        [entity]="entity"
        [interventionType]="interventionType"
      ></cgdis-portal-people-management-function-by-type-samu>
    }
    @case ('gis') {
      <cgdis-portal-people-management-function-by-type-gis
        [entity]="entity"
        [interventionType]="interventionType"
      ></cgdis-portal-people-management-function-by-type-gis>
    }
    @case ('others') {
      <cgdis-portal-people-management-function-by-type-others
        [entity]="entity"
        [interventionType]="interventionType"
      ></cgdis-portal-people-management-function-by-type-others>
    }
    @case ('commandment') {
      <cgdis-portal-people-management-function-by-type-commandment
        [entity]="entity"
        [interventionType]="interventionType"
      ></cgdis-portal-people-management-function-by-type-commandment>
    }
    @case ('ambulance') {
      <cgdis-portal-people-management-function-by-type-ambulance
        [entity]="entity"
        [interventionType]="interventionType"
      ></cgdis-portal-people-management-function-by-type-ambulance>
    }
    @case ('dms') {
      <cgdis-portal-people-management-function-by-type-dms
        [entity]="entity"
        [interventionType]="interventionType"
      ></cgdis-portal-people-management-function-by-type-dms>
    }

  }
}


