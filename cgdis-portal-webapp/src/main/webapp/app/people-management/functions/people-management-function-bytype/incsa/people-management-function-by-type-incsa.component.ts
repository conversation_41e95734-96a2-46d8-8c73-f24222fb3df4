import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Entity } from '@app/model/entity.model';
import { InterventionType } from '@app/model/intervention-type.model';

@Component({
  selector: 'cgdis-portal-people-management-function-by-type-incsa',
  templateUrl: './people-management-function-by-type-incsa.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeopleManagementFunctionByTypeIncsaComponent {
  @Input() entity: Entity;

  @Input() interventionType: InterventionType;
}
