import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Entity } from '@app/model/entity.model';
import { InterventionType } from '@app/model/intervention-type.model';

@Component({
  selector: 'cgdis-portal-people-management-function-by-type-dms',
  templateUrl: './people-management-function-by-type-dms.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeopleManagementFunctionByTypeDmsComponent {
  @Input() entity: Entity;

  @Input() interventionType: InterventionType;
}
