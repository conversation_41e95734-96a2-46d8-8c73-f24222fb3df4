import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
} from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { FunctionOperationalSummary } from '@app/model/function-operational-summary';
import { EpDatatableModule } from '@eportal/components';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector:
    'cgdis-portal-people-management-function-summary-functions-portal-label-column',
  templateUrl: './pmfsf-portal-label-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(() => PmfsfPortalLabelColumnComponent),
    },
  ],
  imports: [EpDatatableModule, TranslateModule],
})
export class PmfsfPortalLabelColumnComponent extends CgdisDatatableColumnComponent<FunctionOperationalSummary> {
  @Input() isMobile = false;
}
