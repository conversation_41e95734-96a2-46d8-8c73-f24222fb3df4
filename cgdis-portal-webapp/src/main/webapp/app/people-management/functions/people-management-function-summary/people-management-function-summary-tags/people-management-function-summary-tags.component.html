<cgdis-portal-cgdisdatatable *ngIf="parameter"
                             [datatableService]="peopleManagementFunctionSummaryService"
                             [sorts]="[{dir:'asc',prop:'portalLabel'}]"
                             [id]="'people-management-summary-tags-table-Id'">

  <!--  <ng-template #template let-row="row">-->
  <!--    <div class="person-detail-row margin">-->
  <!--      <cgdis-portal-people-management-function-summary-detail [operationFunctionTecId]="cast(row).fo.tecid"-->
  <!--                                                              [status]="parameter.status"-->
  <!--                                                              [parameter]="parameter"-->
  <!--      ></cgdis-portal-people-management-function-summary-detail>-->
  <!--    </div>-->
  <!--  </ng-template>-->

  <cgdis-portal-datatable-select-filter [filterName]="'tecid'"
                                        [hidden]="true"
                                        [datatableService]="peopleManagementFunctionSummaryService"
                                        [allowClear]="true"
                                        [customFormControl]="foTecidsFormControl"
                                        [multiple]="true"
  ></cgdis-portal-datatable-select-filter>

  <cgdis-portal-datatable-select-filter [filterName]="'foInterventionTypeTecid'"
                                        [hidden]="true"
                                        [datatableService]="peopleManagementFunctionSummaryService"
                                        [allowClear]="true"
                                        [customFormControl]="interventionTypesTecidsFormControl"
                                        [multiple]="true"
  ></cgdis-portal-datatable-select-filter>


  <ep-datatable-column [columnName]="'portalLabel'" [sortable]="false" [flexGrow]="1">
    <ng-template epDatatableHeader>
        <span
          [translate]="isMobile ? 'people_management.functions.function_operational_tag_mobile' : 'people_management.functions.function_operational_tag'"></span>
    </ng-template>
    <ng-template epDatatableCell let-context>
      <span class="strong">{{ cast(context.row).tag.name }}</span>
    </ng-template>
  </ep-datatable-column>

  <ep-datatable-column [columnName]="'number'" [sortable]="false" [flexGrow]="1">
    <ng-template epDatatableHeader>
        <span
          [translate]="isMobile ? 'people_management.functions.person_number_pro_mobile' : 'people_management.functions.person_number_pro'"></span>
    </ng-template>
    <ng-template epDatatableCell let-context>
      {{ cast(context.row).countPro }}
    </ng-template>
  </ep-datatable-column>
  <ep-datatable-column [columnName]="'numberVol'" [sortable]="false" [flexGrow]="parameter.status.volunteer ? 1:0">
    <ng-template epDatatableHeader>
        <span
          [translate]="isMobile ? 'people_management.functions.person_number_vol_mobile' : 'people_management.functions.person_number_vol'"></span>
    </ng-template>
    <ng-template epDatatableCell let-context>
      {{ cast(context.row).countVol }}
    </ng-template>
  </ep-datatable-column>
  <ep-datatable-column [columnName]="'numberExt'" [sortable]="false" [flexGrow]="1">
    <ng-template epDatatableHeader>
        <span
          [translate]="isMobile ? 'people_management.functions.person_number_total_distinct_mobile' : 'people_management.functions.person_number_total_distinct'"></span>
    </ng-template>
    <ng-template epDatatableCell let-context>
      <span class="strong">{{ cast(context.row).countTotalDistinct }}</span>
    </ng-template>
  </ep-datatable-column>
  <ep-datatable-column [columnName]="'numberExt'" [sortable]="false"
                       [flexGrow]="parameter.status.primaryTypeTechnicalPro ? 1:0">
    <ng-template epDatatableHeader>
        <span
          [translate]="isMobile ? 'people_management.functions.person_number_pro_tec_mobile' : 'people_management.functions.person_number_pro_tec'"></span>
    </ng-template>
    <ng-template epDatatableCell let-context>
      {{ cast(context.row).countProTec }}
    </ng-template>
  </ep-datatable-column>
  <ep-datatable-column [columnName]="'numberExt'" [sortable]="false"
                       [flexGrow]="parameter.status.primaryTypeTechnicalVol ? 1:0">
    <ng-template epDatatableHeader>
        <span
          [translate]="isMobile ? 'people_management.functions.person_number_vol_tec_mobile' : 'people_management.functions.person_number_vol_tec'"></span>
    </ng-template>
    <ng-template epDatatableCell let-context>
      {{ cast(context.row).countVolTec }}
    </ng-template>
  </ep-datatable-column>

</cgdis-portal-cgdisdatatable>
