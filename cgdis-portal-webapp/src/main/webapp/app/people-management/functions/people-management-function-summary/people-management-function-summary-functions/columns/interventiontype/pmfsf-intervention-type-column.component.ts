import { ChangeDetectionStrategy, Component, forwardRef } from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { FunctionOperationalSummary } from '@app/model/function-operational-summary';
import { EpDatatableModule } from '@eportal/components';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector:
    'cgdis-portal-people-management-function-summary-functions-intervention-type-column',
  templateUrl: './pmfsf-intervention-type-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () =>
          PeopleManagementFunctionSummaryFunctionsInterventionTypeColumnComponent,
      ),
    },
  ],
  imports: [EpDatatableModule, TranslateModule],
})
export class PeopleManagementFunctionSummaryFunctionsInterventionTypeColumnComponent extends CgdisDatatableColumnComponent<FunctionOperationalSummary> {}
