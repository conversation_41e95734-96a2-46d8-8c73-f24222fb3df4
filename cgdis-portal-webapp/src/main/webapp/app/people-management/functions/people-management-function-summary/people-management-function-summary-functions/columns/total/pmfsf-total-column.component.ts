import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
} from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { FunctionOperationalSummary } from '@app/model/function-operational-summary';
import { EpDatatableModule } from '@eportal/components';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'cgdis-portal-pmfsf-total-column',
  templateUrl: './pmfsf-total-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () => PeopleManagementFunctionSummaryFunctionsTotalColumnComponent,
      ),
    },
  ],
  imports: [EpDatatableModule, TranslateModule],
})
export class PeopleManagementFunctionSummaryFunctionsTotalColumnComponent extends CgdisDatatableColumnComponent<FunctionOperationalSummary> {
  @Input() isMobile = false;
}
