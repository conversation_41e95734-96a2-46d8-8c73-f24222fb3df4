<cgdis-portal-cgdisdatatable
  [datatableService]="peopleManagementFunctionPersonListService"
  [sorts]="[{dir:'asc',prop:'portalLabel'}]"
  [id]="'people-management-summary-table-Id'">


  <ep-datatable-column [columnName]="'firstName'" [sortable]="false" [flexGrow]="1">
    <ng-template epDatatableHeader>
        <span [translate]="'people_management.functions.details.firstName'"></span>
    </ng-template>
    <ng-template epDatatableCell let-context>
      {{ cast(context.row).firstName }}
    </ng-template>
  </ep-datatable-column>

  <ep-datatable-column [columnName]="'lastName'" [sortable]="false" [flexGrow]="1">
    <ng-template epDatatableHeader>
        <span [translate]="'people_management.functions.details.lastName'"></span>
    </ng-template>
    <ng-template epDatatableCell let-context>
      {{ cast(context.row).lastName }}
    </ng-template>
  </ep-datatable-column>

  <ep-datatable-column [columnName]="'cgdisRegistrationNumber'" [sortable]="false" [flexGrow]="1">
    <ng-template epDatatableHeader>
        <span [translate]="'people_management.functions.details.cgdisRegistrationNumber'"></span>
    </ng-template>
    <ng-template epDatatableCell let-context>
      {{ cast(context.row).cgdisRegistrationNumber }}
    </ng-template>
  </ep-datatable-column>

  <!-- professional (boolean) -->
  <ep-datatable-column [columnName]="'isProfessional'" [sortable]="false" [flexGrow]="status.professional ? 1:0">
    <ng-template epDatatableHeader>
      {{'general_information.operational_grades.list.header.professional_desktop_reduced' | translate}}
    </ng-template>
    <ng-template epDatatableCell let-context>
      {{ (cast(context.row).isProfessional ? '✔' : '-') }}
    </ng-template>
  </ep-datatable-column>

  <ep-datatable-column [columnName]="'isVolunteer'" [sortable]="false" [flexGrow]="status.volunteer ? 1:0">
    <ng-template epDatatableHeader>
      {{'general_information.operational_grades.list.header.volunteer_desktop_reduced' | translate}}
    </ng-template>
    <ng-template epDatatableCell let-context>
      {{ (cast(context.row).isVolunteer ? '✔' : '-') }}
    </ng-template>
  </ep-datatable-column>

  <!-- samu (boolean) -->
  <ep-datatable-column [columnName]="'isSamu'" [sortable]="false" [flexGrow]="status.external ? 1:0">
    <ng-template epDatatableHeader>
      {{'general_information.operational_grades.list.header.samu' | translate}}
    </ng-template>
    <ng-template epDatatableCell let-context>
      {{ (cast(context.row).isExternal ? '✔' : '-') }}
    </ng-template>
  </ep-datatable-column>

</cgdis-portal-cgdisdatatable>
