import { Injectable } from '@angular/core';
import { IAllRestResource, RestService } from '@eportal/core';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { CgdisDatatableDynamicRestService } from '@app/common/modules/datatable/cgdisdatatable-dynamic-rest-service';
import { FunctionOperationalSummaryByTag } from '@app/model/functionoperational/function-operational-summary-by-tags.model';

export interface PeopleManagementFunctionTagsDynamicParams {
  entityTecid: number;
  subentities: boolean;
}

@Injectable()
export class PeopleManagementFunctionSummaryTagsService extends CgdisDatatableDynamicRestService<
  FunctionOperationalSummaryByTag,
  PeopleManagementFunctionTagsDynamicParams
> {
  constructor(
    private restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService, true);
  }

  getRestResource(
    params: PeopleManagementFunctionTagsDynamicParams,
  ): IAllRestResource<FunctionOperationalSummaryByTag> {
    const routes = [
      'function-operationals',
      params.entityTecid.toString(),
      'summary',
      'tags',
    ];
    if (params.subentities) {
      routes.push('subentities');
    }
    return this.restService.all(...routes);
  }
}
