import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { FunctionOperationalSummaryByInterventionTypeDto } from '@app/people-management/functions/people-management-function-summary/model/function-operational-summary-by-intervention-type-dto';
import { PeopleManagementFunctionSummaryFilters } from '@app/people-management/functions/people-management-function-summary/filters/people-management-function-summary-filters';

export interface PeopleManagementFunctionSummaryDynamicParams {
  entityTecid: number;
  subentities: boolean;
}

@Injectable()
export class PeopleManagementFunctionSummaryHeaderService {
  constructor(private restService: RestService) {}

  getSummaryByInterventionTypes(
    entityId: number,
    subentities: boolean,
    filters: PeopleManagementFunctionSummaryFilters,
  ): Observable<FunctionOperationalSummaryByInterventionTypeDto[]> {
    return this.restService
      .one<
        FunctionOperationalSummaryByInterventionTypeDto[]
      >('function-operationals', entityId.toString(), 'interventiontypes', 'summary')
      .get({
        subentities,
        ...filters,
      });
  }
}
