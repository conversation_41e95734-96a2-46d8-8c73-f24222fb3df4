import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { PeopleManagementFunctionSummaryParameter } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-parameter';
import { FunctionOperationalSummaryByInterventionTypeDto } from '@app/people-management/functions/people-management-function-summary/model/function-operational-summary-by-intervention-type-dto';
import { PeopleManagementFunctionSummaryHeaderService } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-header/people-management-function-summary-header.service';
import { PeopleManagementFunctionSummaryFilters } from '@app/people-management/functions/people-management-function-summary/filters/people-management-function-summary-filters';
import {
  PerformanceRow,
  PerformanceRowGroup,
  PerformanceRowGroupItem,
} from '@app/common/modules/performance-row/performance-row.model';

@Component({
  selector: 'cgdis-portal-people-management-function-summary-header',
  templateUrl: './people-management-function-summary-header.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [PeopleManagementFunctionSummaryHeaderService],
})
export class PeopleManagementFunctionSummaryHeaderComponent
  implements OnChanges, OnInit
{
  @Input() params: PeopleManagementFunctionSummaryParameter;
  @Input() filters: PeopleManagementFunctionSummaryFilters;
  @Input() isMobile: boolean;

  summaryByInterventionTypes: FunctionOperationalSummaryByInterventionTypeDto[];
  row: PerformanceRow<number>;
  loading = true;

  constructor(
    private peopleManagementFunctionSummaryService: PeopleManagementFunctionSummaryHeaderService,
    private cd: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    let currentValue = changes.params?.currentValue;
    let previousValue = changes.params?.previousValue;
    let mustInit =
      (currentValue &&
        (currentValue.entityId !== previousValue?.entityId ||
          currentValue.subentities !== previousValue?.subentities)) ||
      changes.filters != undefined;

    if (this.params && mustInit) {
      this.initCount();
    }
  }

  initCount() {
    this.loading = true;
    this.cd.markForCheck();
    this.peopleManagementFunctionSummaryService
      .getSummaryByInterventionTypes(
        this.params.entityId,
        this.params.subentities,
        this.filters,
      )
      .subscribe({
        next: (result) => {
          this.summaryByInterventionTypes = result;
          this.row = new PerformanceRow<number>({
            groups: this.summaryByInterventionTypes
              .sort((a, b) =>
                a.interventionType.label.localeCompare(
                  b.interventionType.label,
                ),
              )
              .map(this.mapSummaryToPerformanceGroup),
          });

          this.loading = false;
          this.cd.markForCheck();
        },
        complete: () => {
          this.loading = false;
          this.cd.markForCheck();
        },
      });
  }

  castContext(context: any) {
    return context as number;
  }

  private mapSummaryToPerformanceGroup(
    summary: FunctionOperationalSummaryByInterventionTypeDto,
  ): PerformanceRowGroup<number> {
    return new PerformanceRowGroup({
      groupTitleKey: `intervention.types.${summary.interventionType.label}`,
      items: [
        new PerformanceRowGroupItem<number>({
          itemTitleKey:
            'people_management.functions.summaryheader.totalpersons',
          value: summary.totalPersons,
        }),
        new PerformanceRowGroupItem<number>({
          itemTitleKey: 'people_management.functions.summaryheader.totalfos',
          value: summary.totalFunctions,
        }),
      ],
    });
  }
}
