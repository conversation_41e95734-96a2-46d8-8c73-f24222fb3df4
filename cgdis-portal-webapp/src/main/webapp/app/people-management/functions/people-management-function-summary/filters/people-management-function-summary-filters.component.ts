import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import { PeopleManagementFunctionSummaryParameter } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-parameter';
import { PeopleManagementFunctionSummaryFilters } from '@app/people-management/functions/people-management-function-summary/filters/people-management-function-summary-filters';

@Component({
  selector: 'cgdis-portal-people-management-function-summary-filters',
  templateUrl: './people-management-function-summary-filters.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeopleManagementFunctionSummaryFiltersComponent {
  @Input() parameter: PeopleManagementFunctionSummaryParameter;

  @Output() filterUpdated =
    new EventEmitter<PeopleManagementFunctionSummaryFilters>();

  private currentFoFilter: number[] = [];
  private currentInterventionTypeFilter: number[] = [];

  selectsFo($event: number[]) {
    this.currentFoFilter = $event ? $event : [];
    this.computeFilters();
  }

  selectsInterventionType($event: number[]) {
    this.currentInterventionTypeFilter = $event ? $event : [];
    this.computeFilters();
  }

  private computeFilters() {
    this.filterUpdated.emit(
      new PeopleManagementFunctionSummaryFilters({
        foTecids: this.currentFoFilter,
        interventionTypeTecids: this.currentInterventionTypeFilter,
      }),
    );
  }
}
