import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { FunctionOperationalSummary } from '@app/model/function-operational-summary';
import { AssignmentStatus } from '@app/model/assignment-status.model';
import { FormControl } from '@angular/forms';
import { PeopleManagementFunctionSummaryFilters } from '@app/people-management/functions/people-management-function-summary/filters/people-management-function-summary-filters';
import { PeopleManagementFunctionSummaryFunctionsService } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-functions/people-management-function-summary-functions.service';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { NgIf } from '@angular/common';
import { PmfsfPortalLabelColumnComponent } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-functions/columns/portallabel/pmfsf-portal-label-column.component';
import { PeopleManagementFunctionSummaryDetailComponent } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-detail/people-management-function-summary-detail.component';
import { PeopleManagementFunctionSummaryFunctionsInterventionTypeColumnComponent } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-functions/columns/interventiontype/pmfsf-intervention-type-column.component';
import { PeopleManagementFunctionSummaryFunctionsProNumberColumnComponent } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-functions/columns/pronumber/pmfsf-pro-number-column.component';
import { PeopleManagementFunctionSummaryFunctionsVolNumberColumnComponent } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-functions/columns/volnumber/pmfsf-vol-number-column.component';
import { PeopleManagementFunctionSummaryFunctionsTotalColumnComponent } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-functions/columns/total/pmfsf-total-column.component';
import { PeopleManagementFunctionSummaryFunctionsProTecNumberColumnComponent } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-functions/columns/protecnumber/pmfsf-pro-tec-number-column.component';
import { PeopleManagementFunctionSummaryFunctionsVolTecNumberColumnComponent } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-functions/columns/voltecnumber/pmfsf-vol-tec-number-column.component';

export interface PeopleManagementFunctionSummaryFunctionsParameters {
  entityId: number;
  subentities: boolean;
  status: AssignmentStatus;
}

@Component({
  selector: 'cgdis-portal-people-management-function-summary-functions',
  templateUrl: './people-management-function-summary-functions.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  providers: [PeopleManagementFunctionSummaryFunctionsService],
  imports: [
    DatatableModule,
    NgIf,
    PmfsfPortalLabelColumnComponent,
    PeopleManagementFunctionSummaryDetailComponent,
    PeopleManagementFunctionSummaryFunctionsInterventionTypeColumnComponent,
    PeopleManagementFunctionSummaryFunctionsProNumberColumnComponent,
    PeopleManagementFunctionSummaryFunctionsVolNumberColumnComponent,
    PeopleManagementFunctionSummaryFunctionsTotalColumnComponent,
    PeopleManagementFunctionSummaryFunctionsProTecNumberColumnComponent,
    PeopleManagementFunctionSummaryFunctionsVolTecNumberColumnComponent,
  ],
})
export class PeopleManagementFunctionSummaryFunctionsComponent
  implements OnChanges
{
  @Input() parameter: PeopleManagementFunctionSummaryFunctionsParameters;

  @Input() isMobile: boolean;

  @Input() currentFilters: PeopleManagementFunctionSummaryFilters;

  foTecidsFormControl = new FormControl<number[]>(undefined);
  interventionTypesTecidsFormControl = new FormControl<number[]>(undefined);

  constructor(
    public peopleManagementFunctionSummaryService: PeopleManagementFunctionSummaryFunctionsService,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes.parameter &&
      ((changes.parameter.currentValue?.entityId !==
        changes.parameter.previousValue?.entityId &&
        changes.parameter.currentValue?.entityId != undefined) ||
        changes.parameter.currentValue?.subentities !==
          changes.parameter.previousValue?.subentities)
    ) {
      this.startSearch();
    }

    if (changes.currentFilters) {
      this.filterUpdated();
    }
  }

  private startSearch() {
    this.peopleManagementFunctionSummaryService.setDynamicRestParams({
      entityTecid: this.parameter.entityId,
      subentities: this.parameter.subentities,
    });
  }

  filterUpdated() {
    if (
      this.currentFilters &&
      (this.currentFilters.foTecids ||
        this.currentFilters.interventionTypeTecids)
    ) {
      this.foTecidsFormControl.setValue(
        this.currentFilters.foTecids.length > 0
          ? this.currentFilters.foTecids
          : null,
      );
      this.interventionTypesTecidsFormControl.setValue(
        this.currentFilters.interventionTypeTecids.length > 0
          ? this.currentFilters.interventionTypeTecids
          : null,
      );
    }
  }

  cast(row: FunctionOperationalSummary) {
    return row as FunctionOperationalSummary;
  }
}
