import { Injectable } from '@angular/core';
import { IAllRestResource, RestService } from '@eportal/core';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { FunctionOperationalSummary } from '@app/model/function-operational-summary';
import { CgdisDatatableDynamicRestService } from '@app/common/modules/datatable/cgdisdatatable-dynamic-rest-service';

export interface PeopleManagementFunctionSummaryDynamicParams {
  entityTecid: number;
  subentities: boolean;
}

@Injectable()
export class PeopleManagementFunctionSummaryFunctionsService extends CgdisDatatableDynamicRestService<
  FunctionOperationalSummary,
  PeopleManagementFunctionSummaryDynamicParams
> {
  constructor(
    private restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService, true);
  }

  getRestResource(
    params: PeopleManagementFunctionSummaryDynamicParams,
  ): IAllRestResource<FunctionOperationalSummary> {
    const routes = [
      'function-operationals',
      params.entityTecid.toString(),
      'summary',
      'functions',
    ];
    if (params.subentities) {
      routes.push('subentities');
    }
    return this.restService.all(...routes);
  }
}
