import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChang<PERSON>,
  OnDestroy,
  OnInit,
  SimpleChang<PERSON>,
} from '@angular/core';
import { Subject, Subscription } from 'rxjs';
import * as _ from 'lodash';
import { take, takeUntil } from 'rxjs/operators';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { PeopleManagementFunctionSummaryParameter } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-parameter';
import { PeopleManagementFunctionSummaryFilters } from '@app/people-management/functions/people-management-function-summary/filters/people-management-function-summary-filters';
import { AssignmentService } from '@app/common/shared/services/assignment.service';
import { AssignmentStatus } from '@app/model/assignment-status.model';
import { PeopleManagementFunctionSummaryFunctionsParameters } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-functions/people-management-function-summary-functions.component';
import { PeopleManagementFunctionSummaryTagsParameters } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-tags/people-management-function-summary-tags.component';

@Component({
  selector: 'cgdis-portal-people-management-function-summary',
  templateUrl: './people-management-function-summary.component.html',
  styles: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeopleManagementFunctionSummaryComponent
  implements OnInit, OnChanges, OnDestroy
{
  @Input() parameter: PeopleManagementFunctionSummaryParameter;

  isMobile: boolean = false;

  currentFilters: PeopleManagementFunctionSummaryFilters;

  functionParameter: PeopleManagementFunctionSummaryFunctionsParameters;
  tagParameter: PeopleManagementFunctionSummaryTagsParameters;

  private status: AssignmentStatus;

  private _unsubscribe$ = new Subject<void>();
  private subscriptions: Subscription[] = [];

  constructor(
    private breakpointObserver: BreakpointObserver,
    private assignmentService: AssignmentService,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 768px)'])
        .pipe(takeUntil(this._unsubscribe$))
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit() {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.parameter) {
      if (this.parameter.entityId) {
        if (
          (changes.parameter.currentValue?.entityId !==
            changes.parameter.previousValue?.entityId &&
            changes.parameter.currentValue?.entityId != undefined) ||
          changes.parameter.currentValue?.subentities !==
            changes.parameter.previousValue?.subentities
        ) {
          this.newEntity();
        } else {
          this.startSearch();
        }
      }
    }
  }

  ngOnDestroy(): void {
    _.each(this.subscriptions, (oneSubscription) =>
      oneSubscription.unsubscribe(),
    );

    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }

  private startSearch() {
    this.functionParameter = {
      ...this.parameter,
      status: this.status,
    };
    this.tagParameter = {
      ...this.parameter,
      status: this.status,
    };
  }

  private newEntity() {
    this.assignmentService
      .getAllStatuesForEntity(
        this.parameter.entityId,
        this.parameter.subentities,
        true,
      )
      .pipe(take(1))
      .subscribe({
        next: (result) => {
          this.status = result;
          this.startSearch();
          this.cd.markForCheck();
        },
      });
  }

  filterUpdated($event: PeopleManagementFunctionSummaryFilters) {
    this.currentFilters = $event;
  }
}
