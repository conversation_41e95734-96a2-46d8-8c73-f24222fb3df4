import { Injectable } from '@angular/core';
import { IAllRestResource, RestService } from '@eportal/core';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { PersonLight } from '@app/model/person/person-light.model';
import { CgdisDatatableDynamicRestService } from '@app/common/modules/datatable/cgdisdatatable-dynamic-rest-service';

export interface PeopleManagementFunctionPersonListDynamicParams {
  entityTecid: number;
  subentities: boolean;
  foTecid: number;
}

@Injectable()
export class PeopleManagementFunctionPersonListService extends CgdisDatatableDynamicRestService<
  PersonLight,
  PeopleManagementFunctionPersonListDynamicParams
> {
  constructor(
    private restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService, true);
  }

  getRestResource(
    params: PeopleManagementFunctionPersonListDynamicParams,
  ): IAllRestResource<PersonLight> {
    const routes = [
      'function-operationals',
      params.entityTecid.toString(),
      'personList',
    ];
    if (params.subentities) {
      routes.push('subentities');
    }
    routes.push('fos', params.foTecid.toString());
    return this.restService.all(...routes);
  }
}
