<ep-datatable-column [columnName]="'number'" [sortable]="false" [flexGrow]="1">
  <ng-template epDatatableHeader>
        <span
          [translate]="isMobile ? 'people_management.functions.person_number_pro_mobile' : 'people_management.functions.person_number_pro'"></span>
  </ng-template>
  <ng-template epDatatableCell let-context>
    {{ cast(context.row).countPro }}
  </ng-template>
</ep-datatable-column>
