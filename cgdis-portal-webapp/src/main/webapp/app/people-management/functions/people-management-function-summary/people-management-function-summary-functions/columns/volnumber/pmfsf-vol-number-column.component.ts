import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
} from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { FunctionOperationalSummary } from '@app/model/function-operational-summary';
import { EpDatatableModule } from '@eportal/components';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'cgdis-portal-pmfsf-vol-number-column',
  templateUrl: './pmfsf-vol-number-column.component.html',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () => PeopleManagementFunctionSummaryFunctionsVolNumberColumnComponent,
      ),
    },
  ],
  imports: [EpDatatableModule, TranslateModule],
})
export class PeopleManagementFunctionSummaryFunctionsVolNumberColumnComponent extends CgdisDatatableColumnComponent<FunctionOperationalSummary> {
  @Input() isMobile = false;
}
