<ep-datatable-column [columnName]="'numberExt'" [sortable]="false" [flexGrow]="1">
  <ng-template epDatatableHeader>
        <span
          [translate]="isMobile ? 'people_management.functions.person_number_total_distinct_mobile' : 'people_management.functions.person_number_total_distinct'"></span>
  </ng-template>
  <ng-template epDatatableCell let-context>
    <span class="strong">{{ cast(context.row).countTotalDistinct }}</span>
  </ng-template>
</ep-datatable-column>
