import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { FormControl } from '@angular/forms';
import { INgxSelectOption } from 'ngx-select-ex';
import { PeopleManagementFunctionsService } from '@app/people-management/functions/people-management-functions.service';
import { PeopleManagementFunctionSummaryParameter } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-parameter';

@Component({
  selector: 'cgdis-portal-people-management-function-summary-filter-fo',
  templateUrl: './people-management-function-summary-filter-fo.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeopleManagementFunctionSummaryFilterFoComponent
  implements OnChanges
{
  @Input() parameter: PeopleManagementFunctionSummaryParameter;

  @Output() selectedValues = new EventEmitter<number[]>();

  possibleValues: FieldOption<number>[];
  filterFormControl = new FormControl<number[]>(undefined);

  constructor(
    private peopleManagementFunctionsService: PeopleManagementFunctionsService,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.parameter) {
      const currentEntityTecid = changes.parameter?.currentValue?.entityId;
      const previousEntityTecid = changes.parameter?.previousValue?.entityId;

      if (currentEntityTecid !== previousEntityTecid) {
        this.loadFoOptions();
      }
    }
  }

  private loadFoOptions() {
    this.peopleManagementFunctionsService
      .getAllByEntityId(this.parameter.entityId, this.parameter.subentities)
      .subscribe({
        next: (response) => {
          console.error('Error loading function options', response);
          this.possibleValues = response.map((fo) => {
            return new FieldOption({
              label: `${fo.portalLabel} (${fo.label})`,
              value: fo.tecid,
            });
          });
        },
      });
  }

  selectionChanged($event: INgxSelectOption[]) {
    this.selectedValues.emit($event.map((value) => value.data.value));
  }
}
