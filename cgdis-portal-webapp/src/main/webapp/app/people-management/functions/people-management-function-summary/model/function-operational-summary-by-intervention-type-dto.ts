import { InterventionType } from '@app/model/intervention-type.model';

export class FunctionOperationalSummaryByInterventionTypeDto {
  interventionType: InterventionType;
  totalPersons: number;
  totalFunctions: number;

  constructor(args: FunctionOperationalSummaryByInterventionTypeDto) {
    this.interventionType = args.interventionType;
    this.totalPersons = args.totalPersons;
    this.totalFunctions = args.totalFunctions;
  }
}
