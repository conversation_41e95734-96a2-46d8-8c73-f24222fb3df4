<ep-datatable-column [columnName]="'portalLabel'" [sortable]="false" [flexGrow]="1">
  <ng-template epDatatableHeader>
        <span
          [translate]="isMobile ? 'people_management.functions.function_operational_mobile' : 'people_management.functions.function_operational'"></span>
  </ng-template>
  <ng-template epDatatableCell let-context>
    <span class="strong">{{ cast(context.row).fo.portalLabel }}</span>
  </ng-template>
</ep-datatable-column>
