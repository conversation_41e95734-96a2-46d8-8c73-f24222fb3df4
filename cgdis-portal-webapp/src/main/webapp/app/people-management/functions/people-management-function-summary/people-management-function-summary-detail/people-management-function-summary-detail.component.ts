import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { PeopleManagementFunctionPersonListService } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-detail/people-management-function-person-list.service';
import { PeopleManagementFunctionSummaryParameter } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-parameter';
import { AssignmentStatus } from '@app/model/assignment-status.model';
import { PersonLight } from '@app/model/person/person-light.model';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'cgdis-portal-people-management-function-summary-detail',
  templateUrl: './people-management-function-summary-detail.component.html',
  styles: [],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [PeopleManagementFunctionPersonListService],
  imports: [DatatableModule, EpDatatableModule, TranslateModule],
})
export class PeopleManagementFunctionSummaryDetailComponent
  implements OnInit, OnChanges
{
  @Input()
  public operationFunctionTecId: number;

  @Input() status: AssignmentStatus;

  @Input()
  parameter: PeopleManagementFunctionSummaryParameter;

  constructor(
    public peopleManagementFunctionPersonListService: PeopleManagementFunctionPersonListService,
  ) {}

  ngOnInit() {}

  ngOnChanges(changes: SimpleChanges) {
    if (
      changes.operationFunctionTecId.currentValue != undefined &&
      changes.parameter.currentValue != null
    ) {
      this.peopleManagementFunctionPersonListService.setDynamicRestParams({
        entityTecid: this.parameter.entityId,
        subentities: this.parameter.subentities,
        foTecid: this.operationFunctionTecId,
      });
    }
  }

  cast(row: any) {
    return row as PersonLight;
  }
}
