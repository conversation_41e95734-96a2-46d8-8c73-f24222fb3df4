<cgdis-portal-datatable-select-filter [filterName]="'tecid'"
                                      [hidden]="true"
                                      [datatableService]="peopleManagementFunctionSummaryService"
                                      [allowClear]="true"
                                      [customFormControl]="foTecidsFormControl"
                                      [multiple]="true"
></cgdis-portal-datatable-select-filter>

<cgdis-portal-datatable-select-filter [filterName]="'foInterventionTypeTecid'"
                                      [hidden]="true"
                                      [datatableService]="peopleManagementFunctionSummaryService"
                                      [allowClear]="true"
                                      [customFormControl]="interventionTypesTecidsFormControl"
                                      [multiple]="true"
></cgdis-portal-datatable-select-filter>


<cgdis-portal-cgdisdatatable *ngIf="parameter"
                             [datatableService]="peopleManagementFunctionSummaryService"
                             [autoMobileFilters]="true"
                             [sorts]="[{dir:'asc',prop:'portalLabel'}]"
                             [id]="'people-management-summary-table-Id'">

  <ng-template #template let-row="row">
    <div class="person-detail-row margin">
      <cgdis-portal-people-management-function-summary-detail [operationFunctionTecId]="cast(row).fo.tecid"
                                                              [status]="parameter.status"
                                                              [parameter]="parameter"
      ></cgdis-portal-people-management-function-summary-detail>
    </div>
  </ng-template>



  <cgdis-portal-people-management-function-summary-functions-portal-label-column
    cgdisDatatableColumn
    [isMobile]="isMobile"
  ></cgdis-portal-people-management-function-summary-functions-portal-label-column>

  <cgdis-portal-people-management-function-summary-functions-intervention-type-column
    cgdisDatatableColumn
  ></cgdis-portal-people-management-function-summary-functions-intervention-type-column>

  <cgdis-portal-pmfsf-pro-number-column
    cgdisDatatableColumn
    [isMobile]="isMobile"
  ></cgdis-portal-pmfsf-pro-number-column>

  <cgdis-portal-pmfsf-vol-number-column
    cgdisDatatableColumn
    *ngIf="parameter.status.volunteer"
    [isMobile]="isMobile"
  ></cgdis-portal-pmfsf-vol-number-column>

  <cgdis-portal-pmfsf-total-column
    cgdisDatatableColumn
    [isMobile]="isMobile"
  ></cgdis-portal-pmfsf-total-column>

  <cgdis-portal-pmfsf-pro-tec-number-column
    cgdisDatatableColumn
    *ngIf="parameter.status.primaryTypeTechnicalPro"
    [isMobile]="isMobile"
  ></cgdis-portal-pmfsf-pro-tec-number-column>

  <cgdis-portal-pmfsf-vol-tec-number-column
    cgdisDatatableColumn
    *ngIf="parameter.status.primaryTypeTechnicalVol"
    [isMobile]="isMobile"
  ></cgdis-portal-pmfsf-vol-tec-number-column>

</cgdis-portal-cgdisdatatable>
