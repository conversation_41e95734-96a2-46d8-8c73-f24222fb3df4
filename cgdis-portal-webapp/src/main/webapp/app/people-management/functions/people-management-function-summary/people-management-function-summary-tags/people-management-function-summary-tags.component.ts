import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { AssignmentStatus } from '@app/model/assignment-status.model';
import { FormControl } from '@angular/forms';
import { PeopleManagementFunctionSummaryFilters } from '@app/people-management/functions/people-management-function-summary/filters/people-management-function-summary-filters';
import { PeopleManagementFunctionSummaryTagsService } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-tags/people-management-function-summary-tags.service';
import { FunctionOperationalSummaryByTag } from '@app/model/functionoperational/function-operational-summary-by-tags.model';

export interface PeopleManagementFunctionSummaryTagsParameters {
  entityId: number;
  subentities: boolean;
  status: AssignmentStatus;
}

@Component({
  selector: 'cgdis-portal-people-management-function-tags-functions',
  templateUrl: './people-management-function-summary-tags.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [PeopleManagementFunctionSummaryTagsService],
})
export class PeopleManagementFunctionSummaryTagsComponent implements OnChanges {
  @Input() parameter: PeopleManagementFunctionSummaryTagsParameters;

  @Input() isMobile: boolean;

  @Input() currentFilters: PeopleManagementFunctionSummaryFilters;

  foTecidsFormControl = new FormControl<number[]>(undefined);
  interventionTypesTecidsFormControl = new FormControl<number[]>(undefined);

  constructor(
    public peopleManagementFunctionSummaryService: PeopleManagementFunctionSummaryTagsService,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes.parameter &&
      ((changes.parameter.currentValue?.entityId !==
        changes.parameter.previousValue?.entityId &&
        changes.parameter.currentValue?.entityId != undefined) ||
        changes.parameter.currentValue?.subentities !==
          changes.parameter.previousValue?.subentities)
    ) {
      this.startSearch();
    }

    if (changes.currentFilters) {
      this.filterUpdated();
    }
  }

  private startSearch() {
    this.peopleManagementFunctionSummaryService.setDynamicRestParams({
      entityTecid: this.parameter.entityId,
      subentities: this.parameter.subentities,
    });
  }

  filterUpdated() {
    if (
      this.currentFilters &&
      (this.currentFilters.foTecids ||
        this.currentFilters.interventionTypeTecids)
    ) {
      this.foTecidsFormControl.setValue(
        this.currentFilters.foTecids.length > 0
          ? this.currentFilters.foTecids
          : null,
      );
      this.interventionTypesTecidsFormControl.setValue(
        this.currentFilters.interventionTypeTecids.length > 0
          ? this.currentFilters.interventionTypeTecids
          : null,
      );
    }
  }

  cast(row: FunctionOperationalSummaryByTag) {
    return row as FunctionOperationalSummaryByTag;
  }
}
