import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges,
} from '@angular/core';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { FormControl } from '@angular/forms';
import { INgxSelectOption } from 'ngx-select-ex';
import { PeopleManagementFunctionSummaryParameter } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-parameter';
import { InterventionTypesService } from '@app/common/shared/services/intervention-types.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector:
    'cgdis-portal-people-management-function-summary-filter-intervention-type',
  templateUrl:
    './people-management-function-summary-filter-intervention-type.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeopleManagementFunctionSummaryFilterInterventionTypeComponent
  implements OnChanges
{
  @Input() parameter: PeopleManagementFunctionSummaryParameter;

  @Output() selectedValues = new EventEmitter<number[]>();

  possibleValues: FieldOption<number>[];
  filterFormControl = new FormControl<number[]>(undefined);

  constructor(
    private interventionTypesService: InterventionTypesService,
    private translateService: TranslateService,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.parameter) {
      const currentEntityTecid = changes.parameter?.currentValue?.entityId;
      const previousEntityTecid = changes.parameter?.previousValue?.entityId;

      if (currentEntityTecid !== previousEntityTecid) {
        this.loadOptions();
      }
    }
  }

  private loadOptions() {
    this.interventionTypesService
      .getCurrentForEntity(this.parameter.entityId, this.parameter.subentities)
      .subscribe({
        next: (response) => {
          this.possibleValues = response.map((interventionType) => {
            return new FieldOption({
              label: this.translateService.instant(
                `intervention.types.${interventionType.label}`,
              ),
              value: interventionType.tecid,
            });
          });
        },
      });
  }

  selectionChanged($event: INgxSelectOption[]) {
    this.selectedValues.emit($event.map((value) => value.data.value));
  }
}
