import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Output,
} from '@angular/core';

@Component({
  selector: 'cgdis-portal-people-management-function-table-filters-functions',
  templateUrl:
    './people-management-function-table-filters-functions.component.html',
  styleUrl:
    './people-management-function-table-filters-functions.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeopleManagementFunctionTableFiltersFunctionsComponent {
  @Output() selectionChanged = new EventEmitter<boolean>();
}
