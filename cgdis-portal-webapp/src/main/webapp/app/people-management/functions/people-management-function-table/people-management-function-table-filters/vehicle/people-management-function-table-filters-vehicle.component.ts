import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { VehicleService } from '@app/common/shared/services/vehicle.service';

@Component({
  selector: 'cgdis-portal-people-management-function-table-filters-vehicle',
  templateUrl:
    './people-management-function-table-filters-vehicle.component.html',
  styleUrl: './people-management-function-table-filters-vehicle.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeopleManagementFunctionTableFiltersVehicleComponent
  implements OnInit, OnChanges
{
  @Input() entityTecid: number;

  @Output() selectionChanged = new EventEmitter<string[]>();

  protected possibleValues: string[];

  constructor(
    private translateService: TranslateService,
    private vehicleService: VehicleService,
    private cd: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes.entityTecid &&
      changes.entityTecid.currentValue &&
      changes.entityTecid.currentValue !== changes.entityTecid.previousValue
    ) {
      this.possibleValues = [];
      this.vehicleService
        .getTypes(
          ['ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW'],
          [this.entityTecid],
        )
        .subscribe({
          next: (response) => {
            this.possibleValues = response;
            this.cd.markForCheck();
          },
        });
    }
  }

  mapItemToValue(value: string) {
    return value;
  }

  mapItemToLabel: (value: string) => string = (value: string) => {
    return this.translateService.instant(`i18n.data.vehicles.type.${value}`);
  };

  selectionChanges($event: string[]) {
    this.selectionChanged.emit($event);
  }
}
