import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  QueryList,
  SimpleChanges,
  ViewChildren,
} from '@angular/core';
import { Observable, Subscription } from 'rxjs';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { TranslateService } from '@ngx-translate/core';
import { FunctionOperational } from '@app/model/function-operational.model';
import { FORM_SERVICE } from '@app/common/modules/form-module/service/iform.service';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { ScrollTableService } from '@app/common/modules/scroll-table/scroll-table.service';
import { AssignmentWithFunctions } from '@app/model/assignment.model';
import { PeopleManagementFunctionTableResultFormService } from '@app/people-management/functions/people-management-function-table/people-management-function-table-result/people-management-function-table-result-form.service';
import { HighlightRowColService } from '@app/common/shared/directives/highlight-row-col/highlight-row-col.service';
import _ from 'lodash';
import { SimpleYesNoPopupData } from '@app/common/modules/popup/yes-no/simple-yes-no-popup-data';
import { SimpleYesNoPopupComponent } from '@app/common/modules/popup/yes-no/simple-yes-no-popup.component';
import { AssgnmentFunctionOperationalForm } from '@app/model/person-function-operational';

interface PeopleManagementFunctionTableResultCell {
  assignmentTecid: number;
  functionTecid: number;
  exist: boolean;
  trackingId: string;
}

@Component({
  selector: 'cgdis-portal-people-management-function-table-result',
  templateUrl: './people-management-function-table-result.component.html',
  styleUrl: './people-management-function-table-result.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    PeopleManagementFunctionTableResultFormService,
    ScrollTableService,
    {
      provide: FORM_SERVICE,
      useExisting: PeopleManagementFunctionTableResultFormService,
    },
  ],
})
export class PeopleManagementFunctionTableResultComponent
  implements AfterViewInit, OnDestroy, OnChanges, OnInit
{
  @Input() rows: AssignmentWithFunctions[];

  @Input() loading = false;

  /**
   * List of all service plan positions
   */
  @Input() functionOperationals: FunctionOperational[];

  @Input() refresh: boolean;

  @Input() canUpdate: boolean;

  @Input() entityId: number;

  /**
   * Call an update has been done on position
   * @type {EventEmitter<void>}
   */
  @Output() onFunctionOperationalUpdated =
    new EventEmitter<AssgnmentFunctionOperationalForm>();

  @Output() nameEmitter = new EventEmitter<string>();

  scrollableContentClasses = {};

  @ViewChildren('scheduleRow') components: QueryList<any>;
  public rowSizes: number[] = [];

  checkPossibleValue: FieldOption<boolean>[];

  subscriptions: Subscription[] = [];
  resetScroll = false;

  isMobile = false;

  private functionsByAssignment: PeopleManagementFunctionTableResultCell[][] =
    [];

  constructor(
    private popupService: SimplePopupService,
    private translateService: TranslateService,
    private changeDetectorRef: ChangeDetectorRef,
    public service: PeopleManagementFunctionTableResultFormService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
    private hightlightService: HighlightRowColService,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit() {
    this.checkPossibleValue = [
      new FieldOption<boolean>({
        label: '',
        value: true,
      }),
    ];
    this.changeDetectorRef.markForCheck();
  }

  ngOnChanges(_changes: SimpleChanges) {
    if (_changes.rows || _changes.functionOperationals) {
      this.resetScroll = true;
      setTimeout(() => {
        this.resetScroll = false;
        this.cd.markForCheck();
      });
      this.functionsByAssignment = [];
      if (this.rows != undefined && this.functionOperationals != undefined) {
        this.rows.forEach((row, rowIndex) => {
          const functionsByAssignmentRow: PeopleManagementFunctionTableResultCell[] =
            this.functionOperationals.map((f) => {
              if (row.functions != undefined) {
                let assignmentFunction = row.functions.find(
                  (assignmentFO) =>
                    assignmentFO.functionOperational.tecid === f.tecid,
                );
                const oneCell: PeopleManagementFunctionTableResultCell = {
                  assignmentTecid: row.tecid,
                  functionTecid: f.tecid,
                  exist: assignmentFunction?.tecid != undefined,
                  trackingId: `${rowIndex}_${row.tecid}_${f.tecid}_${assignmentFunction?.tecid}`,
                };
                return oneCell;
              }
            });

          this.functionsByAssignment.push(functionsByAssignmentRow);
        });
      }
    }
  }

  ngAfterViewInit() {
    this.subscriptions.push(
      this.components.changes.subscribe(() => {
        this.rowSizes = [];
        setTimeout(() => {
          this.components.forEach((component) =>
            this.rowSizes.push(component.nativeElement.clientHeight),
          );
          this.cd.markForCheck();
        }, 0);
      }),
    );
  }

  ngOnDestroy(): void {
    _.forEach(this.subscriptions, function (s: Subscription) {
      s.unsubscribe();
    });
  }

  hasDatas() {
    return this.rows != undefined && this.rows.length > 0;
  }

  public trackById(index: number, item: AssignmentWithFunctions): number {
    return item.person.tecid;
  }

  public trackByPosition(
    index: number,
    item: PeopleManagementFunctionTableResultCell,
  ) {
    return item.trackingId;
  }

  public changeFunctionOperationalForEntity(
    row: AssignmentWithFunctions,
    functionOperational: PeopleManagementFunctionTableResultCell,
    _event: any,
  ): void {
    if (functionOperational.exist) {
      let popupDialog = this.popupService.open(SimpleYesNoPopupComponent, {
        data: new SimpleYesNoPopupData({
          title: 'people_management.functions.deletion',
          message: 'people_management.functions.deletion_message',
          messageHtml: true,
          onYes: () => {
            return new Observable((subscriber) => {
              this.emitUpdateFunctionOperational(
                row.person.tecid,
                functionOperational.assignmentTecid,
                functionOperational.functionTecid,
                false,
              );
              subscriber.next();
            });
          },
          onNo: () => {
            return new Observable((subscriber) => {
              this.service.resetForm();
              subscriber.next();
            });
          },
        }),

        panelClass: this.isMobile ? 'simple-popup-mobile' : ['simple-popup'],
      });
      popupDialog.afterOpened().subscribe(() => {
        document.body.style.overflow = 'hidden';
      });

      popupDialog.afterClosed().subscribe(() => {
        document.body.style.overflow = null;
      });
    } else {
      this.emitUpdateFunctionOperational(
        row.person.tecid,
        functionOperational.assignmentTecid,
        functionOperational.functionTecid,
        true,
      );
    }
  }

  public emitUpdateFunctionOperational(
    personId: number,
    assignmentId: number,
    functionId: number,
    event: boolean,
  ): void {
    this.onFunctionOperationalUpdated.emit(
      new AssgnmentFunctionOperationalForm({
        personId,
        assignmentId: assignmentId,
        functionId: functionId,
        value: event,
      }),
    );
    this.changeDetectorRef.markForCheck();
  }

  filterByName(name: string) {
    this.nameEmitter.emit(name);
  }
}
