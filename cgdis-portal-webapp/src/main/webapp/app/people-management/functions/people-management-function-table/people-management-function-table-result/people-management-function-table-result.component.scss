$rowHeight: 5rem;
.people-management-function-table-result {
  .planner__people-list {
    height: $rowHeight;
    align-items: stretch;

    .functions_people {
      width: 12rem;
      min-height: unset !important;
      flex-basis: auto !important;
      display: flex;
      align-items: center;
      justify-content: center;
    }

  }

  .people-management-function-table-result__person {
    cgdis-portal-tabs-list-item {
      height: $rowHeight;
    }

    .people-management-function-table-result__person__info {
      //padding: 1rem;
      width: 100%;
      align-items: center;
      padding: 0 1rem;
      //height: 3rem !important;
      font-size: 1.3rem;

      .people-management-function-table-result__person__info__name {
        display: block;
      }

      .people-management-function-table-result__person__info__name,
      .people-management-function-table-result__person__info__cgdis {
        color: black;
        font-weight: bold;
      }
    }
  }
}

.service-planner__no-data {
  border-top: 0
}
