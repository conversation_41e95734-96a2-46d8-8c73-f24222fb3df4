import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  OnInit,
  Output,
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { PrimaryType } from '@app/model/primary-type.enum';

@Component({
  selector: 'cgdis-portal-people-management-function-table-filters-assignment',
  templateUrl:
    './people-management-function-table-filters-assignment.component.html',
  styleUrl:
    './people-management-function-table-filters-assignment.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeopleManagementFunctionTableFiltersAssignmentComponent
  implements OnInit
{
  @Output() selectionChanged = new EventEmitter<PrimaryType[]>();

  protected possibleValues: PrimaryType[];

  constructor(private translateService: TranslateService) {}

  ngOnInit(): void {
    this.possibleValues = [];

    for (let primaryTypeKey in PrimaryType) {
      if (Object.prototype.hasOwnProperty.call(PrimaryType, primaryTypeKey)) {
        this.possibleValues.push(primaryTypeKey as PrimaryType);
      }
    }

    // this.possibleValues = [PrimaryType.PRIMARY, PrimaryType.SECONDARY, PrimaryType.PRIMARY];
  }

  mapItemToValue(value: PrimaryType) {
    return value;
  }

  mapItemToLabel: (value: PrimaryType) => string = (value: PrimaryType) => {
    return this.translateService.instant(`assignments.primarytypes.${value}`);
  };

  selectionChanges($event: PrimaryType[]) {
    this.selectionChanged.emit($event);
  }
}
