import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  OnInit,
  Output,
} from '@angular/core';
import { AssignmentType } from '@app/model/assignment-type';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'cgdis-portal-people-management-function-table-filters-status',
  templateUrl:
    './people-management-function-table-filters-status.component.html',
  styleUrl: './people-management-function-table-filters-status.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeopleManagementFunctionTableFiltersStatusComponent
  implements OnInit
{
  @Output() selectionChanged = new EventEmitter<AssignmentType[]>();

  protected possibleValues: AssignmentType[];

  constructor(private translateService: TranslateService) {}

  ngOnInit(): void {
    this.possibleValues = [];
    for (let assignmentTypeKey in AssignmentType) {
      if (
        Object.prototype.hasOwnProperty.call(AssignmentType, assignmentTypeKey)
      ) {
        this.possibleValues.push(assignmentTypeKey as AssignmentType);
      }
    }
  }

  mapItemToValue(value: AssignmentType) {
    return value;
  }

  mapItemToLabel: (value: AssignmentType) => string = (
    value: AssignmentType,
  ) => {
    return this.translateService.instant(`assignments.types.${value}`);
  };

  selectionChanges($event: AssignmentType[]) {
    this.selectionChanged.emit($event);
  }
}
