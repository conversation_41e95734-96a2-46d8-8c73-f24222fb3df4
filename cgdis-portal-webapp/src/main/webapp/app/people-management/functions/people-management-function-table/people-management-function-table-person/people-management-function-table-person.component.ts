import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import { Subscription } from 'rxjs';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { ServicePlanDataService } from '@app/operational/service-plan/shared/date-service.service';
import { IconConstants } from '@app/common/shared/icon/icon-constants';
import { DeviceDetectorService } from 'ngx-device-detector';
import { AssignmentWithFunctions } from '@app/model/assignment.model';

@Component({
  selector: 'cgdis-portal-people-management-function-table-person',
  templateUrl: './people-management-function-table-person.component.html',
  styles: [],
  providers: [ServicePlanDataService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeopleManagementFunctionTablePersonComponent
  implements OnInit, OnChanges, OnD<PERSON>roy
{
  @Input() rows: AssignmentWithFunctions[];

  @Output() loading = new EventEmitter<boolean>();

  scrollableContentClasses: Record<string, boolean> = {};

  private rowSizes: number[];

  /**
   * Icon used for the time
   * @type {string}
   */
  public timeIcon = IconConstants.TIME;

  private rowSubscription: Subscription;

  constructor(
    private dataService: ServicePlanDataService,
    private popupService: SimplePopupService,
    private deviceDetectorService: DeviceDetectorService,
    private changeDetectorRef: ChangeDetectorRef,
  ) {}

  ngOnDestroy(): void {
    if (this.rowSubscription != undefined) {
      this.rowSubscription.unsubscribe();
    }
  }

  ngOnInit(): void {
    this.rowSubscription = this.dataService.currentRowSizesValue.subscribe(
      (rowSizes) => this.setRowSizes(rowSizes),
    );
  }

  ngOnChanges(changes: SimpleChanges) {
    // changes.prop contains the old and the new value...
    if (changes.rows) {
      this.rows = changes.rows.currentValue;
      this.scrollableContentClasses = {};
      this.scrollableContentClasses[
        'rows-min-scroll-height-row-' + this.rows.length
      ] = true;
      this.rowSizes = [];
    }
  }

  /**
   * Get row style depending on _rowSizes content
   * @param {number} rowIndex: the row index
   * @returns {string}: the style
   */
  public getRowStyle(rowIndex: number): Object {
    if (
      this.rowSizes != null &&
      this.rowSizes.length > 0 &&
      this.rowSizes[rowIndex] !== undefined
    ) {
      return {
        'max-height': 'none',
        height:
          (rowIndex === 0
            ? this.rowSizes[rowIndex] - 1
            : this.rowSizes[rowIndex]) + 'px',
      };
    }
    return {};
  }

  public setRowSizes(value: number[]): void {
    this.rowSizes = value;
    this.changeDetectorRef.markForCheck();
  }

  public trackById(index: number, item: AssignmentWithFunctions): number {
    return item.person.tecid;
  }

  public isTabletAndMobile(): boolean {
    return !this.deviceDetectorService.isDesktop();
  }
}
