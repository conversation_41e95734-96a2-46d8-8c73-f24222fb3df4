<cgdis-portal-form [formId]="'people-management-function'" [hideFormActions]="true"
                   class="people-management-function-table-result"
                   [hideSpinner]="true"
><!-- List of available positions  -->

<cgdis-portal-people-management-function-table-functions *hideItBootstrap="['sm','xs','md']" [loading]="loading"
                                                         [functionOperationals]="functionOperationals"
(nameEmitter)="filterByName($event)">
</cgdis-portal-people-management-function-table-functions>


  <div class="d-flex flex-row" [ngClass]="scrollableContentClasses">


    <div class="people-management-function-table-result__person">
      <cgdis-portal-tabs-list
        class="planner-timeslots ng-scrollbar__box-sizing__content " style="display: block;  width:16rem;">
        <cgdis-portal-tabs-list-item
          cgdisPortalHighlightRowCol highlightId="people-management-function-table-functions"
          [highlightRowIndex]="$rowIndex"
          *ngFor="let row of rows; index as $rowIndex"
          [tabClasses]="['service-planner__schedule-selector','p-0','schedule', '-complete']">
          <div class="people-management-function-table-result__person__info ">
                <span class="people-management-function-table-result__person__info__name">
                  {{ row.person.firstName }} {{ row.person.lastName }}
                </span>
            <span class="people-management-function-table-result__person__info__cgdis">
                  {{ row.person.cgdisRegistrationNumber }}
                </span>
            <span class="people-management-function-table-result__person__info__type">
                  {{ row.type }} {{ 'assignments.primarytypes.' + row.primaryType | translate }}
                </span>
          </div>

        </cgdis-portal-tabs-list-item>
      </cgdis-portal-tabs-list>
    </div>

    <div style="flex-grow:1;overflow: auto;">


      <cgdis-portal-scroll-table-bottom [goToScrollPosition]="loading"
                                        [resetScroll]="resetScroll">

        <div *ngIf="!rows || rows.length === 0 " class="service-planner__no-data"
             [translate]="'service_plan.no_data'"></div>

        <ng-container>
          <div style="display: flex; flex-grow:1; ">


            <div
              *ngIf="( rows != undefined && rows.length>=1 ) && (functionOperationals == undefined || functionOperationals.length === 0) ; else withFOTemplate"
              style="flex-grow:1 ;" class="d-flex justify-content-center align-items-center"
            >
              <div class="service-planner__no-data" [translate]="'people_management.functions.table.no_data'">
              </div>
            </div>
            <ng-template #withFOTemplate>
              <div style="flex-grow:1;  ">
                <div
                  *ngFor="let row of rows; trackBy: trackById; index as $index; "
                  id="rows-{{$index}}-content"
                  role="tabpanel"
                  [attr.aria-labelledby]="'rows-'+$index+'-tab'">


                  <ul class="planner__people-list" #scheduleRow>
                    <ng-container
                      *ngFor="let function of functionsByAssignment[$index]; trackBy: trackByPosition; index as currentIndex; last as $last;">
                      <ul *responsive="{bootstrap:['sm','xs','md']}" class="planner__people-list -placeholder"
                      >
                        <li class="functions_people" [ngClass]="['planner__people', 'functions_people' ,'-degraded']">

                      </ul>

                      <li class="planner__people functions_people" style=""
                          cgdisPortalHighlightRowCol highlightId="people-management-function-table-functions"
                          [highlightRowIndex]="$index" [highlightColIndex]="currentIndex"
                      >
                        <ng-container *ngIf="!loading">
                          <cgdis-portal-checkbox-field
                            [name]="function.trackingId"
                            [fieldReadonly]="!canUpdate"
                            [initialValue]="function.exist"
                            [possibleValues]="checkPossibleValue"
                            [slider]="false"
                            class="functions_checkbox -slide"
                            (onCheck)="changeFunctionOperationalForEntity(row, function, $event)"
                          ></cgdis-portal-checkbox-field>
                        </ng-container>


                      </li>

                    </ng-container>
                  </ul>
                </div>
              </div>
            </ng-template>
          </div>
        </ng-container>
      </cgdis-portal-scroll-table-bottom>
    </div>
  <!--  </ng-scrollbar>-->
</div>

</cgdis-portal-form>
<!-- Show header without positions -->
<ng-template #showPlaceholder></ng-template>
