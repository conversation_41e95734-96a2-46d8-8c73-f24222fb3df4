<div *ngIf="loading" >
  <cgdis-portal-spinner [loading]="loading"></cgdis-portal-spinner>
</div>

<cgdis-portal-people-management-function-table-filters
  (filtersChanged)="filtersChanged($event)"
  [entityTecid]="parameter?.entityId"
></cgdis-portal-people-management-function-table-filters>

<div *ngIf="parameter">
  <cgdis-portal-vertical-tabs [tabsClasses]="['service-planner__day-content']">

    <ng-container tabs-header>


      <!--<div class="tabs__tab service-planner__schedule-selectors schedule__selector hide-scroll" [ngClass]="{'pb-0':loading || !hasDatas()}">
      </div>-->

    </ng-container>


    <ng-container tabs-content>

        <!-- desktop -->
        <cgdis-portal-people-management-function-table-result
          *hideItBootstrap="['sm','xs']"
          class="tabs__content"
          [rows]="rows"
          [canUpdate]="canUpdate"
          [loading]="loading"
          [functionOperationals]="functionsOperationals"
          [entityId]="parameter.entityId"
          (onFunctionOperationalUpdated)="updateFunctionOperational($event)"
          (nameEmitter)="filterByName($event)"
        >
        </cgdis-portal-people-management-function-table-result>


    </ng-container>

  </cgdis-portal-vertical-tabs>


  <div>
    <cgdis-portal-pagination [page]="page" (pageChanged)="changePage($event)"></cgdis-portal-pagination>
  </div>



</div>

<!--<div *ngIf="!loading && !hasDatas()" class="service-planner__no-data" [translate]="'service_plan.no_data'"></div>-->
<!--<div *ngIf="loading && !hasDatas()" class="service-planner__no-data" style="position: relative"></div>-->


