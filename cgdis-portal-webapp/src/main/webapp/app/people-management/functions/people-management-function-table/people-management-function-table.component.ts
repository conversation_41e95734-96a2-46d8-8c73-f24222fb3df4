import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChang<PERSON>,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { BehaviorSubject, Subject, Subscription } from 'rxjs';
import { AssgnmentFunctionOperationalForm } from '@app/model/person-function-operational';
import { PeopleManagementFunctionTableService } from '@app/people-management/functions/people-management-function-table/people-management-function-table.service';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { IPage } from '@eportal/core';
import { FormControl, UntypedFormControl } from '@angular/forms';
import { takeUntil } from 'rxjs/operators';
import { PeopleManagementFunctionTableParameter } from '@app/people-management/functions/people-management-function-table/people-management-function-table-parameter';
import { PeopleManagementFunctionsService } from '@app/people-management/functions/people-management-functions.service';
import { AssignmentWithFunctions } from '@app/model/assignment.model';
import { SearchOperator } from '@eportal/components';
import { PrimaryType } from '@app/model/primary-type.enum';
import { FunctionOperational } from '@app/model/function-operational.model';
import { AssignmentType } from '@app/model/assignment-type';
import { PeopleManagementFunctionTableFilters } from '@app/people-management/functions/people-management-function-table/people-management-function-table-filters/people-management-function-table.filters.interface';

@Component({
  selector: 'cgdis-portal-people-management-function-table',
  templateUrl: './people-management-function-table.component.html',
  styles: [],
  providers: [PeopleManagementFunctionTableService],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeopleManagementFunctionTableComponent
  implements OnInit, OnChanges, OnDestroy
{
  @Input() parameter: PeopleManagementFunctionTableParameter;

  public loading = true;
  rows: AssignmentWithFunctions[] = [];
  page: IPage<AssignmentWithFunctions>;
  subscriptions: Subscription[] = [];
  canUpdate = false;

  isMobile: boolean = false;

  protected functionsOperationals: FunctionOperational[];

  private nameControl = new UntypedFormControl();
  private assignmentTypeControl = new FormControl<AssignmentType[]>(undefined);
  private assignmentPrimaryTypeControl = new FormControl<PrimaryType[]>(
    undefined,
  );

  // Vehicle type is used only to filter functions and not person assignments
  private vehicleTypeControl = new FormControl<string[]>(undefined);
  private withoutFunctionControl = new FormControl<boolean>(undefined);
  private subentitiesControl = new UntypedFormControl();

  private _unsubscribe$ = new Subject<void>();

  private pageSizeSubject: Subject<number> = new BehaviorSubject<number>(3);

  constructor(
    private cdRef: ChangeDetectorRef,
    private userService: ConnectedUserService,
    private breakpointObserver: BreakpointObserver,
    public personFunctionOperationalTableService: PeopleManagementFunctionTableService,
    private peopleManagementFunctionService: PeopleManagementFunctionsService,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe([
          '(max-width: 576px)',
          '(min-height: 992px)',
          '(min-height: 1200px)',
        ])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.breakpoints['(max-width: 576px)'];
          if (result.breakpoints['(min-height: 1200px)']) {
            this.pageSizeSubject.next(10);
          } else if (result.breakpoints['(min-height: 992px)']) {
            this.pageSizeSubject.next(5);
          } else {
            this.pageSizeSubject.next(3);
          }
        }),
    );
  }

  ngOnInit() {
    this.personFunctionOperationalTableService.currentPage = 0;
    this.pageSizeSubject
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((size) => {
        if (size !== this.personFunctionOperationalTableService.pageSize) {
          this.personFunctionOperationalTableService.pageSize = size;
          this.personFunctionOperationalTableService.currentPage = 0;
          this.personFunctionOperationalTableService.search();
          this.cdRef.markForCheck();
        }
      });
    this.personFunctionOperationalTableService.addFilterWithFormControl(
      'assignmentTypes',
      this.assignmentTypeControl,
      { operator: SearchOperator.in },
    );
    this.personFunctionOperationalTableService.addFilterWithFormControl(
      'assignmentPrimaryTypes',
      this.assignmentPrimaryTypeControl,
      { operator: SearchOperator.in },
    );

    this.personFunctionOperationalTableService.addFilterWithFormControl(
      'withoutFunction',
      this.withoutFunctionControl,
      { operator: SearchOperator.eq, defaultValue: undefined },
    );
    this.personFunctionOperationalTableService.addFilterWithFormControl(
      'subentities',
      this.subentitiesControl,
      {
        operator: SearchOperator.eq,
        defaultValue: false,
      },
    );
    this.personFunctionOperationalTableService.addFilterWithFormControl(
      'name',
      this.nameControl,
    );
    this.canUpdate = this.userService.hasAnyRoles([
      'ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_MAPPING_UPDATE',
    ]);
    this.personFunctionOperationalTableService
      .isLoading()
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((loading: boolean) => {
        this.setLoading(loading);
        this.cdRef.markForCheck();
      });
    this.cdRef.markForCheck();
    this.subscriptions.push(
      this.personFunctionOperationalTableService
        .getResults()
        .subscribe((result: IPage<AssignmentWithFunctions>) => {
          this.page = result;
          this.rows = result.content;
          this.cdRef.markForCheck();
          this.stopLoading();
        }),
      this.vehicleTypeControl.valueChanges.subscribe((value) => {
        this.loadFunctionsForEntity();
      }),
    );
  }

  loadDatas(): void {
    // this.loading = true;
    if (this.parameter) {
      setTimeout(() => {
        this.personFunctionOperationalTableService.loadDatas(
          this.parameter.entityId,
        );
      });
    }
  }

  stopLoading() {
    setTimeout(() => {
      this.loading = false;
      this.cdRef.markForCheck();
    });
  }

  ngOnDestroy(): void {
    // Unsubscribe to all subscriptions
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
    for (let subscription of this.subscriptions) {
      subscription.unsubscribe();
    }
  }

  hasDatas() {
    return this.rows != undefined && this.rows.length > 0;
  }

  public setLoading(loading: boolean): void {
    if (loading === false) {
      this.stopLoading();
    } else {
      this.loading = loading;
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.parameter) {
      this.loadDatas();
      this.loadFunctionsForEntity();
    }
  }

  updateFunctionOperational(
    personFunctionOperation: AssgnmentFunctionOperationalForm,
  ) {
    this.peopleManagementFunctionService
      .mapByEntity(personFunctionOperation)
      .subscribe((result: boolean) => {
        this.loadDatas();
      });
  }

  filterByName(name: string) {
    if (this.nameControl.value != name) {
      this.nameControl.setValue(name == '' ? undefined : name, {
        emitEvent: false,
      });
      this.loadDatas();
    }
  }

  changePage(newPage: IPage<AssignmentWithFunctions>) {
    this.personFunctionOperationalTableService.updatePage(newPage);
  }

  filtersChanged($event: PeopleManagementFunctionTableFilters) {
    this.assignmentTypeControl.setValue(
      $event.status.length > 0 ? $event.status : undefined,
    );
    this.assignmentPrimaryTypeControl.setValue(
      $event.assignmentTypes.length > 0 ? $event.assignmentTypes : undefined,
    );
    this.vehicleTypeControl.setValue(
      $event.vehicleTypes.length > 0 ? $event.vehicleTypes : undefined,
    );
    this.withoutFunctionControl.setValue(
      $event.withoutFunction === true ? true : null,
    );
  }

  private loadFunctionsForEntity() {
    if (
      this.parameter == undefined ||
      this.parameter.entityId == undefined ||
      this.parameter.interventionTypeId == undefined
    ) {
      return;
    }
    this.peopleManagementFunctionService
      .getAllByEntityIdAndInterventionType(
        this.parameter.entityId,
        false,
        this.parameter.interventionTypeId,
        this.vehicleTypeControl.value,
      )
      .subscribe({
        next: (response) => {
          this.functionsOperationals = response;
        },
      });
  }
}
