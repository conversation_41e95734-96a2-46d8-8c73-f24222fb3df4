import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { PeopleManagementFunctionTableFilters } from '@app/people-management/functions/people-management-function-table/people-management-function-table-filters/people-management-function-table.filters.interface';
import { AssignmentType } from '@app/model/assignment-type';
import { PrimaryType } from '@app/model/primary-type.enum';

@Component({
  selector: 'cgdis-portal-people-management-function-table-filters',
  templateUrl: './people-management-function-table-filters.component.html',
  styleUrl: './people-management-function-table-filters.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeopleManagementFunctionTableFiltersComponent implements OnInit {
  @Input() entityTecid: number;
  @Output() filtersChanged =
    new EventEmitter<PeopleManagementFunctionTableFilters>();

  private currentFilter: PeopleManagementFunctionTableFilters = {
    assignmentTypes: [],
    withoutFunction: false,
    status: [],
    vehicleTypes: [],
  };

  ngOnInit(): void {}

  functionsChanged($event: boolean) {
    this.currentFilter = {
      ...this.currentFilter,
      withoutFunction: $event,
    };
    this.filtersChanged.next(this.currentFilter);
  }

  statusChanged($event: AssignmentType[]) {
    this.currentFilter = {
      ...this.currentFilter,
      status: $event || [],
    };
    this.filtersChanged.next(this.currentFilter);
  }

  assignmentChanged($event: PrimaryType[]) {
    this.currentFilter = {
      ...this.currentFilter,
      assignmentTypes: $event || [],
    };
    this.filtersChanged.next(this.currentFilter);
  }

  vehicleChanged($event: string[]) {
    this.currentFilter = {
      ...this.currentFilter,
      vehicleTypes: $event || [],
    };
    this.filtersChanged.next(this.currentFilter);
  }
}
