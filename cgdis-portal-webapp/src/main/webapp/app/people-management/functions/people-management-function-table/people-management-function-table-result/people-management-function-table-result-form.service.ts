import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { DefaultFormService } from '@app/common/modules/form-module/service/default-form.service';
import { PositionTemplateVersion } from '@app/model/position-template-version.model';
import { ToastService } from '@app/common/shared/toasts/CGDISToastService';
import { FormErrorService } from '@app/common/modules/form-module/service/form-error.service';
import { ActivatedRoute, Router } from '@angular/router';
import { FormError } from '@app/common/modules/error-management/model/form-error.model';
import { AssgnmentFunctionOperationalForm } from '@app/model/person-function-operational';
import { AssignmentWithFunctions } from '@app/model/assignment.model';

@Injectable()
export class PeopleManagementFunctionTableResultFormService extends DefaultFormService<
  AssgnmentFunctionOperationalForm,
  AssignmentWithFunctions
> {

  private basePersonFunctionUrl: string[] = ['person-function-operational'];

  /**
   * Based url to access one entity by id
   * @type {string}
   */

  constructor(
    toastService: ToastService,
    private restService: RestService,
    formErrorService: FormErrorService,
  ) {
    super(
      toastService,
      'admin.service_plan_model.version.edit.success',
      formErrorService,
    );
  }

  submit(
    form: AssgnmentFunctionOperationalForm,
  ): Observable<AssignmentWithFunctions> {
    return null;
  }

  submitSuccess(result: PositionTemplateVersion): void {}

  submitError(formError: FormError): void {}


}
