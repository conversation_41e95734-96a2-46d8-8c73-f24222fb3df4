<div class="planner__header-jobs">
  <div class="filter people-management-function-table-functions__filter">
    <label [translate]="'people_management.functions.table.filterName'"></label>
    <div class="field">
      <input type="text"
             class="datatable-input"
             cgdisPortalEllipsis
             [ngModel]="nameSearch"
             (ngModelChange)="inputCompleted($event)"
             [oneLine]="true" />
      <span>
          <button *ngIf="nameSearch != null && nameSearch.length > 0" class="close-icon" (click)="clear()">
            <i class="fa fa-times"></i>
          </button>
          <button class="search-icon">
            <i class="fa fa-search"></i>
          </button>
        </span>
    </div>
  </div>
  <div class="people-management-function-table-functions__list">
    <cgdis-portal-scroll-table-top [hideScrollbars]="true">
      <ul class="planner__people-list -placeholder">
        <li *ngFor="let function of functionOperationals; trackBy: trackById; index as $colIndex"
            cgdisPortalHighlightRowCol highlightId="people-management-function-table-functions"
            [highlightColIndex]="$colIndex"
            [ngClass]="[ 'planner__people', 'functions_people','-degraded' ]">
          <div class="functions_people-label">{{ function.portalLabel }}</div>
        </li>
      </ul>
    </cgdis-portal-scroll-table-top>
  </div>
</div>

