import { Subject, Subscription } from 'rxjs';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import _ from 'lodash';
import { InterventionTypesService } from '@app/common/shared/services/intervention-types.service';
import { InterventionType } from '@app/model/intervention-type.model';
import { PeopleManagementFunctionsService } from '@app/people-management/functions/people-management-functions.service';
import { FilterConfig, SearchOperator } from '@eportal/components';
import { Entity } from '@app/model/entity.model';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { PeopleManagementFunctionSummaryParameter } from '@app/people-management/functions/people-management-function-summary/people-management-function-summary-parameter';
import { LandscapeBlockerModes } from '@app/model/landscape-blocker-modes.model';

@Component({
  selector: 'cgdis-portal-admin-people-management-functions',
  templateUrl: './people-management-functions.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeopleManagementFunctionsComponent implements OnInit, OnDestroy {
  selectedEntity: Entity;

  interventionTypes: InterventionType[];

  summaryTableParameter: PeopleManagementFunctionSummaryParameter;

  subscriptions: Subscription[] = [];

  loading = true;
  subentities = false;

  filterConfigAllPersons = new FilterConfig({
    inUrl: false,
    operator: SearchOperator.eq,
  });

  isMobile = false;

  protected readonly landscapeBlockerModes = LandscapeBlockerModes;

  private _unsubscribe$: Subject<void> = new Subject<void>();

  constructor(
    private interventionTypesService: InterventionTypesService,
    private peopleManagementFunctionService: PeopleManagementFunctionsService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit() {}

  public selectEntity(selectedEntity: {
    entity: Entity;
    allUnderEntity: boolean;
  }): void {
    this.loading = true;
    this.selectedEntity = selectedEntity.entity;
    this.subentities = selectedEntity.allUnderEntity;
    this.interventionTypesService
      .getCurrentForEntity(selectedEntity.entity.tecid, this.subentities)
      .subscribe((interventionTypes) => {
        this.interventionTypes = interventionTypes;
        this.loading = false;
        this.initFunctionOperationalsData();
        this.cd.markForCheck();
      });
    this.cd.markForCheck();
  }

  initFunctionOperationalsData(): void {
    this.summaryTableParameter = {
      entityId: this.selectedEntity.tecid,
      subentities: this.subentities,
    };
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
    _.each(this.subscriptions, (oneSubscription) =>
      oneSubscription.unsubscribe(),
    );
  }

  exportExcel() {
    if (!this.loading) {
      this.loading = true;
      this.cd.markForCheck();
      this.peopleManagementFunctionService.export(
        this.selectedEntity.tecid,
        this.subentities,
        (e) => {},
        () => {
          this.loading = false;
          this.cd.markForCheck();
        },
      );
    }
  }
}
