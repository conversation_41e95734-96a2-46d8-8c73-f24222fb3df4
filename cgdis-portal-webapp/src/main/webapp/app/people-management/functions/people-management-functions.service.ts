import { first, map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { DownloadOptions, ProgressDownload, RestService } from '@eportal/core';
import { Observable } from 'rxjs';
import { Entity } from '@app/model/entity.model';
import { FunctionOperational } from '@app/model/function-operational.model';
import { AssgnmentFunctionOperationalForm } from '@app/model/person-function-operational';

@Injectable()
export class PeopleManagementFunctionsService {
  /**
   * Based url to access all entities
   * @type {string}
   */
  private baseUrl = ['function-operationals'];

  private basePersonFunctionUrl: string[] = ['person-function-operational'];

  /**
   * Based url to access one entity by id
   * @type {string}
   */

  constructor(private restService: RestService) {}

  /**
   * Get all entities
   * @return {Observable<SearchResult<Entity>>}
   */
  getAllByEntityId(
    entityId: number,
    subentities: boolean,
  ): Observable<FunctionOperational[]> {
    const restResource = this.restService.all(
      ...this.baseUrl,
      String(entityId),
    );
    return restResource.get({ subentities: subentities }).pipe(
      map((value) => {
        return value as FunctionOperational[];
      }),
    );
  }

  getAllByEntityIdAndInterventionType(
    entityId: number,
    subentities: boolean,
    interventionTypeTecid: number,
    vehicleTypes: string[] | null,
  ): Observable<FunctionOperational[]> {
    const restResource = this.restService.all(
      ...this.baseUrl,
      String(entityId),
      'interventiontypes',
      String(interventionTypeTecid),
    );

    let params: { subentities: boolean; vt?: string[] } = {
      subentities: subentities,
    };
    if (vehicleTypes != undefined && vehicleTypes.length > 0) {
      params.vt = vehicleTypes;
    }
    return restResource.get(params).pipe(
      map((value) => {
        return value as FunctionOperational[];
      }),
    );
  }

  mapByEntity(
    personFunctionOperational: AssgnmentFunctionOperationalForm,
  ): Observable<boolean> {
    const restResource = this.restService.all(
      ...this.basePersonFunctionUrl,
      'persons',
      personFunctionOperational.personId.toString(),
      'mapByEntity',
      String(personFunctionOperational.functionId),
      String(personFunctionOperational.assignmentId),
    );
    return restResource.put(personFunctionOperational).pipe(
      map((value) => {
        return value;
      }),
    );
  }

  export(
    entityTecid: number,
    subentities: boolean,
    errorCallback: (e: any) => void,
    completeCallback: () => void,
  ): void {
    const baseUrl = ['function-operationals', String(entityTecid), 'exports'];
    if (subentities) {
      baseUrl.push('subentities');
    }
    this.restService
      .one(...baseUrl)
      .download(new DownloadOptions({}))
      .pipe(first())
      .subscribe(
        (progressDownload: ProgressDownload) => {
          // Nothing to do !
        },
        (e) => errorCallback(e),
        () => completeCallback(),
      );
  }
}
