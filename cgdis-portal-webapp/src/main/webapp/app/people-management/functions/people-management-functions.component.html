<ng-container class="people-management">
  <cgdis-portal-page-template>

    <cgdis-portal-page-header [titleKey]="'layout.navigation.menu.items.people_management.function'" [subtitleAlign]="true">
      <div page-header-subtitle>
        <cgdis-portal-entity-filter [entityPermission]="['ROLE_PERMISSION_USER_MANAGEMENT_OP_FUNCT_VIEW']"
                                    [underDisable]="false" [initPrimaryEntity]="true"
                                    (filterSelected)="selectEntity($event)"></cgdis-portal-entity-filter>

      </div>

      <div page-header-icon class="float-right">

        <cgdis-portal-link-with-icon-download *ngIf=" !isMobile"
                                              [linkDisabled]="loading"
                                              id="export-excel-button" (click)="exportExcel()" [rounded]="true"
                                              [tooltipText]="'tooltip.export.excel' | translate"></cgdis-portal-link-with-icon-download>
      </div>

      <!--      <cgdis-portal-expansion-panel-->
      <!--        [expanded]="true"-->
      <!--        [titleKey]="'people_management.functions.summary'"-->
      <!--      >-->
      <!--        <cgdis-portal-people-management-function-summary-->
      <!--          [parameter]="summaryTableParameter"-->
      <!--        ></cgdis-portal-people-management-function-summary>-->
      <!--      </cgdis-portal-expansion-panel>-->

    </cgdis-portal-page-header>

    <cgdis-portal-panel>
      <div class="localSpinner" cgdisPortalLandscapeBlocker
           [landscapeBlockerMode]="landscapeBlockerModes.BLOCKONTABLETANDMOBILE">
        <!-- Persons -->
        <ng-container *ngIf="selectedEntity ">
          <cgdis-portal-spinner [loading]="loading" ></cgdis-portal-spinner>
          <mat-accordion [ngClass]="'borderBoxExpansionPanel'">
            <cgdis-portal-expansion-panel
              [expanded]="true"
              [titleKey]="'people_management.functions.summary'"
              collapsedHeight="48px" expandedHeight="48px"
            >
              <ng-template cgdisPortalExpansionPanelContent>
                <cgdis-portal-people-management-function-summary
                  [parameter]="summaryTableParameter"
                ></cgdis-portal-people-management-function-summary>
              </ng-template>
            </cgdis-portal-expansion-panel>


            <ng-container *ngIf="!subentities">
              <cgdis-portal-people-management-function-by-type
                [entity]="selectedEntity"
                [subentities]="subentities"
              ></cgdis-portal-people-management-function-by-type>



            </ng-container>
          </mat-accordion>


        </ng-container>
      </div>
    </cgdis-portal-panel>

  </cgdis-portal-page-template>
</ng-container>



