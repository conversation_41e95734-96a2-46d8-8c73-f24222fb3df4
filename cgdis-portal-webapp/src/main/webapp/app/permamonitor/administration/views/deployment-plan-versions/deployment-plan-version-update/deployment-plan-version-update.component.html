<cgdis-portal-page-template>
  <!-- Header -->
  <cgdis-portal-page-header [subtitleAlign]="true" [titleKey]="'permamonitor.title-deployment-plan-update'">

  </cgdis-portal-page-header>
  @if (currentDeploymentPlan) {
    <cgdis-portal-default-form-template>
      <cgdis-portal-form [customButtonsRow]="false" [formReadonly]="!formValidation.editable || !canUpdate"
                         (onCancel)="cancel()"
                         [formClasses]="['row', 'create-deployment-plan-form']"

                         [formId]="'permamonitor-deployment-plan-update-id'"
                         [hideCancelAction]="false"
                         [longLoadingNoteTranslationKey]="'permamonitor.admin.pa.update-form.loading'"

      >
        <cgdis-portal-input-field [fieldRequired]="true"
                                  [initialValue]="currentDeploymentPlan.tecid"
                                  [name]="'tecid'"
                                  [hidden]="true"
        ></cgdis-portal-input-field>
        <cgdis-portal-input-field [fieldRequired]="true"
                                  [initialValue]="currentDeploymentPlan.teclock"
                                  [name]="'teclock'"
                                  [hidden]="true"
        ></cgdis-portal-input-field>

        <div class="row">
          <cgdis-portal-input-field [fieldRequired]="true"
                                    [initialValue]="currentDeploymentPlan.name"
                                    [labelKey]="'permamonitor.admin.pa.update-form.name'"
                                    [name]="'name'"
                                    [fieldMaxLength]="20"
                                    [fieldReadonly]="!formValidation.name"
                                    class="col-6"
          ></cgdis-portal-input-field>

          <cgdis-portal-datepicker-field
            [fieldRequired]="true"
            [labelKey]="'permamonitor.admin.pa.update-form.start-date'"
            [name]="'startDate'" class="col-6 "
            [initialValue]="currentDeploymentPlan.startDate"
            [fieldReadonly]="!formValidation.startDate"


          ></cgdis-portal-datepicker-field>
        </div>
        <div class="row">
          <cgdis-portal-input-field [labelKey]="'permamonitor.admin.pa.update-form.description'"
                                    [name]="'description'"
                                    [initialValue]="currentDeploymentPlan.description"
                                    [fieldReadonly]="!formValidation.description"
                                    [fieldMaxLength]="150"
                                    class="col-12 "

          ></cgdis-portal-input-field>
        </div>

      </cgdis-portal-form>


    </cgdis-portal-default-form-template>
  }
</cgdis-portal-page-template>
