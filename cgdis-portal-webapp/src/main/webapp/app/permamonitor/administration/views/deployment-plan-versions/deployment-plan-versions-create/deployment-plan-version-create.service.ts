import { Injectable } from '@angular/core';
import { PermDeploymentPlanPostModel } from '@app/model/permamonitor/deployment-plan/perm-deployment-plan-post.model';
import { DefaultFormService } from '@app/common/modules/form-module/service/default-form.service';
import { FormError } from '@app/common/modules/error-management/model/form-error.model';
import { Observable } from 'rxjs';
import { DeploymentPlanModel } from '@app/model/permamonitor/service-plan-category/deployment-plan.model';
import { FormErrorService } from '@app/common/modules/form-module/service/form-error.service';
import { ToastService } from '@app/common/shared/toasts/CGDISToastService';
import { DateModel, RestService } from '@eportal/core';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute, Router } from '@angular/router';

@Injectable()
export class DeploymentPlanVersionCreateService extends DefaultFormService<
  PermDeploymentPlanPostModel,
  DeploymentPlanModel
> {
  constructor(
    formErrorService: FormErrorService,
    toastr: ToastService,
    private restService: RestService,
    protected translationService: TranslateService,
    private router: Router,
    private route: ActivatedRoute,
  ) {
    super(
      toastr,
      translationService.instant('permamonitor.admin.pa.create-form.toast'),
      formErrorService,
    );
  }

  submit(parameter: any): Observable<any> {
    return this.createDeploymentPlanVersion(
      parameter.name,
      parameter.description,
      parameter.startDate,
    );
  }

  submitSuccess(result: any): void {
    this.router.navigate(['../'], { relativeTo: this.route });
  }

  submitError(formError: FormError): void {}

  createDeploymentPlanVersion(
    name: string,
    description: string,
    startDate: DateModel,
  ) {
    return this.restService
      .one('permamonitor', 'admin', 'deployment-plan', 'create')
      .post(
        new PermDeploymentPlanPostModel({
          name: name,
          description: description,
          startDate: startDate,
        }),
      );
  }
}
