import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { ServicePlanConfigTableService } from '@permamonitor/administration/views/service-plan-configuration/service-plan-config-table/service-plan-config-table.service';
import { FormControl, FormsModule, UntypedFormControl } from '@angular/forms';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import {
  EpDatatableModule,
  FilterConfig,
  SearchOperator,
} from '@eportal/components';
import { SharedModule } from '@app/common/shared/shared.module';
import { NgxSelectModule } from 'ngx-select-ex';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { IPage } from '@eportal/core';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import _ from 'lodash';
import { Category } from '@permamonitor/permamonitor/common/constants';
import { ToastService } from '@app/common/shared/toasts/CGDISToastService';
import { TranslateService } from '@ngx-translate/core';
import { BreakpointObserver } from '@angular/cdk/layout';
import { PermCategoryWithSubcategoriesModel } from '@app/model/permamonitor/perm-category-with-subcategories.model';
import { PermSubcategoryModel } from '@app/model/permamonitor/perm-subcategory.model';
import { PermServicePlanCategoryWithSubcategoryModel } from '@app/model/permamonitor/service-plan-category/perm-service-plan-category-with-subcategroy.model';
import { EntityFilterModule } from '@app/common/modules/entity-filter/entity-filter.module';
import { Entity } from '@app/model/entity.model';

@Component({
  selector: 'cgdis-portal-pds-config-table',
  standalone: true,
  imports: [
    DatatableModule,
    EpDatatableModule,
    SharedModule,
    NgxSelectModule,
    FormsModule,
    EntityFilterModule,
  ],
  templateUrl: './service-plan-config-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ServicePlanConfigTableComponent
  implements OnChanges, OnInit, OnDestroy
{
  @Input() deploymentPlanTecid: number = undefined;
  bookmarkedFilterValues: FieldGroupOption<string, any>[];
  protected entityFormControl = new FormControl<number>(undefined);
  protected categories: PermCategoryWithSubcategoriesModel[];
  protected subcategoriesByCategory: Map<number, PermSubcategoryModel[]>;
  protected allSubcategories: FieldGroupOption<number, any>[];
  protected entityTypesFilterValues: FieldGroupOption<Category, any>[];
  protected currentTableRows: IPage<PermServicePlanCategoryWithSubcategoryModel>;
  protected canUpdate = false;
  protected servicePlanWithoutCategoryFilter: FilterConfig = new FilterConfig({
    defaultValue: 'true',
  });
  protected isDeploymentPlanEditable: boolean = false;
  protected dynamicPageSize: number;
  protected windowHeight: number = window.innerHeight;
  private deploymentPlanTecidFormControl = new UntypedFormControl();
  private _unsubscribe$ = new Subject<void>();

  constructor(
    // Service defined on parent component
    protected pdsConfigTableService: ServicePlanConfigTableService,
    private cd: ChangeDetectorRef,
    private breakpointObserver: BreakpointObserver,
    private connectedUserService: ConnectedUserService,
    private toastService: ToastService,
    private translateService: TranslateService,
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.deploymentPlanTecid && this.deploymentPlanTecid) {
      this.deploymentPlanTecidFormControl.setValue(this.deploymentPlanTecid);
      this.pdsConfigTableService
        .isDeploymentPlanEditable(this.deploymentPlanTecid)
        .subscribe((isEditableResult) => {
          this.isDeploymentPlanEditable = isEditableResult;
        });
    }
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }

  ngOnInit() {
    this.windowHeight = window.innerHeight;
    this.dynamicPageSize = Math.floor(window.innerHeight / 110);

    this.initializeCustomFilters();
    this.connectedUserService
      .hasAnyRolesObservable([
        'ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_DEPLOYMENT_PLAN_CONFIG',
      ])
      .subscribe((value) => {
        this.canUpdate = value;
        this.cd.markForCheck();
      });
    this.getAllCategories();

    this.pdsConfigTableService
      .getResults()
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((value) => {
        this.currentTableRows = value;
      });
    this.loadBookmarkedValues();
  }

  initializeCustomFilters() {
    this.pdsConfigTableService.addFilterWithFormControl(
      'permConfigDpceEntityTecid',
      this.entityFormControl,
      {
        operator: SearchOperator.eq,
      },
    );
  }

  toggleFavoritesCheckbox(
    index: number,
    currentRow: PermServicePlanCategoryWithSubcategoryModel,
    currentValue: boolean,
  ) {
    if (currentRow.tecid == undefined) {
      this.toastService.error(
        this.translateService.instant(
          'permamonitor.admin.pds.table.update-failure-missing-category',
        ),
      );
      return;
    }
    this.pdsConfigTableService.startLoading();
    this.pdsConfigTableService
      .updateIsBookmarked(
        currentRow.permDeploymentPlanTecid,
        currentRow.tecid,
        currentRow.teclock,
        !currentValue,
      )
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe({
        error: (error) => this.pdsConfigTableService.stopLoading(),
        next: (value) => {
          this.currentTableRows.content[index].bookmarked = value.bookmarked;
          this.pdsConfigTableService.setResults(this.currentTableRows);
          this.pdsConfigTableService.stopLoading();

          this.toastService.success(
            this.translateService.instant(
              value.bookmarked
                ? 'permamonitor.admin.pds.table.toasts.added-favorite'
                : 'permamonitor.admin.pds.table.toasts.removed-favorite',
            ),
          );
          this.cd.markForCheck();
        },
      });
  }

  selectSubcategory(
    index: number,
    currentRow: PermServicePlanCategoryWithSubcategoryModel,
    newSubcategoryTecid: number,
  ) {
    if (currentRow.tecid == undefined) {
      this.toastService.error(
        this.translateService.instant(
          'permamonitor.admin.pds.table.update-failure-missing-category',
        ),
      );
      return;
    }
    this.pdsConfigTableService.startLoading();
    this.pdsConfigTableService
      .updateSubcategory(
        currentRow.permDeploymentPlanTecid,
        currentRow.tecid,
        currentRow.teclock,
        newSubcategoryTecid,
      )
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe({
        error: (error) => this.pdsConfigTableService.stopLoading(),
        next: (value) => {
          this.currentTableRows.content[index].subcategoryTecid =
            value.subcategoryTecid;
          this.pdsConfigTableService.setResults(this.currentTableRows);
          this.pdsConfigTableService.stopLoading();

          this.toastService.success(
            this.translateService.instant(
              'permamonitor.admin.pds.table.toasts.updated-subcategory',
            ),
          );
          this.cd.markForCheck();
        },
      });
  }

  deleteCategory(
    index: number,
    currentRow: PermServicePlanCategoryWithSubcategoryModel,
  ) {
    this.pdsConfigTableService.startLoading();
    this.pdsConfigTableService
      .deleteCategory(
        currentRow.permDeploymentPlanTecid,
        currentRow.tecid,
        currentRow.teclock,
        currentRow.permCategoryTecid,
      )
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe({
        error: (error) => this.pdsConfigTableService.stopLoading(),
        next: (value) => {
          this.currentTableRows.content[index].tecid = undefined;
          this.currentTableRows.content[index].permCategory = undefined;
          this.currentTableRows.content[index].permCategoryTecid = undefined;
          this.currentTableRows.content[index].bookmarked = undefined;
          this.pdsConfigTableService.setResults(this.currentTableRows);
          this.pdsConfigTableService.stopLoading();

          this.toastService.success(
            this.translateService.instant(
              'permamonitor.admin.pds.table.toasts.deleted-category',
            ),
          );
          this.cd.markForCheck();
        },
      });
  }

  deleteSubcategory(
    index: number,
    currentRow: PermServicePlanCategoryWithSubcategoryModel,
    subcategoryTecid: number,
  ) {
    if (currentRow.tecid == undefined) {
      this.toastService.error(
        this.translateService.instant(
          'permamonitor.admin.pds.table.update-failure-missing-category',
        ),
      );
      return;
    }
    this.pdsConfigTableService.startLoading();
    this.pdsConfigTableService
      .deleteSubcategory(
        currentRow.permDeploymentPlanTecid,
        currentRow.tecid,
        currentRow.teclock,
        currentRow.subcategoryTecid,
      )
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe({
        error: (error) => this.pdsConfigTableService.stopLoading(),
        next: (value) => {
          this.currentTableRows.content[index].subcategoryTecid =
            value.subcategoryTecid;
          this.pdsConfigTableService.setResults(this.currentTableRows);
          this.pdsConfigTableService.stopLoading();

          this.toastService.success(
            this.translateService.instant(
              'permamonitor.admin.pds.table.toasts.updated-subcategory',
            ),
          );
          this.cd.markForCheck();
        },
      });
  }

  castRow(rowContext: any): PermServicePlanCategoryWithSubcategoryModel {
    return rowContext as PermServicePlanCategoryWithSubcategoryModel;
  }

  getSubcategories(
    currentRow: PermServicePlanCategoryWithSubcategoryModel,
  ): any[] {
    if (currentRow.permCategoryTecid != undefined) {
      let map = this.categories
        .filter((value) => value.tecid == currentRow.permCategoryTecid)
        .map((value) => [...value.subcategories]);
      return map;
    }

    return [new PermSubcategoryModel({ name: 'No subcategories' })];
  }

  selectCategory(
    index: number,
    currentRow: PermServicePlanCategoryWithSubcategoryModel,
    newCategoryTecid: number,
  ) {
    this.pdsConfigTableService.startLoading();
    if (currentRow.tecid == undefined) {
      this.pdsConfigTableService
        .createCategory(
          this.deploymentPlanTecid,
          newCategoryTecid,
          currentRow.servicePlan.tecid,
        )
        .pipe(takeUntil(this._unsubscribe$))
        .subscribe({
          error: (error) => this.pdsConfigTableService.stopLoading(),
          next: (value) => {
            this.currentTableRows.content[index] = value;
            this.pdsConfigTableService.setResults(this.currentTableRows);
            this.pdsConfigTableService.stopLoading();
            this.toastService.success(
              this.translateService.instant(
                'permamonitor.admin.pds.table.toasts.updated-category',
              ),
            );
            this.cd.markForCheck();
          },
        });
    } else {
      this.pdsConfigTableService
        .updateCategory(
          currentRow.permDeploymentPlanTecid,
          currentRow.tecid,
          currentRow.teclock,
          newCategoryTecid,
        )
        .pipe(takeUntil(this._unsubscribe$))
        .subscribe({
          error: (error) => this.pdsConfigTableService.stopLoading(),
          next: (value) => {
            this.currentTableRows.content[index] = value;
            this.pdsConfigTableService.setResults(this.currentTableRows);
            this.pdsConfigTableService.stopLoading();
            this.toastService.success(
              this.translateService.instant(
                'permamonitor.admin.pds.table.toasts.updated-category',
              ),
            );
            this.cd.markForCheck();
          },
        });
    }
  }

  getAllCategories() {
    this.pdsConfigTableService
      .getAllCategoriesWithSubcaegories()
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((value) => {
        this.categories = value;
        this.subcategoriesByCategory = new Map<
          number,
          PermSubcategoryModel[]
        >();
        this.allSubcategories = [];
        this.categories.forEach((category) => {
          this.subcategoriesByCategory.set(
            category.tecid,
            category.subcategories,
          );
          if (category.subcategories) {
            category.subcategories.forEach((subcategory) => {
              this.allSubcategories.push(
                new FieldGroupOption({
                  value: subcategory.tecid,
                  I18NLabel: subcategory.name,
                }),
              );
            });
          }
        });
        console.error('this.allSubcategories', this.allSubcategories);
        this.entityTypesFilterValues = _.map(
          value,
          (oneType) =>
            new FieldGroupOption({
              value: oneType.name,
              I18NLabel: oneType.name,
            }),
        );
        this.cd.markForCheck();
      });
  }

  entitySelected($event: { entity: Entity; allUnderEntity: boolean }) {
    this.entityFormControl.setValue($event.entity?.tecid);
  }

  private loadBookmarkedValues(): void {
    this.bookmarkedFilterValues = [];

    this.bookmarkedFilterValues.push(
      new FieldGroupOption({
        value: 'true',
        label: this.translateService.instant(
          'permamonitor.admin.pds.table.favorite-true',
        ),
      }),
    );

    this.bookmarkedFilterValues.push(
      new FieldGroupOption({
        value: 'false',
        label: this.translateService.instant(
          'permamonitor.admin.pds.table.favorite-false',
        ),
      }),
    );
  }
}
