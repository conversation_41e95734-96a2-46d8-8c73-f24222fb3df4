import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Inject,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
} from '@angular/core';
import { PojCopyEntityConfigurationPopupService } from '@permamonitor/administration/views/poj-configuration/popup/copyentity/poj-copy-entity-configuration-popup.service';
import { Category } from '@permamonitor/permamonitor/common/constants';
import { Subject } from 'rxjs';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { PojCopyEntityConfigurationPopupData } from '@permamonitor/administration/views/poj-configuration/popup/copyentity/poj-copy-entity-configuration-popup-data';
import { TranslateModule } from '@ngx-translate/core';
import { FormModule } from '@app/common/modules/form-module/form.module';
import { DefaultFormTemplateModule } from '@app/common/template/default-form-template/default-form-template.module';
import { SimplePopupModule } from '@app/common/modules/popup/simple-popup.module';
import { FORM_SERVICE } from '@app/common/modules/form-module/service/iform.service';
import { Entity } from '@app/model/entity.model';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { NgxSelectModule } from 'ngx-select-ex';
import { takeUntil } from 'rxjs/operators';
import { PojConfigurationFiltersService } from '@permamonitor/administration/views/poj-configuration/filters/poj-configuration-filters.service';

export type PojCopyEntityConfigurationPopupContent = {
  deploymentPlanTecid: number;
  categoryName: Category;
  fromEntity: Entity;
  baseEntities: Entity[];
};

@Component({
  selector: 'cgdis-portal-poj-copy-entity-configuration',
  standalone: true,
  imports: [
    TranslateModule,
    FormModule,
    DefaultFormTemplateModule,
    SimplePopupModule,
    NgxSelectModule,
  ],
  templateUrl: './poj-copy-entity-configuration-popup.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    PojConfigurationFiltersService,
    PojCopyEntityConfigurationPopupService,
    {
      provide: FORM_SERVICE,
      useExisting: PojCopyEntityConfigurationPopupService,
    },
  ],
})
export class PojCopyEntityConfigurationPopupComponent
  implements OnDestroy, OnInit
{
  protected entities: FieldOption<number>[] = [];
  private _unsubscribe$ = new Subject<void>();

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: PojCopyEntityConfigurationPopupData,
    public dialogRef: MatDialogRef<PojCopyEntityConfigurationPopupComponent>,
    private popupService: PojCopyEntityConfigurationPopupService,
    private cd: ChangeDetectorRef,
    private pojConfigurationFiltersService: PojConfigurationFiltersService,
  ) {}

  ngOnInit(): void {
    this.data.subtitle = this.data.content.fromEntity.name;
    this.popupService
      .onSuccessSubmit()
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe(() => {
        this.data.onSubmitSuccess();
        this.closePopup();
      });

    this.loadEntities();
  }

  public closePopup(): void {
    this.dialogRef.close(false);
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }

  private loadEntities(): void {
    this.pojConfigurationFiltersService
      .getAllCis(
        this.data.content.deploymentPlanTecid,
        this.data.content.categoryName,
        this.data.content.baseEntities
          ? this.data.content.baseEntities.map((entity) => entity.tecid)
          : [],
        true,
      )
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((value: Entity[]) => {
        this.entities = value
          .filter(
            (value1) => value1.tecid !== this.data.content.fromEntity.tecid,
          )
          .map(
            (entity) =>
              new FieldOption({
                value: entity.tecid,
                label: entity.name,
              }),
          );
        this.cd.markForCheck();
      });
  }
}
