import { ChangeDetectionStrategy, Component } from '@angular/core';
import { PageTemplateModule } from '@app/common/template/page-template/page-template.module';
import { SharedModule } from '@app/common/shared/shared.module';
import { DeploymentPlanVersionCreateService } from '@permamonitor/administration/views/deployment-plan-versions/deployment-plan-versions-create/deployment-plan-version-create.service';
import { DefaultFormTemplateModule } from '@app/common/template/default-form-template/default-form-template.module';
import { FormModule } from '@app/common/modules/form-module/form.module';
import { FORM_SERVICE } from '@app/common/modules/form-module/service/iform.service';
import { ActivatedRoute, Router } from '@angular/router';

@Component({
  selector: 'cgdis-portal-deployment-plan-version-create',
  standalone: true,
  imports: [
    PageTemplateModule,
    SharedModule,
    DefaultFormTemplateModule,
    FormModule,
  ],
  templateUrl: './deployment-plan-version-create.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    DeploymentPlanVersionCreateService,
    {
      provide: FORM_SERVICE,
      useExisting: DeploymentPlanVersionCreateService,
    },
  ],
})
export class DeploymentPlanVersionCreateComponent {
  constructor(
    private deploymentPlanVersionCreateService: DeploymentPlanVersionCreateService,
    private router: Router,
    private activeRoute: ActivatedRoute,
  ) {}

  submit() {
    this.deploymentPlanVersionCreateService.submitForm();
  }

  cancel() {
    this.router.navigate(['../'], {
      relativeTo: this.activeRoute,
    });
  }
}
