import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { map } from 'rxjs/operators';
import { DeploymentPlanModel } from '@app/model/permamonitor/service-plan-category/deployment-plan.model';

@Injectable()
export class DeploymentPlanSelectorService {
  constructor(private _restService: RestService) {}

  getAllPdsVersions() {
    return this._restService
      .all('permamonitor', 'admin', 'deployment-plans')
      .get()
      .pipe(
        map((response: DeploymentPlanModel[]) => response.map((item) => item)),
      );
  }
}
