import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  <PERSON><PERSON><PERSON><PERSON>,
  On<PERSON><PERSON>roy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { DefaultFormTemplateModule } from '@app/common/template/default-form-template/default-form-template.module';
import { FormModule } from '@app/common/modules/form-module/form.module';
import { PageTemplateModule } from '@app/common/template/page-template/page-template.module';
import { SharedModule } from '@app/common/shared/shared.module';
import { FORM_SERVICE } from '@app/common/modules/form-module/service/iform.service';
import { DeploymentPlanVersionUpdateService } from '@permamonitor/administration/views/deployment-plan-versions/deployment-plan-version-update/deployment-plan-version-update.service';
import { ActivatedRoute, Router } from '@angular/router';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { DeploymentPlanModel } from '@app/model/permamonitor/service-plan-category/deployment-plan.model';
import { PermDeploymentPlanUpdateFormValidationModel } from '@app/model/permamonitor/deployment-plan/perm-deployment-plan-update-form-validation.model';
import { ConnectedUserService } from '@app/security/connected-user.service';

@Component({
  selector: 'cgdis-portal-deployment-plan-version-update',
  standalone: true,
  imports: [
    DefaultFormTemplateModule,
    FormModule,
    PageTemplateModule,
    SharedModule,
  ],
  templateUrl: './deployment-plan-version-update.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    DeploymentPlanVersionUpdateService,
    {
      provide: FORM_SERVICE,
      useExisting: DeploymentPlanVersionUpdateService,
    },
  ],
})
export class DeploymentPlanVersionUpdateComponent
  implements OnInit, OnDestroy, OnChanges
{
  protected currentDeploymentPlan: DeploymentPlanModel;
  protected formValidation: PermDeploymentPlanUpdateFormValidationModel =
    new PermDeploymentPlanUpdateFormValidationModel({
      name: false,
      startDate: false,
      description: false,
    });
  private _unsubscribe$ = new Subject<void>();
  protected canUpdate = false;

  constructor(
    private deploymentPlanVersionUpdateService: DeploymentPlanVersionUpdateService,
    private activeRoute: ActivatedRoute,
    private router: Router,
    private cd: ChangeDetectorRef,
    private connectedUserService: ConnectedUserService,
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.currentDeploymentPlan && this.currentDeploymentPlan) {
      console.log(this.currentDeploymentPlan.tecid);
    }
  }

  submit() {
    this.deploymentPlanVersionUpdateService.submitForm();
  }

  ngOnInit() {
    this.connectedUserService
      .hasAnyRolesObservable([
        'ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_DEPLOYMENT_PLAN',
      ])
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe({
        next: (value: boolean) => {
          this.canUpdate = value;
          this.cd.markForCheck();
        },
      });

    this.activeRoute.params
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((params) => {
        this.deploymentPlanVersionUpdateService
          .getDeploymentPlanByTecid(params['deploymentPlanTecid'])
          .subscribe((value) => {
            this.currentDeploymentPlan = value;
            this.isDeploymentPlanEditable(value.tecid);
            this.cd.markForCheck();
          });
      });
  }

  isDeploymentPlanEditable(tecid: number) {
    this.deploymentPlanVersionUpdateService
      .isDeploymentPlanEditable(tecid)
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((value) => {
        this.formValidation = value;
        this.cd.markForCheck();
      });
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }

  cancel() {
    this.router.navigate(['../../'], {
      relativeTo: this.activeRoute,
    });
  }
}
