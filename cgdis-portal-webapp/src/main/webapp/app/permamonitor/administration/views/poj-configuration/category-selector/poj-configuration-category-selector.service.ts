import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { PermCategoryModel } from '@app/model/permamonitor/perm-category.model';

@Injectable()
export class PojConfigurationCategorySelectorService {
  constructor(private _restService: RestService) {}

  getAllCategories() {
    return this._restService
      .all<PermCategoryModel>('permamonitor', 'categories', 'all')
      .get();
  }
}
