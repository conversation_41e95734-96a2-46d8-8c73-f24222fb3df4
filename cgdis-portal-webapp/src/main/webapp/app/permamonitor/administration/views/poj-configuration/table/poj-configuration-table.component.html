@if (cisgis?.length || data?.length > 0) {
  <div class="poj-table">
    <div class="poj-table-row">
      <div class="poj-table-cell poj-table-header time-header">
        <span [translate]="'permamonitor.admin.poj.table.time-title'" class="time-header-title "></span>
        <cgdis-portal-link-with-icon *ngIf="isDeploymentPlanEditable && canUpdate" (click)="openDuplicatePopup()"
                                     [icon]="'icon-duplicat'"
                                     [rounded]="true" [smallIcon]="true"></cgdis-portal-link-with-icon>
      </div>
      @for (time of times; track time) {
        <div class="poj-table-cell time-cell" [ngClass]="{'highlight-row': $index === selectionIndexes?.hours}">
          {{ time }}
        </div>
      }
    </div>
    @for (day of days; track day) {
      <div class="poj-table-row">
        <div class="poj-table-cell poj-table-header">
          <span class="day-header-title" [translate]="'permamonitor.admin.poj.table.days.' + day"></span>
          <div class="values header-title ">
            <span class="value opt">Opt</span>
            <span class="value crit">Crit</span>
            <span class="value inac">Noac</span>
          </div>
        </div>
        @if (data) {
          @for (pojData of data[$index]; track data; let idx = $index) {
            <div class="poj-table-cell">
              <div class="values">
                <input type="text"
                       [tabindex]="(idx+1)*50 + $index * 3"
                       [disabled]="!isDeploymentPlanEditable || !canUpdate" [ngClass]="{
                                 'highlight-row': (idx === selectionIndexes?.hours) && ($index <= selectionIndexes.day) && ((selectionIndexes.type === 'crit' || selectionIndexes.type === 'opt' || selectionIndexes.type === 'inac')|| ($index < selectionIndexes.day && selectionIndexes.type !== 'opt')),
                                 'highlight-col': ($index === selectionIndexes?.day)  && (idx <= selectionIndexes.hours) && (selectionIndexes.type === 'opt'),
                                 'selection': ($index === selectionIndexes?.day)  && (idx === selectionIndexes.hours) && (selectionIndexes.type === 'opt'),
                                 'with-delta': (pojData.includedOptDelta != undefined && pojData.includedOptDelta !== 0)
                                 }"
                       [(ngModel)]="pojData.opt"
                       class="value opt"
                       (blur)="onBlur($index,idx,pojData)" (focus)="onFocus(pojData, $index, 'opt')"
                       (ngModelChange)="onValueChange()"
                       (keydown)="onKeydown($event, inputRef, pojData.opt )" #inputRef />
                <input [disabled]="!isDeploymentPlanEditable || !canUpdate" type="text" [(ngModel)]="pojData.crit"
                       [tabindex]="(idx+1)*50 + $index * 3 + 1"
                       class="value crit"
                       [ngClass]="{
                                 'highlight-row': (idx === selectionIndexes?.hours) && ($index <= selectionIndexes.day) && ((selectionIndexes.type === 'crit' || selectionIndexes.type === 'inac' ) || ($index < selectionIndexes.day && selectionIndexes.type !== 'crit')),
                                 'highlight-col': ($index === selectionIndexes?.day)  && (idx <= selectionIndexes.hours) && (selectionIndexes.type === 'crit'),
                                 'selection': ($index === selectionIndexes?.day)  && (idx === selectionIndexes.hours) && (selectionIndexes.type === 'crit'),
                                 'with-delta': (pojData.includedCritDelta != undefined && pojData.includedCritDelta !== 0)}"
                       (blur)="onBlur($index,idx,pojData)" (focus)="onFocus(pojData,$index,'crit')"
                       (keydown)="onKeydown($event, inputRef2, pojData.crit, pojData.opt)" #inputRef2
                       (ngModelChange)="onValueChange()" />
                <input [disabled]="!isDeploymentPlanEditable || !canUpdate" type="text" [(ngModel)]="pojData.inac"
                       [tabindex]="(idx+1)*50 + $index * 3 + 2"
                       (blur)="onBlur($index,idx,pojData)" [ngClass]="{
                                 'highlight-row': (idx === selectionIndexes?.hours) && ($index <= selectionIndexes.day) && ((selectionIndexes.type === 'inac') || ($index < selectionIndexes.day && selectionIndexes.type !== 'inac') ),
                                 'highlight-col': ($index === selectionIndexes?.day)  && (idx <= selectionIndexes.hours) && (selectionIndexes.type === 'inac'),
                                 'selection': ($index === selectionIndexes?.day)  && (idx === selectionIndexes.hours) && (selectionIndexes.type === 'inac'),
                                 'with-delta': (pojData.includedInacDelta != undefined && pojData.includedInacDelta !== 0)}"
                       (focus)="onFocus(pojData, $index, 'inac')"
                       (keydown)="onKeydown($event, inputRef3, pojData.inac, pojData.crit)"
                       (ngModelChange)="onValueChange()"
                       #inputRef3
                       class="value inac"
                />
              </div>
            </div>
          }
        }
      </div>
    }
  </div>
} @else {
  <h2 class="no-cis-note">{{ 'permamonitor.admin.poj.table.no-cis-note' | translate }}</h2>

}
<cgdis-portal-spinner [loading]="loading"></cgdis-portal-spinner>
