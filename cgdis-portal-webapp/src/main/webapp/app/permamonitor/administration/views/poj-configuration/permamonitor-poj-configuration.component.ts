import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnInit,
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { PageTemplateModule } from '@app/common/template/page-template/page-template.module';
import { SharedModule } from '@app/common/shared/shared.module';
import { PojConfigurationCategorySelectorComponent } from '@permamonitor/administration/views/poj-configuration/category-selector/poj-configuration-category-selector.component';
import { PojConfigurationTableComponent } from '@permamonitor/administration/views/poj-configuration/table/poj-configuration-table.component';
import { PermCategoryModel } from '@app/model/permamonitor/perm-category.model';
import { Entity } from '@app/model/entity.model';
import { ServicePlanConfigTableService } from '@permamonitor/administration/views/service-plan-configuration/service-plan-config-table/service-plan-config-table.service';
import {
  PojConfigurationFiltersComponent,
  POJFilterOutput,
} from '@permamonitor/administration/views/poj-configuration/filters/poj-configuration-filters.component';

@Component({
  selector: 'cgdis-portal-permamonitor-poj-configuration',
  standalone: true,
  imports: [
    PageTemplateModule,
    SharedModule,
    PojConfigurationCategorySelectorComponent,
    PojConfigurationFiltersComponent,
    PojConfigurationTableComponent,
  ],
  templateUrl: './permamonitor-poj-configuration.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ServicePlanConfigTableService],
})
export class PermamonitorPojConfigurationComponent implements OnInit {
  protected deploymentPlanTecid: number;

  protected selectedCategory: PermCategoryModel;
  protected selectedFilter: POJFilterOutput;
  protected cisgis: Entity[];
  protected isDeploymentPlanEditable: boolean = false;

  constructor(
    private route: ActivatedRoute,
    private cd: ChangeDetectorRef,
    protected pdsConfigTableService: ServicePlanConfigTableService,
  ) {}

  ngOnInit() {
    this.deploymentPlanTecid = parseInt(
      this.route.snapshot.paramMap.get('deploymentPlanTecid'),
    );
    this.pdsConfigTableService
      .isDeploymentPlanEditable(this.deploymentPlanTecid)
      .subscribe((value) => {
        this.isDeploymentPlanEditable = value;
        this.cd.detectChanges();
      });
  }

  setSelectedCategory(category: PermCategoryModel) {
    this.selectedCategory = { ...category };
  }

  setPOJFilter(filter: POJFilterOutput) {
    this.selectedFilter = { ...filter };
  }

  setCisGis(cisgis: Entity[]) {
    this.cisgis = cisgis;
  }
}
