import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { EntityFilterModule } from '@app/common/modules/entity-filter/entity-filter.module';
import { FormControl } from '@angular/forms';
import { SearchOperator } from '@eportal/components';
import { Entity } from '@app/model/entity.model';
import { ServicePlanConfigTableService } from '@permamonitor/administration/views/service-plan-configuration/service-plan-config-table/service-plan-config-table.service';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'cgdis-portal-entity-selector',
  standalone: true,
  imports: [EntityFilterModule, TranslateModule],
  templateUrl: './entity-selector.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class EntitySelectorComponent implements OnInit {
  protected entityFormControl = new FormControl<number>(undefined);

  constructor(
    // Service defined in parent
    protected pdsConfigTableService: ServicePlanConfigTableService,
  ) {}

  entitySelected($event: { entity: Entity; allUnderEntity: boolean }) {
    this.entityFormControl.setValue($event.entity?.tecid);
  }

  ngOnInit(): void {
    this.pdsConfigTableService.addFilterWithFormControl(
      'permConfigDpceEntityTecid',
      this.entityFormControl,
      {
        operator: SearchOperator.eq,
      },
    );
  }
}
