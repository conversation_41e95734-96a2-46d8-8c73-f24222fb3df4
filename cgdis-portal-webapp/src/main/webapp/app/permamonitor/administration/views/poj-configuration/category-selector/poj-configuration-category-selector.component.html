<div class="category-selector">
  <label for="categorySelect"> {{ 'permamonitor.category-selector-label' | translate }}:</label>
  @if (categories) {
    <ngx-select
      (selectionChanges)="onCategoryChange($event)"
      [defaultValue]="[initialCategory.tecid]"
      [optionTextField]="'name'" [optionValueField]="'tecid'"
      [items]="categories"
      id="categorySelect"
    >
    </ngx-select>
  }

</div>
