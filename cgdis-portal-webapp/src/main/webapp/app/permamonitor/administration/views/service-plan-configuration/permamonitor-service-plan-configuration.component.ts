import { ChangeDetectionStrategy, Component } from '@angular/core';
import { PageTemplateModule } from '@app/common/template/page-template/page-template.module';
import { RouterLink, RouterOutlet } from '@angular/router';
import { SharedModule } from '@app/common/shared/shared.module';
import { PermamonitorCategorySelectorComponent } from '@permamonitor/permamonitor/common/category-selector/category-selector.component';
import { ServicePlanConfigTableComponent } from '@permamonitor/administration/views/service-plan-configuration/service-plan-config-table/service-plan-config-table.component';
import { DeploymentPlanSelectorComponent } from '@permamonitor/administration/views/service-plan-configuration/deployment-plan-selector/deployment-plan-selector.component';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { ServicePlanConfigTableService } from '@permamonitor/administration/views/service-plan-configuration/service-plan-config-table/service-plan-config-table.service';
import { EntityFilterModule } from '@app/common/modules/entity-filter/entity-filter.module';
import { EntitySelectorComponent } from '@permamonitor/administration/views/service-plan-configuration/entity-selector/entity-selector.component';

@Component({
  selector: 'cgdis-portal-permamonitor-pds-configuration',
  standalone: true,
  imports: [
    PageTemplateModule,
    RouterOutlet,
    SharedModule,
    PermamonitorCategorySelectorComponent,
    ServicePlanConfigTableComponent,
    DeploymentPlanSelectorComponent,
    RouterLink,
    DatatableModule,
    EntityFilterModule,
    EntitySelectorComponent,
  ],
  templateUrl: './permamonitor-service-plan-configuration.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ServicePlanConfigTableService],
})
export class PermamonitorServicePlanConfigurationComponent {
  protected deploymentPlanTecid: number;

  constructor() {}

  onPdsVersionChange(deploymentPlanTecid: number) {
    this.deploymentPlanTecid = deploymentPlanTecid;
  }
}
