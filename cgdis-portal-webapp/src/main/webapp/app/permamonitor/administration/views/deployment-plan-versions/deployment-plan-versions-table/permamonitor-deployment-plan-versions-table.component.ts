import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { PermamonitorDeploymentPlanVersionsTableService } from '@permamonitor/administration/views/deployment-plan-versions/deployment-plan-versions-table/permamonitor-deployment-plan-versions-table.service';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { NgxSelectModule } from 'ngx-select-ex';
import { SharedModule } from '@app/common/shared/shared.module';
import { DateModel, DateService } from '@eportal/core';
import { Router } from '@angular/router';
import { SimpleTableModule } from '@app/common/modules/simple-table/simple-table.module';
import { BreakpointObserver } from '@angular/cdk/layout';
import { Subject } from 'rxjs';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { DeploymentPlanModel } from '@app/model/permamonitor/service-plan-category/deployment-plan.model';

@Component({
  selector: 'cgdis-portal-permamonitor-pa-versions-table',
  standalone: true,
  imports: [
    DatatableModule,
    EpDatatableModule,
    NgxSelectModule,
    SharedModule,
    SimpleTableModule,
  ],
  templateUrl: './permamonitor-deployment-plan-versions-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [PermamonitorDeploymentPlanVersionsTableService],
})
export class PermamonitorDeploymentPlanVersionsTableComponent
  implements OnInit, OnDestroy
{
  protected dynamicPageSize: number;
  protected canAccessConfigs = false;
  protected windowHeight: number = window.innerHeight;
  private _unsubscribe$ = new Subject<void>();

  constructor(
    private breakpointObserver: BreakpointObserver,
    private router: Router,
    protected permamonitorPaVersionsTableService: PermamonitorDeploymentPlanVersionsTableService,
    private dateService: DateService,
    private connectedUserService: ConnectedUserService,
    private cd: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    this.windowHeight = window.innerHeight;
    this.dynamicPageSize = Math.floor(window.innerHeight / 110);

    this.connectedUserService
      .hasAnyRolesObservable([
        'ROLE_PERMISSION_PERMAMONITOR_ADMIN_ACCESS_CONFIG',
      ])
      .subscribe((value) => {
        this.canAccessConfigs = value;
        this.cd.markForCheck();
      });
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }

  getDateString(date: DateModel) {
    return this.dateService.format(date, 'DD/MM/YYYY');
  }

  navigateToSelectedPds(deploymentPlanTecid: number) {
    this.router.navigate(['/permamonitor/admin/service-plans'], {
      queryParams: {
        filter: JSON.stringify({
          criteria: [
            {
              operator: 'eq',
              attribute: 'permDeploymentPlanTecid',
              value: deploymentPlanTecid,
            },
          ],
          pageNumber: 0,
          pageSize: this.dynamicPageSize,
          orderBy: null,
        }),
      },
    });
  }

  navigateToSelectedPoj(deploymentPlanTecid: number) {
    this.router.navigate(['/permamonitor/admin/poj/' + deploymentPlanTecid]);
  }

  editDeploymentPlan(event: DeploymentPlanModel) {
    this.router.navigate(['/permamonitor/admin/update/' + event.tecid]);
  }
}
