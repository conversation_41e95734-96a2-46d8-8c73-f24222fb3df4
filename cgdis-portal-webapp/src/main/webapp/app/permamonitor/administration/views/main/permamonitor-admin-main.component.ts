import { ChangeDetectionStrategy, Component } from '@angular/core';
import { PageTemplateModule } from '@app/common/template/page-template/page-template.module';
import { PermamonitorCategorySelectorComponent } from '@permamonitor/permamonitor/common/category-selector/category-selector.component';
import { RouterOutlet } from '@angular/router';
import { SharedModule } from '@app/common/shared/shared.module';

@Component({
  selector: 'cgdis-portal-permamonitor-admin-main',
  standalone: true,
  imports: [
    PageTemplateModule,
    PermamonitorCategorySelectorComponent,
    RouterOutlet,
    SharedModule,
  ],
  templateUrl: './permamonitor-admin-main.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PermamonitorAdminMainComponent {}
