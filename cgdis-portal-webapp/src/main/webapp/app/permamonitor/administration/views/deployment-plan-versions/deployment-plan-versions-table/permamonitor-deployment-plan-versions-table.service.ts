import { Injectable } from '@angular/core';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { DateModel, RestService } from '@eportal/core';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { DeploymentPlanModel } from '@app/model/permamonitor/service-plan-category/deployment-plan.model';

@Injectable()
export class PermamonitorDeploymentPlanVersionsTableService extends CgdisDatatableService<DeploymentPlanModel> {
  constructor(
    private restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService);
    super.initDataResourceList(
      restService.all(
        'permamonitor',
        'admin',
        'deployment-plans',
        'table-data',
      ),
    );
  }

  updatePaTest() {
    return this.restService
      .one('permamonitor', 'admin', 'deployment-plan', 'update')
      .patch({
        tecid: 59,
        teclock: 0,
        name: 'newtest',
        startDate: new DateModel({
          day: 12,
          month: 1,
          year: 2027,
        }),
      });
  }
}
