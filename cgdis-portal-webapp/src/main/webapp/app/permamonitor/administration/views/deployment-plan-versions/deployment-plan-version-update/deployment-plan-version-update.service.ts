import { Injectable } from '@angular/core';
import { DefaultFormService } from '@app/common/modules/form-module/service/default-form.service';
import { FormError } from '@app/common/modules/error-management/model/form-error.model';
import { Observable } from 'rxjs';
import { DeploymentPlanModel } from '@app/model/permamonitor/service-plan-category/deployment-plan.model';
import { FormErrorService } from '@app/common/modules/form-module/service/form-error.service';
import { ToastService } from '@app/common/shared/toasts/CGDISToastService';
import { DateModel, RestService } from '@eportal/core';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute, Router } from '@angular/router';
import { PermDeploymentPlanPatchModel } from '@app/model/permamonitor/deployment-plan/perm-deployment-plan-patch.model';
import { PermDeploymentPlanUpdateFormValidationModel } from '@app/model/permamonitor/deployment-plan/perm-deployment-plan-update-form-validation.model';

@Injectable()
export class DeploymentPlanVersionUpdateService extends DefaultFormService<
  PermDeploymentPlanPatchModel,
  DeploymentPlanModel
> {
  constructor(
    formErrorService: FormErrorService,
    private toastr: ToastService,
    private restService: RestService,
    protected translationService: TranslateService,
    private router: Router,
    private route: ActivatedRoute,
  ) {
    super(
      toastr,
      translationService.instant('permamonitor.admin.pa.update-form.toast'),
      formErrorService,
    );
  }

  submit(parameter: any): Observable<any> {
    return this.updateDeploymentPlan(
      parameter.tecid,
      parameter.teclock,
      parameter.name,
      parameter.description,
      parameter.startDate,
    );
  }

  submitSuccess(result: any): void {
    this.toastr.warning(
      this.translationService.instant(
        'permamonitor.admin.pa.update-form.toast-warning',
      ),
    );
    this.router.navigate(['../../'], { relativeTo: this.route });
  }

  submitError(formError: FormError): void {}

  getDeploymentPlanByTecid(tecid: number) {
    return this.restService
      .one<DeploymentPlanModel>(
        'permamonitor',
        'admin',
        'deployment-plan',
        tecid.toString(),
      )
      .get();
  }

  updateDeploymentPlan(
    tecid: number,
    teclock: number,
    name: string,
    description: string,
    startDate: DateModel,
  ) {
    return this.restService
      .one('permamonitor', 'admin', 'deployment-plan', 'update')
      .patch(
        new PermDeploymentPlanPatchModel({
          tecid: tecid,
          teclock: teclock,
          name: name,
          description: description,
          startDate: startDate,
        }),
      );
  }

  isDeploymentPlanEditable(tecid: number) {
    return this.restService
      .one<PermDeploymentPlanUpdateFormValidationModel>(
        'permamonitor',
        'admin',
        'deployment-plan',
        tecid.toString(),
        'is-editable',
      )
      .get();
  }
}
