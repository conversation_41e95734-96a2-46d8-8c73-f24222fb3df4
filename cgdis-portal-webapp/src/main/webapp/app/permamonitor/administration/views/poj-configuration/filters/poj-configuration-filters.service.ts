import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { Entity } from '@app/model/entity.model';
import { Category } from '@permamonitor/permamonitor/common/constants';
import { EntityType } from '@app/model/entity-type.enum';

@Injectable()
export class PojConfigurationFiltersService {
  constructor(private restService: RestService) {}

  getAllCis(
    deploymentPlanTecid: number,
    category: Category,
    baseEntityTecid: number[],
    withGis: boolean,
  ) {
    let params: {
      entityType: EntityType[];
      withGis: boolean;
      baseEntityTecid?: number[];
    } = {
      entityType: [EntityType.CIS],
      withGis: withGis,
    };
    if (baseEntityTecid) {
      params.baseEntityTecid = baseEntityTecid;
    }
    return this.restService
      .all<Entity>(
        'permamonitor',
        'admin',
        'deployment-plan',
        deploymentPlanTecid.toString(),
        category,
        'entities',
      )
      .get(params);
  }
}
