import { Injectable } from '@angular/core';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { RestService } from '@eportal/core';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { map } from 'rxjs/operators';
import { PermCategoryModel } from '@app/model/permamonitor/perm-category.model';
import { PermServicePlanCategoryModel } from '@app/model/permamonitor/service-plan-category/perm-service-plan-category.model';
import { Observable } from 'rxjs';
import { PermServicePlanCategoryPatchIsBookmarkedModel } from '@app/model/permamonitor/service-plan-category/perm-service-plan-category-patch-is-bookmarked.model';
import { PermServicePlanCategoryPatchCategoryModel } from '@app/model/permamonitor/service-plan-category/perm-service-plan-category-patch-category.model';
import { PermServicePlanCategoryCreateCategoryModel } from '@app/model/permamonitor/service-plan-category/perm-service-plan-category-create-category.model';
import { PermCategoryWithSubcategoriesModel } from '@app/model/permamonitor/perm-category-with-subcategories.model';
import { PermServicePlanCategoryPatchSubcategoryModel } from '@app/model/permamonitor/service-plan-category/perm-service-plan-category-patch-subcategory.model';
import { PermServicePlanCategoryWithSubcategoryModel } from '@app/model/permamonitor/service-plan-category/perm-service-plan-category-with-subcategroy.model';

@Injectable()
export class ServicePlanConfigTableService extends CgdisDatatableService<PermServicePlanCategoryModel> {
  constructor(
    private restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService);
    super.initDataResourceList(
      restService.all('permamonitor', 'admin', 'service-plans'),
    );
  }

  getAllCategories() {
    return this.restService
      .all('permamonitor', 'categories', 'all')
      .get()
      .pipe(
        map((response: PermCategoryModel[]) => response.map((item) => item)),
      );
  }

  getAllCategoriesWithSubcaegories() {
    return this.restService
      .all('permamonitor', 'categories', 'subcategories')
      .get()
      .pipe(
        map((response: PermCategoryWithSubcategoriesModel[]) =>
          response.map((item) => new PermCategoryWithSubcategoriesModel(item)),
        ),
      );
  }

  createCategory(
    permDeploymentPlanTecid: number,
    categoryTecid: number,
    servicePlanTecid: number,
  ): Observable<PermServicePlanCategoryModel> {
    return this.restService
      .one(
        'permamonitor',
        'admin',
        permDeploymentPlanTecid.toString(),
        'service-plan',
        'category',
        categoryTecid.toString(),
      )
      .post(
        new PermServicePlanCategoryCreateCategoryModel({
          permCategoryTecid: categoryTecid,
          servicePlanTecid: servicePlanTecid,
          permDeploymentPlanTecid: permDeploymentPlanTecid,
        }),
      );
  }

  updateSubcategory(
    deploymentPlanCategoryTecid: number,
    servicePlanCategoryTecid: number,
    servicePlanCategoryTeclock: number,
    subcategoryTecid: number,
  ): Observable<PermServicePlanCategoryWithSubcategoryModel> {
    return this.restService
      .one(
        'permamonitor',
        'admin',
        deploymentPlanCategoryTecid.toString(),
        'service-plan',
        servicePlanCategoryTecid.toString(),
        'subcategory',
      )
      .patch(
        new PermServicePlanCategoryPatchSubcategoryModel({
          tecid: servicePlanCategoryTecid,
          teclock: servicePlanCategoryTeclock,
          subcategoryTecid: subcategoryTecid,
        }),
      );
  }

  deleteCategory(
    deploymentPlanCategoryTecid: number,
    servicePlanCategoryTecid: number,
    servicePlanCategoryTeclock: number,
    categoryTecid: number,
  ): Observable<boolean> {
    return this.restService
      .one(
        'permamonitor',
        'admin',
        deploymentPlanCategoryTecid.toString(),
        'service-plan',
        servicePlanCategoryTecid.toString(),
        'deletecategory',
      )
      .patch(
        new PermServicePlanCategoryPatchCategoryModel({
          tecid: servicePlanCategoryTecid,
          teclock: servicePlanCategoryTeclock,
          permCategoryTecid: categoryTecid,
        }),
      );
  }

  deleteSubcategory(
    deploymentPlanCategoryTecid: number,
    servicePlanCategoryTecid: number,
    servicePlanCategoryTeclock: number,
    subcategoryTecid: number,
  ): Observable<PermServicePlanCategoryWithSubcategoryModel> {
    return this.restService
      .one(
        'permamonitor',
        'admin',
        deploymentPlanCategoryTecid.toString(),
        'service-plan',
        servicePlanCategoryTecid.toString(),
        'deletesubcategory',
      )
      .patch(
        new PermServicePlanCategoryPatchSubcategoryModel({
          tecid: servicePlanCategoryTecid,
          teclock: servicePlanCategoryTeclock,
          subcategoryTecid: subcategoryTecid,
        }),
      );
  }

  updateCategory(
    deploymentPlanCategoryTecid: number,
    servicePlanCategoryTecid: number,
    servicePlanCategoryTeclock: number,
    categoryTecid: number,
  ): Observable<PermServicePlanCategoryModel> {
    return this.restService
      .one(
        'permamonitor',
        'admin',
        deploymentPlanCategoryTecid.toString(),
        'service-plan',
        servicePlanCategoryTecid.toString(),
        'category',
      )
      .patch(
        new PermServicePlanCategoryPatchCategoryModel({
          tecid: servicePlanCategoryTecid,
          teclock: servicePlanCategoryTeclock,
          permCategoryTecid: categoryTecid,
        }),
      );
  }

  updateIsBookmarked(
    deploymentPlanCategoryTecid: number,
    servicePlanCategoryTecid: number,
    servicePlanCategoryTeclock: number,
    isBookmarked: boolean,
  ): Observable<PermServicePlanCategoryModel> {
    return this.restService
      .one(
        'permamonitor',
        'admin',
        deploymentPlanCategoryTecid.toString(),
        'service-plan',
        servicePlanCategoryTecid.toString(),
        'bookmarked',
      )
      .patch(
        new PermServicePlanCategoryPatchIsBookmarkedModel({
          tecid: servicePlanCategoryTecid,
          teclock: servicePlanCategoryTeclock,
          isBookmarked: isBookmarked,
        }),
      );
  }

  isDeploymentPlanEditable(tecid: number) {
    return this.restService
      .one<boolean>(
        'permamonitor',
        'admin',
        'deployment-plan',
        tecid.toString(),
        'config',
        'is-editable',
      )
      .get();
  }
}
