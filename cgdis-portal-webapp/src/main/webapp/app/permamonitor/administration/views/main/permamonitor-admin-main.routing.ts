import { Routes } from '@angular/router';
import { PermamonitorAdminMainComponent } from '@permamonitor/administration/views/main/permamonitor-admin-main.component';
import { RoleGuard } from '@app/security/guards/role.guard';

export const PERMAMONITOR_ADMIN_MAIN_ROUTE: Routes = [
  {
    path: '',
    component: PermamonitorAdminMainComponent,
    canActivate: [RoleGuard],
    canActivateChild: [RoleGuard],
    data: {
      expectedRoles: ['ROLE_PERMISSION_PERMAMONITOR_ADMIN_VIEW'],
    },
    children: [
      {
        path: '',
        loadComponent: () =>
          import(
            '@permamonitor/administration/views/deployment-plan-versions/permamonitor-deployment-plan-versions.component'
          ).then((m) => m.PermamonitorDeploymentPlanVersionsComponent),
      },
      {
        path: 'create',
        loadComponent: () =>
          import(
            '@permamonitor/administration/views/deployment-plan-versions/deployment-plan-versions-create/deployment-plan-version-create.component'
          ).then((m) => m.DeploymentPlanVersionCreateComponent),
      },
      {
        path: 'update/:deploymentPlanTecid',
        loadComponent: () =>
          import(
            '@permamonitor/administration/views/deployment-plan-versions/deployment-plan-version-update/deployment-plan-version-update.component'
          ).then((m) => m.DeploymentPlanVersionUpdateComponent),
      },
      {
        path: 'service-plans',
        loadComponent: () =>
          import(
            '@permamonitor/administration/views/service-plan-configuration/permamonitor-service-plan-configuration.component'
          ).then((m) => m.PermamonitorServicePlanConfigurationComponent),
      },
      {
        path: 'poj/:deploymentPlanTecid',
        loadComponent: () =>
          import(
            '@permamonitor/administration/views/poj-configuration/permamonitor-poj-configuration.component'
          ).then((m) => m.PermamonitorPojConfigurationComponent),
      },
    ],
  },
];
