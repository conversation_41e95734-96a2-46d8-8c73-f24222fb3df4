import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnInit,
} from '@angular/core';
import { DeploymentPlanSelectorComponent } from '@permamonitor/administration/views/service-plan-configuration/deployment-plan-selector/deployment-plan-selector.component';
import { PageTemplateModule } from '@app/common/template/page-template/page-template.module';
import { ServicePlanConfigTableComponent } from '@permamonitor/administration/views/service-plan-configuration/service-plan-config-table/service-plan-config-table.component';
import { SharedModule } from '@app/common/shared/shared.module';
import { PermamonitorDeploymentPlanVersionsTableComponent } from '@permamonitor/administration/views/deployment-plan-versions/deployment-plan-versions-table/permamonitor-deployment-plan-versions-table.component';
import { EntityFilterModule } from '@app/common/modules/entity-filter/entity-filter.module';
import { ActivatedRoute, Router } from '@angular/router';
import { ConnectedUserService } from '@app/security/connected-user.service';

@Component({
  selector: 'cgdis-portal-permamonitor-administration',
  standalone: true,
  imports: [
    DeploymentPlanSelectorComponent,
    PageTemplateModule,
    ServicePlanConfigTableComponent,
    SharedModule,
    PermamonitorDeploymentPlanVersionsTableComponent,
    EntityFilterModule,
  ],
  templateUrl: './permamonitor-deployment-plan-versions.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PermamonitorDeploymentPlanVersionsComponent implements OnInit {
  protected canCreate = false;

  constructor(
    private router: Router,
    private activeRoute: ActivatedRoute,
    private connectedUserService: ConnectedUserService,
    private cd: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    this.connectedUserService
      .hasAnyRolesObservable([
        'ROLE_PERMISSION_PERMAMONITOR_ADMIN_CREATE_DEPLOYMENT_PLAN',
      ])
      .subscribe((value) => {
        this.canCreate = value;
        this.cd.markForCheck();
      });
  }

  addDeploymentPlan() {
    this.router.navigate(['create'], { relativeTo: this.activeRoute });
  }
}
