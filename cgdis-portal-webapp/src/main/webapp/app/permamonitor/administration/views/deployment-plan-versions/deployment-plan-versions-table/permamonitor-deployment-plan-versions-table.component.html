<cgdis-portal-cgdisdatatable
  (rowClicked)="editDeploymentPlan($event)"
  [datatableService]="permamonitorPaVersionsTableService"
  [pageSize]="dynamicPageSize"
  [rowClickable]="true"
>
  <ep-datatable-column [columnName]="'name'" [flexGrow]="1.5"
                       [sortable]="true">

    <ng-template epDatatableFilter>
      <cgdis-portal-datatable-text-filter [allowClear]="true"
                                          [datatableService]="permamonitorPaVersionsTableService"
                                          [filterName]="'name'"
                                          [placeholder]="''"></cgdis-portal-datatable-text-filter>
    </ng-template>
    <ng-template epDatatableHeader>
      <span [translate]="'permamonitor.admin.pa.table.name'"> </span>
    </ng-template>
    <ng-template epDatatableCell let-context>
      {{ context.row.name | defaultValue:"-" }}
    </ng-template>
  </ep-datatable-column>

  <ep-datatable-column [columnName]="'description'"
                       [flexGrow]="1.5" [sortable]="true">

    <ng-template epDatatableFilter>
      <cgdis-portal-datatable-text-filter [allowClear]="true"
                                          [datatableService]="permamonitorPaVersionsTableService"
                                          [filterName]="'description'"
                                          [placeholder]="''"></cgdis-portal-datatable-text-filter>
    </ng-template>
    <ng-template epDatatableHeader>
      <span [translate]="'permamonitor.admin.pa.table.description'"> </span>
    </ng-template>
    <ng-template epDatatableCell let-context>
      <span class="description">{{ context.row.description | defaultValue:"-" }}</span>
    </ng-template>
  </ep-datatable-column>

  <ep-datatable-column [columnName]="'startDate'" [flexGrow]="1.5"
                       [sortable]="true">
    <ng-template epDatatableFilter>
      <cgdis-portal-datatable-datepicker-filter
        [datatableService]="permamonitorPaVersionsTableService"
        [filterName]="'startDate'"></cgdis-portal-datatable-datepicker-filter>
    </ng-template>
    <ng-template epDatatableHeader>
      <span [translate]="'permamonitor.admin.pa.table.start-date'"> </span>
    </ng-template>
    <ng-template epDatatableCell let-context>
      {{ getDateString(context.row.startDate) | defaultValue:"-" }}
    </ng-template>
  </ep-datatable-column>

  <ep-datatable-column [columnName]="'endDate'" [flexGrow]="1.5"
                       [sortable]="true">
    <ng-template epDatatableFilter>
      <cgdis-portal-datatable-datepicker-filter
        [datatableService]="permamonitorPaVersionsTableService"
        [filterName]="'endDate'"></cgdis-portal-datatable-datepicker-filter>
    </ng-template>
    <ng-template epDatatableHeader>
      <span [translate]="'permamonitor.admin.pa.table.end-date'"> </span>
    </ng-template>
    <ng-template epDatatableCell let-context>
      {{ getDateString(context.row.endDate) | defaultValue:"-" }}
    </ng-template>
  </ep-datatable-column>

  <ep-datatable-column [columnName]="'actions'" [flexGrow]="1.5"
                       [sortable]="false">

    <ng-template epDatatableHeader>
      <span [translate]="'permamonitor.admin.pa.table.actions'"> </span>
    </ng-template>
    <ng-template [disableClickOnCell]="true" epDatatableCell let-context>
      <cgdis-portal-simple-table-button (onclick)="canAccessConfigs?navigateToSelectedPds(context.row.tecid):null"
      >
        <cgdis-portal-link-with-icon [icon]="'icon-link'" [linkDisabled]="!canAccessConfigs" [rounded]="true"
                                     [smallIcon]="true"></cgdis-portal-link-with-icon>
      </cgdis-portal-simple-table-button>
      <cgdis-portal-simple-table-button (onclick)="canAccessConfigs?navigateToSelectedPoj(context.row.tecid):null"
      >
        <cgdis-portal-link-with-icon [icon]="'icon-gear'" [linkDisabled]="!canAccessConfigs" [rounded]="true"
                                     [smallIcon]="true"></cgdis-portal-link-with-icon>
      </cgdis-portal-simple-table-button>
    </ng-template>
  </ep-datatable-column>


</cgdis-portal-cgdisdatatable>

