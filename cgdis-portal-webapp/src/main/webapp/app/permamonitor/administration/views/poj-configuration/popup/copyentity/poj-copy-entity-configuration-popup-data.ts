import { SimplePopupDataModel } from '@app/common/modules/popup/simple-popup-data.model';
import { PojCopyEntityConfigurationPopupContent } from '@permamonitor/administration/views/poj-configuration/popup/copyentity/poj-copy-entity-configuration-popup.component';

export class PojCopyEntityConfigurationPopupData extends SimplePopupDataModel<PojCopyEntityConfigurationPopupContent> {
  onSubmitSuccess: () => void;

  constructor(args: PojCopyEntityConfigurationPopupData) {
    super(args);
    this.onSubmitSuccess = args.onSubmitSuccess;
  }
}
