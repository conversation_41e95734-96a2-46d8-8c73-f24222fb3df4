import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  OnInit,
  Output,
} from '@angular/core';
import { NgxSelectModule } from 'ngx-select-ex';
import { TranslateModule } from '@ngx-translate/core';
import { PojConfigurationCategorySelectorService } from '@permamonitor/administration/views/poj-configuration/category-selector/poj-configuration-category-selector.service';
import { PermCategoryModel } from '@app/model/permamonitor/perm-category.model';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'cgdis-portal-poj-configuration-category-selector',
  standalone: true,
  imports: [NgxSelectModule, TranslateModule, FormsModule],
  templateUrl: './poj-configuration-category-selector.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [PojConfigurationCategorySelectorService],
})
export class PojConfigurationCategorySelectorComponent implements OnInit {
  @Output() selectedCategory = new EventEmitter<PermCategoryModel>();
  protected categories: PermCategoryModel[];
  protected initialCategory: PermCategoryModel;

  constructor(
    private pojConfigurationCategorySelectorService: PojConfigurationCategorySelectorService,
    private cd: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    this.pojConfigurationCategorySelectorService
      .getAllCategories()
      .subscribe((value) => {
        this.categories = value;
        this.initialCategory = this.categories[0];
        this.cd.markForCheck();
      });
  }

  onCategoryChange(event: any) {
    this.selectedCategory.emit(event[0].data as PermCategoryModel);
  }
}
