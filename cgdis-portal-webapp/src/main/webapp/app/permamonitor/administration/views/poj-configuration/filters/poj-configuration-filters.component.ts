import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { PermamonitorRegionFiltersService } from '@permamonitor/permamonitor/common/filter/filters/region-filter/region-filter.service';
import { ButtonGroupSelectorComponent } from '@app/common/shared/selectors/button-group-selector/button-group-selector/button-group-selector.component';
import { EntityWithParent } from '@app/model/entity-with-parent.model';
import { Entity } from '@app/model/entity.model';
import { RegionFilterOutput } from '@permamonitor/permamonitor/common/filter/filters/region-filter/region-filter.component';
import { forkJoin, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { NgxSelectModule } from 'ngx-select-ex';
import { TranslateModule } from '@ngx-translate/core';
import { PojConfigurationFiltersService } from '@permamonitor/administration/views/poj-configuration/filters/poj-configuration-filters.service';
import { PermCategoryModel } from '@app/model/permamonitor/perm-category.model';
import { ToggleFilterComponent } from '@permamonitor/permamonitor/common/filter/filters/toggle-filter/toggle-filter.component';
import { FormsModule } from '@angular/forms';
import { NgIf } from '@angular/common';
import { SharedModule } from '@app/common/shared/shared.module';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { PojCopyEntityConfigurationPopupComponent } from '@permamonitor/administration/views/poj-configuration/popup/copyentity/poj-copy-entity-configuration-popup.component';
import { PojCopyEntityConfigurationPopupData } from '@permamonitor/administration/views/poj-configuration/popup/copyentity/poj-copy-entity-configuration-popup-data';
import { MatDialog } from '@angular/material/dialog';

export type POJFilterOutput = RegionFilterOutput & {
  cis: Entity;
  gis: boolean;
};

@Component({
  selector: 'cgdis-portal-poj-configuration-filters',
  standalone: true,
  imports: [
    ButtonGroupSelectorComponent,
    NgxSelectModule,
    TranslateModule,
    ToggleFilterComponent,
    FormsModule,
    NgIf,
    SharedModule,
  ],
  templateUrl: './poj-configuration-filters.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [PermamonitorRegionFiltersService, PojConfigurationFiltersService],
})
export class PojConfigurationFiltersComponent
  implements OnInit, OnChanges, OnDestroy
{
  @Input() selectedDeploymentPlanTecid: number;
  @Input() selectedCategory: PermCategoryModel;
  @Output() POJFilterOutput: EventEmitter<POJFilterOutput> = new EventEmitter();
  @Output() CisGisOutput: EventEmitter<Entity[]> = new EventEmitter();
  @Input() isDeploymentPlanEditable: boolean = false;
  @ViewChild('groupSelector') groupSelector!: ButtonGroupSelectorComponent;
  protected groups: EntityWithParent[];
  protected zones: Entity[];
  protected cis: Entity[];
  protected currentPOJFilterOutput: POJFilterOutput = {
    zone: undefined,
    group: undefined,
    cis: undefined,
    gis: false,
  };
  protected canUpdate = false;
  private _unsubscribe$ = new Subject<void>();

  constructor(
    private permamonitorRegionFiltersService: PermamonitorRegionFiltersService,
    private pojConfigurationFiltersService: PojConfigurationFiltersService,
    private cd: ChangeDetectorRef,
    private connectedUserService: ConnectedUserService,
    private dialog: MatDialog,
  ) {}

  get filteredGroups(): EntityWithParent[] {
    if (this.groups && this.currentPOJFilterOutput.zone) {
      return this.groups.filter(
        (group) =>
          group.parent.tecid === this.currentPOJFilterOutput.zone.tecid,
      );
    }
    return [];
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (this.isInputsReady()) {
      this.initializeCisFilter();
    }
  }

  selectZone($event: Entity) {
    if ($event != undefined) {
      this.currentPOJFilterOutput.zone = $event;
      this.currentPOJFilterOutput.cis = undefined;
      this.fetchCis(
        this.currentPOJFilterOutput.zone.tecid,
        this.currentPOJFilterOutput.gis,
      );
      if (this.currentPOJFilterOutput.group) {
        this.resetGroupSelection();
      } else {
        this.emitPOJFilterOutput();
      }
    }
  }

  emitPOJFilterOutput() {
    this.POJFilterOutput.emit(this.currentPOJFilterOutput);
    this.computeCanUpdate();
  }

  selectGroup($event: EntityWithParent) {
    this.currentPOJFilterOutput.group = $event;
    this.currentPOJFilterOutput.cis = undefined;
    if (this.currentPOJFilterOutput.group !== undefined) {
      this.fetchCis(
        this.currentPOJFilterOutput.group.tecid,
        this.currentPOJFilterOutput.gis,
      );
    }
    this.emitPOJFilterOutput();
  }

  selectGis($event: boolean) {
    this.currentPOJFilterOutput.gis = $event;
    if (
      this.currentPOJFilterOutput.zone !== undefined ||
      this.currentPOJFilterOutput.group !== undefined
    ) {
      this.fetchCis(
        this.currentPOJFilterOutput.group?.tecid ||
          this.currentPOJFilterOutput.zone?.tecid,
        this.currentPOJFilterOutput.gis,
      );
    }
    this.emitPOJFilterOutput();
  }

  resetGroupSelection() {
    this.groupSelector.selectValue(undefined);
  }

  ngOnInit(): void {
    this.initRegionFilter();
  }

  initRegionFilterOutput(zone: Entity, group: EntityWithParent) {
    this.currentPOJFilterOutput.group = group;
    this.currentPOJFilterOutput.zone = zone;

    this.emitPOJFilterOutput();
  }

  initRegionFilter() {
    forkJoin({
      zones: this.permamonitorRegionFiltersService.getAllZoneNames(),
      groups: this.permamonitorRegionFiltersService.getAllGroupsWithParent(),
    })
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe({
        next: ({ zones, groups }) => {
          this.zones = zones;
          this.groups = groups;
          this.initRegionFilterOutput(this.zones[0], undefined);

          if (this.isInputsReady()) {
            this.initializeCisFilter();
          }

          this.cd.markForCheck();
        },
        error: (error) => {},
      });
  }

  initializeCisFilter() {
    this.pojConfigurationFiltersService
      .getAllCis(
        this.selectedDeploymentPlanTecid,
        this.selectedCategory?.name,
        [this.zones[0].tecid],
        false,
      )
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((value) => {
        this.cis = value;
        this.CisGisOutput.emit(value);
        this.cd.markForCheck();
      });
  }

  fetchCis(entityTecId: number, withGis: boolean) {
    this.pojConfigurationFiltersService
      .getAllCis(
        this.selectedDeploymentPlanTecid,
        this.selectedCategory?.name,
        [entityTecId],
        withGis,
      )
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((value) => {
        this.cis = value;
        this.CisGisOutput.emit(value);
        this.cd.markForCheck();
      });
  }

  onCisSelectionChange($event: any) {
    if ($event.length) {
      if ($event[0].data?.tecid != this.currentPOJFilterOutput.cis?.tecid) {
        this.currentPOJFilterOutput.cis = $event[0].data;
        this.emitPOJFilterOutput();
      }
    } else {
      if (this.currentPOJFilterOutput.cis != undefined) {
        this.currentPOJFilterOutput.cis = undefined;
        this.emitPOJFilterOutput();
      }
    }
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }

  isInputsReady(): boolean {
    return (
      !!this.selectedDeploymentPlanTecid &&
      !!this.selectedCategory &&
      !!this.zones
    );
  }

  private computeCanUpdate() {
    this.canUpdate =
      this.connectedUserService.hasAnyRoles([
        'ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_POJ',
      ]) &&
      this.currentPOJFilterOutput &&
      this.currentPOJFilterOutput.cis != undefined;
  }

  openDuplicatePopup() {
    this.dialog.open(PojCopyEntityConfigurationPopupComponent, {
      data: new PojCopyEntityConfigurationPopupData({
        onSubmitSuccess: () => {},

        content: {
          deploymentPlanTecid: this.selectedDeploymentPlanTecid,
          categoryName: this.selectedCategory.name,
          fromEntity: this.currentPOJFilterOutput.cis,
          baseEntities: this.zones,
        },
      }),
      width: '500px',
      panelClass: 'simple-popup',
    });
  }
}
