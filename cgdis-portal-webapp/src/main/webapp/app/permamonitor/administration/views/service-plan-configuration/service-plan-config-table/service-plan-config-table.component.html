@if (isDeploymentPlanEditable !== undefined && dynamicPageSize !== undefined && dynamicPageSize !== 0) {
  <cgdis-portal-cgdisdatatable
    [datatableService]="pdsConfigTableService"
    [pageSize]="dynamicPageSize"
    [sorts]="[{dir:'asc',prop:'servicePlan'}]"

    [useSelectRow]="true"
  >
    <ep-datatable-column [columnName]="'servicePlan'" [flexGrow]="1.5"
                         [sortable]="true">

      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-text-filter [allowClear]="true"
                                            [datatableService]="pdsConfigTableService"
                                            [filterName]="'servicePlanName'"
                                            [placeholder]="''"></cgdis-portal-datatable-text-filter>
      </ng-template>
      <ng-template epDatatableHeader>
        <span [translate]="'permamonitor.admin.pds.table.pds'"> </span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{ context.value.portalLabel | defaultValue:"-" }}
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column [columnName]="'permCategory'" [flexGrow]="1.5" [sortable]="true">
      <ng-template epDatatableFilter>

        <cgdis-portal-datatable-select-filter [allowClear]="true" [datatableService]="pdsConfigTableService"
                                              [filterName]="'permCategory.name'"
                                              [flexGrow]="3"
                                              [possibleValues]="entityTypesFilterValues"></cgdis-portal-datatable-select-filter>
      </ng-template>
      <ng-template epDatatableHeader>
        <span [translate]="'permamonitor.admin.pds.table.category'"> </span>

      </ng-template>
      <ng-template epDatatableCell let-context>
        <div class="category-selector">
          @if (canUpdate && isDeploymentPlanEditable) {
            <ngx-select
              (select)="selectCategory(context.rowIndex,context.row, $event)"
              [(ngModel)]="context.value.tecid"
              [allowClear]="true"
              (remove)="deleteCategory(context.rowIndex, context.row)"
              [items]="categories"
              [optionTextField]="'name'"
              [optionValueField]="'tecid'"
              id="categorySelect"
            >
            </ngx-select>
          } @else {
            {{ context.value.name }}
          }
        </div>
      </ng-template>
    </ep-datatable-column>


    <ep-datatable-column [columnName]="'subcategoryTecid'" [flexGrow]="1.5" [sortable]="true">
      <ng-template epDatatableFilter>

        <cgdis-portal-datatable-select-filter [allowClear]="true" [datatableService]="pdsConfigTableService"
                                              [filterName]="'subcategoryTecid'"
                                              [flexGrow]="3"
                                              [possibleValues]="allSubcategories"></cgdis-portal-datatable-select-filter>
      </ng-template>
      <ng-template epDatatableHeader>
        <span [translate]="'permamonitor.admin.pds.table.subcategory'"> </span>

      </ng-template>
      <ng-template epDatatableCell let-context>
        <div class="category-selector">

          @if (subcategoriesByCategory.get(castRow(context.row).permCategoryTecid)?.length) {
            <ngx-select
              (select)="selectSubcategory(context.rowIndex,context.row, $event)"
              [(ngModel)]="context.value"
              [disabled]="!(canUpdate && isDeploymentPlanEditable)"
              [allowClear]="true" (remove)="deleteSubcategory(context.rowIndex, context.row)"
              [items]="subcategoriesByCategory.get(castRow(context.row).permCategoryTecid)"
              [optionTextField]="'name'"
              [optionValueField]="'tecid'"
              id="subcategorySelect"
            >
            </ngx-select>
          } @else {
            -
          }
        </div>
      </ng-template>
    </ep-datatable-column>


    <ep-datatable-column [columnName]="'bookmarked'" [flexGrow]="1.5" [sortable]="true">
      <ng-template epDatatableHeader>
        <span [translate]="'permamonitor.admin.pds.table.favorite'"></span>
      </ng-template>
      <ng-template epDatatableFilter>

        <cgdis-portal-datatable-select-filter [possibleValues]="bookmarkedFilterValues"
                                              [filterName]="'bookmarked'"
                                              [flexGrow]="1"

                                              [datatableService]="pdsConfigTableService"
                                              [allowClear]="true"></cgdis-portal-datatable-select-filter>
      </ng-template>

      <ng-template epDatatableCell let-context>
        @if (canUpdate && isDeploymentPlanEditable) {
          <input
            (click)="toggleFavoritesCheckbox(context.rowIndex, context.row, context.value)"
            [checked]="context.value"
            type="checkbox">
          <label (click)="toggleFavoritesCheckbox(context.rowIndex, context.row, context.value)"
                 class="checkboxLabel"></label>
        } @else {
          <input
            [checked]="context.value"
            type="checkbox">
          <label
            class="checkboxLabel"></label>
        }
      </ng-template>
    </ep-datatable-column>


  </cgdis-portal-cgdisdatatable>
}

<cgdis-portal-datatable-text-filter [datatableService]="pdsConfigTableService"
                                    [filterConfig]="servicePlanWithoutCategoryFilter"
                                    [filterName]="'servicePlanWithoutCategory'"
                                    [hidden]="true"></cgdis-portal-datatable-text-filter>
