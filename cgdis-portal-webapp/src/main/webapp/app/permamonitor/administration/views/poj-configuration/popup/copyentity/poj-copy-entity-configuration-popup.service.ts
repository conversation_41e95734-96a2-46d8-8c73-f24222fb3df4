import { Injectable } from '@angular/core';
import { DefaultFormService } from '@app/common/modules/form-module/service/default-form.service';
import { ToastService } from '@app/common/shared/toasts/CGDISToastService';
import { FormErrorService } from '@app/common/modules/form-module/service/form-error.service';
import { Observable } from 'rxjs';
import { FormError } from '@app/common/modules/error-management/model/form-error.model';
import { PojCopyEntityConfigurationForm } from '@permamonitor/administration/views/poj-configuration/popup/copyentity/poj-copy-entity-configuration-form';
import { RestService } from '@eportal/core';
import { PermPojConfigModel } from '@app/model/permamonitor/poj/perm-poj-config.model';

@Injectable()
export class PojCopyEntityConfigurationPopupService extends DefaultFormService<
  PojCopyEntityConfigurationForm,
  PermPojConfigModel[]
> {
  constructor(
    toasterService: ToastService,
    formErrorService: FormErrorService,
    private restService: RestService,
  ) {
    super(
      toasterService,
      'permamonitor.admin.poj.popup.copy.entity.success',
      formErrorService,
    );
  }

  submit(
    parameter: PojCopyEntityConfigurationForm,
  ): Observable<PermPojConfigModel[]> {
    return this.restService
      .one(
        'permamonitor',
        'admin',
        'criticity',
        parameter.deploymentTecid.toString(),
        parameter.categoryName,
        parameter.fromEntityTecid.toString(),
        'copy',
        parameter.toEntityTecid.toString(),
      )
      .post({});
  }

  submitError(formError: FormError): void {}

  submitSuccess(result: PermPojConfigModel[]): void {}
}
