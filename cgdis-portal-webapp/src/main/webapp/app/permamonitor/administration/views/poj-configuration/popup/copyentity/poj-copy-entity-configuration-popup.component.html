<cgdis-portal-popup-template
  (onClose)="closePopup()" [data]="data"
>
  <ng-container popupTitle>
    {{ 'permamonitor.admin.poj.popup.copy.entity.title'| translate:{ entity: data.content.fromEntity.name } }}
  </ng-container>
  <!--  <cgdis-portal-spinner [loading]="waiting" popupContent>-->
  <!--  </cgdis-portal-spinner>-->
  <ng-container popupContent>

    <cgdis-portal-default-form-template>
      <cgdis-portal-form (onCancel)="closePopup()" [formClasses]="['row']"
                         [formId]="'permamonitor-poj-copy-entity-configuration-id'"

      >
        <div class="form-item mb-5">
          <label [translate]="'permamonitor.admin.poj.popup.copy.entity.warning'"
                 style="cursor: default !important"></label>
        </div>
        <cgdis-portal-input-field
          [name]="'fromEntityTecid'"
          [initialValue]="data.content.fromEntity.tecid"
          [visible]="false"
          [disableIfNotVisible]="false"
        ></cgdis-portal-input-field>

        <cgdis-portal-input-field
          [name]="'categoryName'"
          [initialValue]="data.content.categoryName"
          [visible]="false"
          [disableIfNotVisible]="false"
        ></cgdis-portal-input-field>

        <cgdis-portal-input-field
          [name]="'deploymentTecid'"
          [initialValue]="data.content.deploymentPlanTecid"
          [visible]="false"
          [disableIfNotVisible]="false"
        ></cgdis-portal-input-field>

        <cgdis-portal-select-field
          [possibleValues]="entities"
          [fieldRequired]="true"
          [name]="'toEntityTecid'"
          [labelKey]="'permamonitor.admin.poj.popup.copy.entity.toentity'"

        ></cgdis-portal-select-field>

      </cgdis-portal-form>

    </cgdis-portal-default-form-template>
  </ng-container>
</cgdis-portal-popup-template>
