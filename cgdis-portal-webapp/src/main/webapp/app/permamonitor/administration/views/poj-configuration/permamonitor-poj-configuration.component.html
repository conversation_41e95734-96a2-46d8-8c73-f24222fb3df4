<cgdis-portal-page-template>
  <!-- Header -->
  <cgdis-portal-page-header [returnRoute]="{commands: ['permamonitor/admin']}" [subtitleAlign]="true"
                            [titleKey]="'permamonitor.title-poj-config'">
    <div page-header-subtitle>
      <cgdis-portal-poj-configuration-category-selector
        (selectedCategory)="setSelectedCategory($event)"></cgdis-portal-poj-configuration-category-selector>

    </div>
  </cgdis-portal-page-header>
  <cgdis-portal-poj-configuration-filters (CisGisOutput)="setCisGis($event)"
                                          (POJFilterOutput)="setPOJFilter($event)"
                                          [isDeploymentPlanEditable]="isDeploymentPlanEditable"
                                          [selectedCategory]="selectedCategory"
                                          [selectedDeploymentPlanTecid]="deploymentPlanTecid"></cgdis-portal-poj-configuration-filters>
  <cgdis-portal-poj-configuration-table [cisgis]="cisgis"
                                        [filter]="selectedFilter"
                                        [isDeploymentPlanEditable]="isDeploymentPlanEditable"
                                        [selectedCategory]="selectedCategory"
                                        [selectedDeploymentPlanTecid]="deploymentPlanTecid"></cgdis-portal-poj-configuration-table>

</cgdis-portal-page-template>
