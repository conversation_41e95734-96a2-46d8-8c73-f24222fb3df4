<div class="poj-filters">
  <div class="zone-filter filter-group">
    <span [translate]="'permamonitor.admin.poj.filters.zones-title'" class="label"></span>
    <cgdis-portal-button-group-selector
      (selectedValueOutput)="selectZone($event)"
      [initIndex]="0"
      [invertColors]="true"
      [labelKey]="'name'"
      [values]="zones"></cgdis-portal-button-group-selector>
  </div>

  <div class="group-filter filter-group">
    <span [translate]="'permamonitor.admin.poj.filters.groups-title'" class="label"></span>
    <cgdis-portal-button-group-selector
      #groupSelector
      (selectedValueOutput)="selectGroup($event)"
      [invertColors]="true"
      [labelKey]="'name'"
      [values]="filteredGroups"
    ></cgdis-portal-button-group-selector>
  </div>

  <div class="cis-filter filter-group">
    <span [translate]="'permamonitor.admin.poj.filters.gis-title'" class="label"></span>
    <cgdis-portal-permamonitor-toggle-filter
      (toggleFilterOutput)="selectGis($event)"
    >
    </cgdis-portal-permamonitor-toggle-filter>
  </div>

  <div class="cis-filter filter-group">
    <span [translate]="'permamonitor.admin.poj.filters.cis-title'" class="label"></span>
    <ngx-select
      (selectionChanges)="onCisSelectionChange($event)"
      [allowClear]="true"
      [items]="cis"
      [optionTextField]="'name'" [optionValueField]="'tecid'"

    ></ngx-select>
  </div>

  <div class="copy-filter filter-group">
    <span class="label">&nbsp;</span>
    <div class="align-content-center" style="height:100%">
      <cgdis-portal-link-with-icon *ngIf="isDeploymentPlanEditable && canUpdate" (click)="openDuplicatePopup()"
                                   [icon]="'icon-duplicat'"
                                   [rounded]="true" [smallIcon]="true"></cgdis-portal-link-with-icon>
    </div>

  </div>


</div>
