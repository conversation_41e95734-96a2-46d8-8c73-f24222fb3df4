import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  On<PERSON><PERSON>roy,
  OnInit,
  Output,
} from '@angular/core';
import { Subject } from 'rxjs';
import { NgxSelectModule } from 'ngx-select-ex';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { DeploymentPlanSelectorService } from '@permamonitor/administration/views/service-plan-configuration/deployment-plan-selector/deployment-plan-selector.service';
import { DeploymentPlanModel } from '@app/model/permamonitor/service-plan-category/deployment-plan.model';
import { takeUntil } from 'rxjs/operators';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { ServicePlanConfigTableService } from '@permamonitor/administration/views/service-plan-configuration/service-plan-config-table/service-plan-config-table.service';
import _ from 'lodash';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import { $e } from 'codelyzer/angular/styles/chars';

@Component({
  selector: 'cgdis-portal-pds-version-selector',
  standalone: true,
  imports: [NgxSelectModule, TranslateModule, FormsModule, DatatableModule],
  templateUrl: './deployment-plan-selector.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [DeploymentPlanSelectorService],
})
export class DeploymentPlanSelectorComponent implements OnInit, OnDestroy {
  @Output() deploymentPlanVersionTecidOutput = new EventEmitter<number>();
  protected deploymentPlans!: DeploymentPlanModel[];
  protected entityTypesFilterValues: FieldGroupOption<number, any>[];
  protected readonly $e = $e;
  private _unsubscribe$ = new Subject<void>();

  constructor(
    private cd: ChangeDetectorRef,
    private deploymentPlanSelectorService: DeploymentPlanSelectorService,
    // Service defined on parent component
    protected pdsConfigTableService: ServicePlanConfigTableService,
  ) {}

  ngOnInit() {
    this.initCategories();
  }

  initCategories() {
    this.deploymentPlanSelectorService
      .getAllPdsVersions()
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((value) => {
        this.deploymentPlans = value;
        this.entityTypesFilterValues = _.map(
          value,
          (oneType) =>
            new FieldGroupOption({
              value: oneType.tecid,
              I18NLabel: oneType.name,
            }),
        );
        this.cd.markForCheck();
      });
  }

  onValueChange($event: number) {
    this.deploymentPlanVersionTecidOutput.emit($event);
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }
}
