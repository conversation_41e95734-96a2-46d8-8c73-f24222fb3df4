import { RestService } from '@eportal/core';
import { Category } from '@permamonitor/permamonitor/common/constants';
import { Injectable } from '@angular/core';
import { PermPojConfigModel } from '@app/model/permamonitor/poj/perm-poj-config.model';
import { PermPojUpdateConfigModel } from '@app/model/permamonitor/poj/perm-poj-update-config.model';
import { Observable } from 'rxjs';
import { PermConfigDpceCriticityAdminDto } from '@app/model/permamonitor/poj/perm-config-dpce-criticity-admin-dto.model';

@Injectable()
export class PojConfigurationTableService {
  constructor(private restService: RestService) {}

  fetchData(
    deploymentPlanTecid: number,
    categoryName: Category,
    cisTecid: number,
  ) {
    return this.restService
      .all<
        PermConfigDpceCriticityAdminDto[]
      >('permamonitor', 'admin', 'criticity', deploymentPlanTecid.toString(), categoryName, cisTecid.toString())
      .get();
  }

  updateData(
    deploymentPlanTecid: number,
    categoryName: Category,
    cisTecid: number,
    form: PermPojUpdateConfigModel,
  ): Observable<PermConfigDpceCriticityAdminDto> {
    return this.restService
      .one<PermPojConfigModel>(
        'permamonitor',
        'admin',
        'criticity',
        deploymentPlanTecid.toString(),
        categoryName,
        cisTecid.toString(),
      )
      .put(form);
  }

  createConfiguration(
    deploymentPlanTecid: number,
    categoryName: Category,
    cisTecid: number,
  ) {
    return this.restService
      .one<PermPojConfigModel>(
        'permamonitor',
        'admin',
        'criticity',
        deploymentPlanTecid.toString(),
        categoryName,
        cisTecid.toString(),
      )
      .post();
  }
}
