import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { PermCategoryModel } from '@app/model/permamonitor/perm-category.model';
import { SharedModule } from '@app/common/shared/shared.module';
import { ENTITY_FORM_KEYS } from '@app/admin/entity/shared/detail/entity-forms.model';
import { FormModule } from '@app/common/modules/form-module/form.module';
import { POJFilterOutput } from '@permamonitor/administration/views/poj-configuration/filters/poj-configuration-filters.component';
import { MatDialog } from '@angular/material/dialog';
import { PojDuplicatePopupComponent } from '@app/common/modules/popup/permamonitor/poj-duplicate-popup/poj-duplicate-popup.component';
import { FormsModule } from '@angular/forms';
import { PojConfigurationTableService } from '@permamonitor/administration/views/poj-configuration/table/poj-configuration-table.service';
import { PermPojConfigModel } from '@app/model/permamonitor/poj/perm-poj-config.model';
import { PermPojUpdateConfigModel } from '@app/model/permamonitor/poj/perm-poj-update-config.model';
import { ToastService } from '@app/common/shared/toasts/CGDISToastService';
import { of, Subject } from 'rxjs';
import { debounceTime, map, takeUntil } from 'rxjs/operators';
import { PojDuplicatePopupData } from '@app/common/modules/popup/permamonitor/poj-duplicate-popup/poj-duplicate-popup-data';
import { ConnectedUserService } from '@app/security/connected-user.service';
import { Entity } from '@app/model/entity.model';
import { SimpleYesNoPopupComponent } from '@app/common/modules/popup/yes-no/simple-yes-no-popup.component';
import { SimpleYesNoPopupData } from '@app/common/modules/popup/yes-no/simple-yes-no-popup-data';
import { PermConfigDpceCriticityAdminDto } from '@app/model/permamonitor/poj/perm-config-dpce-criticity-admin-dto.model';

export type PojValueType = 'crit' | 'inac' | 'opt';

@Component({
  selector: 'cgdis-portal-poj-configuration-table',
  standalone: true,
  imports: [TranslateModule, SharedModule, FormModule, FormsModule],
  templateUrl: './poj-configuration-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [PojConfigurationTableService],
})
export class PojConfigurationTableComponent
  implements OnChanges, OnDestroy, OnInit
{
  @Input() selectedCategory: PermCategoryModel;
  @Input() filter: POJFilterOutput;
  @Input() cisgis: Entity[];
  @Input() selectedDeploymentPlanTecid: number;
  @Input() isDeploymentPlanEditable: boolean = false;
  protected data: PermConfigDpceCriticityAdminDto[][];
  protected loading: boolean = false;
  protected selectionIndexes: {
    hours: number;
    day: number;
    type: PojValueType;
  } = null;
  protected days: string[] = [
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday',
    'saturday',
    'sunday',
    'holidays',
  ];
  protected times: string[] = Array.from(
    { length: 24 },
    (_, i) => `${i.toString().padStart(2, '0')}:00`,
  );
  protected readonly keys = ENTITY_FORM_KEYS;

  protected canUpdate = false;
  private _unsubscribe$ = new Subject<void>();
  private pojValueChange$ = new Subject<PermPojConfigModel>();
  private originalValue: PermPojConfigModel = null;
  private hasChanged: Boolean = false;
  private fetchDataIndex = 0;

  constructor(
    private connectedUserService: ConnectedUserService,
    private cd: ChangeDetectorRef,
    private dialog: MatDialog,
    private pojConfigurationTableService: PojConfigurationTableService,
    private toastr: ToastService,
    private translateService: TranslateService,
  ) {}

  ngOnInit(): void {
    this.pojValueChange$
      .pipe(debounceTime(500), takeUntil(this._unsubscribe$))
      .subscribe((data) => this.sendPojUpdateRequest(data));

    this.connectedUserService
      .hasAnyRolesObservable(['ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_POJ'])
      .subscribe((value) => {
        this.computeCanUpdate();
        this.cd.markForCheck();
      });
  }

  onFocus(
    data: PermPojConfigModel,
    dayIndex: number,
    pojValueType: PojValueType,
  ) {
    this.selectionIndexes = {
      day: dayIndex,
      hours: data.startHour,
      type: pojValueType,
    };

    this.hasChanged = false;
    this.originalValue = data;
    this.cd.markForCheck();
  }

  onValueChange() {
    this.hasChanged = true;
  }

  sendPojUpdateRequest(
    data: PermPojConfigModel,
    currentIndex?: { currentDayIndex: number; currentHourIndex: number },
  ) {
    if (this.hasChanged) {
      const form = new PermPojUpdateConfigModel({
        tecid: data.tecid,
        teclock: data.teclock,
        opt: data.opt ? data.opt : 0,
        crit: data.crit ? data.crit : 0,
        inac: data.inac ? data.inac : 0,
      });
      this.loading = true;
      this.cd.markForCheck();
      this.pojConfigurationTableService
        .updateData(
          this.selectedDeploymentPlanTecid,
          this.selectedCategory.name,
          this.getEntitiyFromFilter().tecid,
          form,
        )
        .pipe(takeUntil(this._unsubscribe$))
        .subscribe({
          next: (value) => {
            const dayData = this.data[currentIndex.currentDayIndex];
            const valueOverriden =
              form.opt < value.opt ||
              form.crit < value.crit ||
              form.inac < value.inac;
            dayData[currentIndex.currentHourIndex] = value;
            this.cd.markForCheck();
            this.toastr.success(
              this.translateService.instant(
                valueOverriden
                  ? this.filter.group == undefined
                    ? 'permamonitor.admin.poj.zone-overriden-update-success-toast'
                    : 'permamonitor.admin.poj.group-overriden-update-success-toast'
                  : 'permamonitor.admin.poj.update-success-toast',
              ),
            );
          },
          error: (err) => {
            this.loading = false;
            this.cd.markForCheck();
          },
          complete: () => {
            this.loading = false;
            this.cd.markForCheck();
          },
        });
    }
  }

  onBlur(
    currentDayIndex: number,
    currentHourIndex: number,
    data: PermPojConfigModel,
  ) {
    if (data.opt !== undefined && data.opt !== null) {
      this.sendPojUpdateRequest(data, { currentDayIndex, currentHourIndex });
    }
    this.resetSelection();
  }

  onKeydown(
    event: KeyboardEvent,
    inputElement: HTMLInputElement,
    currentValue: number | null,
    maxValue: number = 99999,
  ) {
    const allowedKeys = [
      'Backspace',
      'Delete',
      'Tab',
      'Enter',
      'ArrowLeft',
      'ArrowRight',
      'ArrowUp',
      'ArrowDown',
      'Escape',
    ];

    const isNumberKey = event.key >= '0' && event.key <= '9';
    if (!allowedKeys.includes(event.key) && !isNumberKey) {
      event.preventDefault();
    }

    if (event.key === 'Enter' || event.key === 'Escape') {
      this.resetSelection();
      inputElement.blur();
      return;
    }

    if (isNumberKey) {
      const isFullySelected =
        inputElement.selectionStart === 0 &&
        inputElement.selectionEnd === inputElement.value.length;

      let newValue;

      if (isFullySelected) {
        newValue = event.key;
      } else {
        newValue = (currentValue ?? '').toString() + event.key;
      }

      const compareValue = parseInt(newValue, 10);
      if (compareValue > maxValue) {
        event.preventDefault();
        this.toastr.warning(
          this.translateService.instant(
            'permamonitor.admin.poj.maxvalue-success-toast',
            { maxValue },
          ),
        );
      }
    }
  }

  resetSelection() {
    this.selectionIndexes = null;
  }

  fetchData() {
    if (
      this.filter &&
      // this.filter.cis &&
      this.selectedCategory &&
      this.selectedDeploymentPlanTecid
    ) {
      this.loading = true;
      let entityToLoad = this.getEntitiyFromFilter();

      if (entityToLoad != undefined) {
        const currentFetchDateIndex = ++this.fetchDataIndex;
        this.pojConfigurationTableService
          .fetchData(
            this.selectedDeploymentPlanTecid,
            this.selectedCategory.name,
            entityToLoad.tecid,
          )
          .pipe(takeUntil(this._unsubscribe$))
          .subscribe((value) => {
            if (this.fetchDataIndex !== currentFetchDateIndex) {
              return;
            }
            this.data = value;
            this.loading = false;
            this.cd.markForCheck();
            if (this.filter.cis && this.data.length == 0) {
              this.openPopupToCreateConfiguration();
            }
          });
      }
    }
  }

  private getEntitiyFromFilter() {
    let entityToLoad = this.filter.cis || this.filter.group || this.filter.zone;
    return entityToLoad;
  }

  private openPopupToCreateConfiguration() {
    let canEditConfiguration = this.connectedUserService.hasAnyRoles([
      'ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_POJ',
    ]);
    this.dialog.open(SimpleYesNoPopupComponent, {
      data: new SimpleYesNoPopupData({
        onYes: () => {
          if (canEditConfiguration) {
            return this.pojConfigurationTableService
              .createConfiguration(
                this.selectedDeploymentPlanTecid,
                this.selectedCategory.name,
                this.getEntitiyFromFilter().tecid,
              )
              .pipe(
                takeUntil(this._unsubscribe$),
                map(() => {
                  this.fetchData();
                  return null;
                }),
              );
          }
          return of(false);
        },
        title: canEditConfiguration
          ? 'permamonitor.admin.poj.popup.creation.title'
          : 'permamonitor.admin.poj.popup.creation.titlenoeditright',
        messageHtml: true,
        message: canEditConfiguration
          ? 'permamonitor.admin.poj.popup.creation.message'
          : 'permamonitor.admin.poj.popup.creation.messagenoeditright',
        labelButtonYes: canEditConfiguration
          ? 'permamonitor.admin.poj.popup.creation.yes'
          : 'permamonitor.admin.poj.popup.creation.yesnoeditright',
        labelButtonNo: 'permamonitor.admin.poj.popup.creation.no',
      }),
      width: '500px',
      panelClass: 'simple-popup',
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.selectedCategory && this.selectedCategory) {
      this.fetchData();
      this.cd.markForCheck();
    }
    if (changes.filter && this.filter) {
      this.computeCanUpdate();
      this.fetchData();
      this.cd.detectChanges();
    }
    if (
      changes.selectedDeploymentPlanTecid &&
      this.selectedDeploymentPlanTecid
    ) {
      this.fetchData();
      this.cd.detectChanges();
    }
  }

  openDuplicatePopup() {
    this.dialog.open(PojDuplicatePopupComponent, {
      data: new PojDuplicatePopupData({
        onSubmitSuccess: () => this.fetchData(),

        content: {
          deploymentPlanTecid: this.selectedDeploymentPlanTecid,
          categoryName: this.selectedCategory.name,
          entityTecid: this.getEntitiyFromFilter().tecid,
        },
      }),
      width: '500px',
      panelClass: 'simple-popup',
    });
  }

  getDayTranslationKey(day: string) {
    return 'permamonitor.admin.poj.days' + day;
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }

  generateTable() {}

  private computeCanUpdate() {
    this.canUpdate = this.connectedUserService.hasAnyRoles([
      'ROLE_PERMISSION_PERMAMONITOR_ADMIN_UPDATE_POJ',
    ]);
  }
}
