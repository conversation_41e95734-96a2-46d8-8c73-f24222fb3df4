<cgdis-portal-page-template>
  <!-- Header -->
  <cgdis-portal-page-header [returnRoute]="{commands: ['permamonitor/admin']}" [subtitleAlign]="true"
                            [titleKey]="'permamonitor.title-pds-config'">
    <div page-header-subtitle>

      <!-- Category selector -->
      <cgdis-portal-pds-version-selector (deploymentPlanVersionTecidOutput)="onPdsVersionChange($event)"
      ></cgdis-portal-pds-version-selector>

      <cgdis-portal-entity-selector></cgdis-portal-entity-selector>
    </div>
  </cgdis-portal-page-header>
  <cgdis-portal-pds-config-table [deploymentPlanTecid]="deploymentPlanTecid"></cgdis-portal-pds-config-table>

</cgdis-portal-page-template>
