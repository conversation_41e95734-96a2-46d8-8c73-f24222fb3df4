import { NgModule } from '@angular/core';
import { PermamonitorComponent } from './permamonitor.component';
import { SharedModule } from '@app/common/shared/shared.module';
import { RouterModule } from '@angular/router';
import { PERMAMONITOR_ROUTE } from '@permamonitor/permamonitor.route';
import { NgxSelectModule } from 'ngx-select-ex';

@NgModule({
  declarations: [PermamonitorComponent],
  imports: [
    SharedModule,
    RouterModule.forChild(PERMAMONITOR_ROUTE),
    NgxSelectModule,
  ],
})
export class PermamonitorModule {}
