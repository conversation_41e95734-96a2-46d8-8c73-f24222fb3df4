import { Routes } from '@angular/router';
import { PermamonitorComponent } from '@permamonitor/permamonitor.component';
import { RoleGuard } from '@app/security/guards/role.guard';

export const PERMAMONITOR_ROUTE: Routes = [
  {
    path: '',
    component: PermamonitorComponent,
    canActivate: [RoleGuard],
    canActivateChild: [RoleGuard],
    data: {
      expectedRoles: [
        'ROLE_PERMISSION_PERMAMONITOR',
        'ROLE_PERMISSION_PERMAMONITOR_ADMIN_VIEW',
      ],
    },
    children: [
      {
        path: '',
        loadChildren: () =>
          import('./permamonitor/views/main/permamonitor-main.routing').then(
            (m) => m.PERMAMONITOR_MAIN_ROUTE,
          ),
        canActivate: [RoleGuard],
        canActivateChild: [RoleGuard],
        data: {
          expectedRoles: ['ROLE_PERMISSION_PERMAMONITOR'],
        },
      },
      {
        path: 'admin',
        loadChildren: () =>
          import(
            '@permamonitor/administration/views/main/permamonitor-admin-main.routing'
          ).then((m) => m.PERMAMONITOR_ADMIN_MAIN_ROUTE),
        canActivate: [RoleGuard],
        canActivateChild: [RoleGuard],
        data: {
          expectedRoles: ['ROLE_PERMISSION_PERMAMONITOR_ADMIN_VIEW'],
        },
      },
    ],
  },
];
