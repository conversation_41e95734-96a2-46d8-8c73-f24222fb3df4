import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnChanges,
  SimpleChanges,
} from '@angular/core';
import { NgClass } from '@angular/common';
import { SharedModule } from '@app/common/shared/shared.module';

@Component({
  selector: 'cgdis-portal-poj-view-table-value',
  templateUrl: './poj-view-table-value.component.html',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgClass, SharedModule],
})
export class PojViewTableValueComponent implements OnChanges {
  @Input() value: number;
  @Input() decimal: boolean = false;

  @Input() limits: {
    pojOptimal: number;
    pojCritical: number;
    pojUnacceptable: number;
  };

  class: string;

  ngOnChanges(changes: SimpleChanges): void {
    this.computeClass();
  }

  private computeClass(): void {
    if (this.value != undefined && this.limits != undefined) {
      if (this.value < this.limits.pojUnacceptable) {
        this.class = 'poj-view-table-value poj-view-table-value-unacceptable';
      } else if (this.value < this.limits.pojCritical) {
        this.class = 'poj-view-table-value poj-view-table-value-critical';
      } else if (this.value < this.limits.pojOptimal) {
        this.class = 'poj-view-table-value poj-view-table-value-minimal';
      } else {
        this.class = 'poj-view-table-value poj-view-table-value-optimal';
      }
    } else {
      this.class = 'poj-view-table-value poj-view-table-value-unacceptable';
    }
  }
}
