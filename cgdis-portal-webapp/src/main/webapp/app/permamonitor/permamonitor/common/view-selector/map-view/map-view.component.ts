import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { HighchartsChartModule } from 'highcharts-angular';
import HC_map from 'highcharts/modules/map';
import groups from '@permamonitor/permamonitor/common/view-selector/map-view/constants/geojson/groupements.json';
import zones from '@permamonitor/permamonitor/common/view-selector/map-view/constants/geojson/zones.json';

import Highcharts from 'highcharts';
import { ScrollModule } from '@app/common/modules/scroll/scroll.module';
import { TranslateModule } from '@ngx-translate/core';
import { MapViewInfoBoxComponent } from '@permamonitor/permamonitor/common/view-selector/map-view/map-view-info-box/map-view-info-box.component';
import { InfoBoxBase } from '@permamonitor/permamonitor/common/info-box/info-box-base';
import { SharedModule } from '@app/common/shared/shared.module';
import { LegendModule } from '@app/common/modules/legend/legend.module';
import { MapViewService } from '@permamonitor/permamonitor/common/view-selector/map-view/map-view.service';
import { PermMapViewPopupInputData } from '@permamonitor/permamonitor/common/view-selector/map-view/models/PermMapViewPopupInputData';
import { MatDialog } from '@angular/material/dialog';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { MapViewMobilePopupComponent } from '@app/common/modules/popup/permamonitor/map-view-mobile-popup/map-view-mobile-popup.component';
import { PermOneEntityScheduleModel } from '@app/model/permamonitor/schedule/perm-entity-schedule.model';
import { PermPojStatusModel } from '@app/model/permamonitor/schedule/perm-poj-status.model';
import { LegendItem } from '@app/common/modules/legend/legend-item';

HC_map(Highcharts);

@Component({
  selector: 'cgdis-portal-map-view',
  standalone: true,
  imports: [
    HighchartsChartModule,
    ScrollModule,
    TranslateModule,
    MapViewInfoBoxComponent,
    SharedModule,
    LegendModule,
  ],
  providers: [MapViewService],
  templateUrl: './map-view.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MapViewComponent
  extends InfoBoxBase
  implements OnInit, OnChanges, OnDestroy
{
  @Input() data: any;
  @Input() filterInput: any;
  Highcharts: typeof Highcharts = Highcharts;
  chartConstructor = 'mapChart';
  chartOptions: any;
  isChartVisible = true;
  zoneGeo = zones;
  groupGeo = groups;
  isDrilledDown: boolean = false;
  isTablet: boolean = false;
  zoneData: PermOneEntityScheduleModel[];
  protected legendItems: LegendItem[];
  private _unsubscribe$ = new Subject<void>();
  firstInitialized = false;

  constructor(
    protected cd: ChangeDetectorRef,
    private mapViewService: MapViewService,
    private dialog: MatDialog,
    private breakpointObserver: BreakpointObserver,
  ) {
    super(cd);
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }

  pointToObject(point: any) {
    return {
      id: point.properties.ID,
      name: point.entityName,
      periodEvolution: this.filterInput.periodEvolution,
      category: this.filterInput.category,
      tecid: point.entityTecid,
      isFavorite: this.filterInput.isFavorite,
    };
  }

  async ngOnInit() {
    this.setLegendItems();
    this.initBreakpointObserverer();
    this.initChart(this.zoneGeo, this.zoneData);
  }

  setLegendItems() {
    this.legendItems = [
      new LegendItem({
        id: '1',
        labelKey: 'permamonitor.view.map.statuses.ideal',
        classes: ['-ideal'],
      }),
      // new LegendItem({
      //   id: '2',
      //   labelKey: 'permamonitor.view.map.statuses.optimal',
      //   classes: ['-optimal'],
      // }),
      new LegendItem({
        id: '2',
        labelKey: 'permamonitor.view.map.statuses.critical',
        classes: ['-critical'],
      }),
      new LegendItem({
        id: '3',
        labelKey: 'permamonitor.view.map.statuses.unacceptable',
        classes: ['-unacceptable'],
      }),
    ];
    this.cd.markForCheck();
  }

  initBreakpointObserverer() {
    this.breakpointObserver
      .observe(['(max-width: 1200px)'])
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((result: BreakpointState) => {
        this.isTablet = result.matches;
        this.cd.markForCheck();
      });
  }

  fetchMapData(entityType: 'ZONE' | 'GROUP', geoMap: any) {
    // console.log(this.filterInput);
    this.mapViewService
      .getMapData({
        category: this.filterInput.category,
        entityType: entityType,
        dateTime: this.filterInput.periodEvolution,
      })
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((value) => {
        if (entityType === 'ZONE') {
          this.zoneData = value.rows;
        }
        this.redrawChart(geoMap, value.rows);
      });
  }

  openMapViewMobilePopup(data: any): void {
    let dialogData: PermMapViewPopupInputData = new PermMapViewPopupInputData();
    dialogData.category = this.filterInput.category;
    dialogData.baseEntityTecid = data.tecid;
    dialogData.dateTime = data.periodEvolution;
    dialogData.isFavorite = data.isFavorite;
    dialogData.name = data.name;
    this.dialog.open(MapViewMobilePopupComponent, {
      data: dialogData,
      width: '500px',
      panelClass: 'simple-popup-mobile',
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.filterInput && this.filterInput) {
      const typeLocation = this.filterInput['typeLocation'];
      if (typeLocation === 'Zones') {
        this.fetchMapData('ZONE', this.zoneGeo);
      } else {
        this.fetchMapData('GROUP', this.groupGeo);
      }
    }
  }

  setInfobox(point: any) {
    this.infobox = this.pointToObject(point);

    this.cd.detectChanges();
  }

  redrawChart(mapData: any, data: any) {
    // Trigger fade-out
    this.isChartVisible = false;
    this.cd.markForCheck();
    // Wait for fade-out to complete before updating chartOptions
    setTimeout(() => {
      this.chartOptions = null; // Clear existing chart options
      this.cd.markForCheck();

      // Highcharts bug fix with 0ms timeout
      setTimeout(() => {
        this.generateChartOptions(mapData, data);

        // Trigger fade-in
        this.isChartVisible = true;
        this.cd.markForCheck();
      }, 0);
    }, 250); // Match the fade-out duration
  }

  initChart(mapData: any, data: PermOneEntityScheduleModel[]) {
    setTimeout(() => {
      if (data) {
        this.generateChartOptions(mapData, data);
      }
      this.cd.markForCheck();
    }, 0);
  }

  generateChartOptions(mapData: any, data: PermOneEntityScheduleModel[]) {
    const componentInstance = this;

    const statusColors: Record<PermPojStatusModel, string> = {
      [PermPojStatusModel.OPTIMAL]: 'rgba(0,119,0,0.3)',
      [PermPojStatusModel.CRITICAL]: 'rgba(255,115,0,0.3)',
      [PermPojStatusModel.MINIMAL]: 'rgba(255,115,0,0.3)',
      [PermPojStatusModel.UNACCEPTABLE]: 'rgba(255,0,0,0.3)',
    };
    // Map data to include color based on status

    const formattedData = data.map((item: any) => {
      const baseColor =
        statusColors[item.status as PermPojStatusModel] || 'rgba(255,0,0,0.3)';

      return {
        ...item,
        color: baseColor,
        hoverColor: Highcharts.color(baseColor).brighten(-0.2).get(), // Darker shade on hover
        lineWidth: 30,
      };
    });
    this.chartOptions = {
      title: {
        text: null,
      },

      credits: {
        enabled: false,
      },
      legend: {
        enabled: false,
      },
      chart: {
        map: mapData,
        backgroundColor: null,
        height: 800,
      },
      responsive: {
        rules: [
          {
            condition: {
              minWidth: 1021,
            },
            chartOptions: {
              chart: {
                height: 800,
              },
            },
          },
          {
            condition: {
              maxWidth: 1020,
            },
            chartOptions: {
              chart: {
                height: 600,
              },
            },
          },
          {
            condition: {
              maxWidth: 600,
            },
            chartOptions: {
              chart: {
                height: 500,
              },
            },
          },
        ],
      },
      plotOptions: {
        map: {
          color: 'rgb(44, 62, 123)',

          states: {
            hover: {
              opacity: 1,
              // color: '#596dff',
              // borderColor: '#333333',
              // borderWidth: 4,
            },
          },
        },
        series: {
          states: {
            inactive: {
              opacity: 1,
            },
          },
          point: {
            events: {
              mouseOver: function () {
                componentInstance.setInfobox(this);
              },
              mouseOut: function () {
                componentInstance.resetInfobox();
              },
              click: function () {
                // Trigger drilldown on click
                const zoneID = this.entityTecid;
                if (!this.zoneID) {
                  componentInstance.drilldownToGroups(zoneID, this);
                }
              },
            },
          },
        },
      },
      tooltip: {
        enabled: false,
      },
      series: [
        {
          type: 'map',
          data: formattedData,
          joinBy: 'geoJsonId',
          name: 'Zones',
          dataLabels: {
            enabled: false,
          },
        },
      ],
    };
    this.firstInitialized = true;
    this.cd.markForCheck();
  }

  drilldownToGroups(zoneID: number, point: any) {
    if (!this.isTablet) {
      if (!this.isDrilledDown && this.filterInput.typeLocation === 'Zones') {
        this.mapViewService
          .getMapData({
            category: this.filterInput.category,
            entityType: 'GROUP',
            dateTime: this.filterInput.periodEvolution,
            zoneId: zoneID,
          })
          .pipe(takeUntil(this._unsubscribe$))
          .subscribe((value) => {
            this.isDrilledDown = true;
            this.redrawChart(this.groupGeo, value.rows);
          });
      }
    } else {
      this.openMapViewMobilePopup(this.pointToObject(point));
    }
  }

  drillupToZones() {
    this.redrawChart(this.zoneGeo, this.zoneData);
    this.isDrilledDown = false;
  }
}
