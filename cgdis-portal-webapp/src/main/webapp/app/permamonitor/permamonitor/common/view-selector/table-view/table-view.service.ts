import { Injectable } from '@angular/core';
import {
  DateModel,
  DateService,
  DatetimeService,
  RestService,
} from '@eportal/core';
import { PermScheduleModel } from '@app/model/permamonitor/schedule/perm-schedule.model';
import { Observable } from 'rxjs';
import { Category } from '@permamonitor/permamonitor/common/constants';
import { PermScheduleRowDetailModel } from '@app/model/permamonitor/schedule/perm-schedule-row-detail.model';
import { PermScheduleFilterModel } from '@app/model/permamonitor/schedule/perm-schedule-filter.model';

@Injectable()
export class TableViewService {
  constructor(
    private _restService: RestService,
    private _dateService: DateService,
    private _dateTimeService: DatetimeService,
  ) {}

  // http://localhost:9000/api/permamonitor/schedules/SAP/2023-12-18?startHour=17&nbHours=24
  // http://localhost:9100/api/permamonitor/schedules/SAP/2023-12-18?startHour=18&nbHours=2&baseEntityTecid=168
  getAllSlots(
    requestInput: PermScheduleFilterModel,
  ): Observable<PermScheduleModel> {
    const params: Record<string, any> = {};
    if (requestInput.baseEntityTecid !== undefined) {
      params.baseEntityTecid = requestInput.baseEntityTecid;
    }

    if (
      this._dateService.equals(
        requestInput.startDateTime,
        this._dateService.now(),
      )
    ) {
      const today = this._dateTimeService.now();
      params.startHour = today.hours;
      params.nbHours = 24;
    }

    if (requestInput.isFavorite) {
      params.favorite = requestInput.isFavorite;
    }

    if (
      requestInput.subcategory !== undefined &&
      requestInput.subcategory.tecid !== undefined
    ) {
      params.subcategory = requestInput.subcategory.tecid;
    }

    const formatedDate = this._dateService.format(
      requestInput.startDateTime,
      'YYYY-MM-DD',
    );
    return this._restService
      .one<PermScheduleModel>(
        'permamonitor',
        'schedules',
        requestInput.category,
        formatedDate,
      )
      .get(params);
  }

  // http://localhost:9000/api/permamonitor/schedules/SAP/2023-12-18/3808473
  getSlotDetails(category: Category, date: DateModel, slotId: string) {
    const formatedDate = this._dateService.format(date, 'YYYY-MM-DD');
    return this._restService
      .one<PermScheduleRowDetailModel>(
        'permamonitor',
        'schedules',
        category,
        formatedDate,
        slotId,
      )
      .get();
  }
}
