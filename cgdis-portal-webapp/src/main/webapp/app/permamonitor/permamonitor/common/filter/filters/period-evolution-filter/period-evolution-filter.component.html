<cgdis-portal-filter-template [filterGroupClass]="'period-evolution'">
  <span filterHeader>{{ "permamonitor.filter.label.period" | translate }}</span>
  <ng-container filterContent>
    <div class="date-picker">
      <input (keyup.enter)="onEnter($event)" (onDateSelected)="changePeriod($event)"
             [configuration]="configuration"
             [defaultValue]="currentDateInput"
             [selectedDate]="getDateModel()"
             cgdisPortalDatepicker
             class="-with-datepicker"
             type="text" /></div>

    <div class="evolution">
      <!--      <label-->
      <!--        [translate]="'permamonitor.filter.label.evolution'"></label>-->
      <cgdis-portal-default-button (click)="addDays(-1)"
                                   [buttonClasses]="['-compact']">J-1
      </cgdis-portal-default-button>
      @if (!hideButtons) {


        @if (isPlay) {
          <cgdis-portal-default-button class="play-button" (click)="togglePlay()" [icon]="'icon-pause'"
                                       [buttonClasses]="['-compact']"></cgdis-portal-default-button>
        } @else {
          <cgdis-portal-default-button class="play-button" (click)="togglePlay()" [icon]="'icon-play'"
                                       [buttonClasses]="['-compact']"></cgdis-portal-default-button>
        }
      }
      <cgdis-portal-default-button (click)="addDays(1)"
                                   [buttonClasses]="['-compact']">J+1
      </cgdis-portal-default-button>
    </div>

    <div class="slider">
      <cgdis-portal-slider (change)="onTimeSlotChanged($event)" (sliding)="onSlide($event)" [disabled]="isPlay"
                           [firstTickLabel]="'00h00'"
                           [hideIntermediateTicks]="false"
                           [lastTickLabel]="'23h00'"
                           [showOnlyFirstLastTickValues]="true"
                           [sliderConfig]="sliderConfig" [sliderPointsFormatter]="sliderPointsFormatter"
                           [values]="sliderCurrentTime"
      ></cgdis-portal-slider>
      <div class="current-time">{{ getCurrentSliderTimeInHoursAndMinutes() }}</div>
    </div>
  </ng-container>
</cgdis-portal-filter-template>
