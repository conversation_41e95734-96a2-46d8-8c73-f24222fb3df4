<div>
  <div class="table-view-body-content">
    <div class="information">

      <cgdis-portal-table-body-info-box
        [requestData]="infobox"></cgdis-portal-table-body-info-box>
      <cgdis-portal-legend [items]="legendItems"></cgdis-portal-legend>

    </div>
    <div class="chart-wrapper-table">
      @if (!isLoading) {
        @if (schedules) {
          <div class="sticky-x-axis">
            @if (xAxisOptions) {
              <highcharts-chart
                [Highcharts]="HighchartsStickyAxis"
                [options]="xAxisOptions"
                [runOutsideAngular]="true"
                style="width: 99%; display: block;"
              ></highcharts-chart>
            }
          </div>
          <cgdis-portal-scroll [visibility]="'visible'"
                               [scrollbarClasses]="{'cgdis-scroll__cgdis-portal-permamonitor-table-body':true}">
            @if (chartOptions) {
              <highcharts-chart
                [Highcharts]="Highcharts"
                [options]="chartOptions"
                [runOutsideAngular]="true"
                style="width: 99%; display: block;"
              ></highcharts-chart>
            }
          </cgdis-portal-scroll>
        }
      } @else {
        <cgdis-portal-spinner [loading]="isLoading"></cgdis-portal-spinner>
      }
    </div>
  </div>
</div>
