import { Injectable } from '@angular/core';
import {
  DateService,
  DatetimeModel,
  DatetimeService,
  RestService,
} from '@eportal/core';
import { Category } from '@permamonitor/permamonitor/common/constants';
import { PermEntityScheduleModel } from '@app/model/permamonitor/schedule/perm-entity-schedule.model';
import { PermScheduleWithCounterAndCriticityModel } from '@app/model/permamonitor/counters/perm-schedule-with-counter-and-criticity.model';

@Injectable()
export class MapViewService {
  constructor(
    private _restService: RestService,
    private _dateService: DateService,
    private _dateTimeService: DatetimeService,
  ) {}

  // http://localhost:9000/api/permamonitor/schedules/SAP/2023-12-18/3/counters
  getMapDetails(requestInput: {
    category: Category;
    dateTime: DatetimeModel;
    baseEntityTecid: number;
    isFavorite: boolean;
  }) {
    const params: Record<string, any> = {};

    if (requestInput.baseEntityTecid !== undefined) {
      params.baseEntityTecid = requestInput.baseEntityTecid;
    }

    if (requestInput.isFavorite) {
      params.favorite = requestInput.isFavorite;
    }
    return this._restService
      .one<PermScheduleWithCounterAndCriticityModel>(
        'permamonitor',
        'schedules',
        requestInput.category,
        this._dateTimeService.format(requestInput.dateTime, 'YYYY-MM-DD'),
        this._dateTimeService.format(requestInput.dateTime, 'HH'),
        'counters',
      )
      .get(params);
  }

  // http://localhost:9000/api/permamonitor/schedules/SAP/2023-12-18/3/counters
  getMapData(requestInput: {
    category: Category;
    dateTime: DatetimeModel;
    entityType: 'ZONE' | 'GROUP';
    zoneId?: number;
  }) {
    const params: Record<string, any> = {};

    if (requestInput.zoneId) {
      params.zoneId = requestInput.zoneId;
    }

    if (requestInput.dateTime) {
      params.startHour = this._dateTimeService.format(
        requestInput.dateTime,
        'HH',
      );
    }

    return this._restService
      .one<PermEntityScheduleModel>(
        'permamonitor',
        'schedules',
        requestInput.category,
        this._dateTimeService.format(requestInput.dateTime, 'YYYY-MM-DD'),
        'entities',
        requestInput.entityType,
      )
      .get(params);
  }
}
