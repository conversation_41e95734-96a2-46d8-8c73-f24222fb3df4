<div class="filters">
  @for (filter of filters; track filter) {
    @switch (filter) {
      @case ('typeIncsa') {
        <cgdis-portal-permamonitor-type-filter
          [translationKey]="'permamonitor.filter.label.type'"
          (typeFilterOutput)="setIsSauvetageFilterOutput($event)"
          [typesInput]="incsaTypes"
          [labelKey]="'name'"
        ></cgdis-portal-permamonitor-type-filter>
      }
      @case ('isFavorite') {
        <cgdis-portal-permamonitor-is-primary-filter
          (isPrimaryFilterOutput)="setIsPrimaryFilterOutput($event)"
        ></cgdis-portal-permamonitor-is-primary-filter>
      }
      @case ('typeAutres') {
        <cgdis-portal-permamonitor-type-filter
          [translationKey]="'permamonitor.filter.label.type'"
          (typeFilterOutput)="setTypeGisComFilterOutput($event)"
          [typesInput]="autresTypes"
          [labelKey]="'name'"
        ></cgdis-portal-permamonitor-type-filter>
      }
      @case ('typeLocation') {
        <cgdis-portal-permamonitor-type-filter
          [translationKey]="'permamonitor.filter.label.localisation'"
          (typeFilterOutput)="setTypeRegionFilterOutput($event)"
          [typesInput]="['Zones', 'Groupements']"
        ></cgdis-portal-permamonitor-type-filter>
      }
      @case ('region') {
        <cgdis-portal-permamonitor-region-filter
          (regionFilterOutput)="setRegionFilterOutput($event)"

        ></cgdis-portal-permamonitor-region-filter>
      }
      @case ('period') {
        <cgdis-portal-permamonitor-period-filter
          (periodFilterOutput)="setPeriodFilterOutput($event)"></cgdis-portal-permamonitor-period-filter>
      }
      @case ('periodWithButtons') {
        <cgdis-portal-permamonitor-period-filter
          (periodFilterOutput)="setPeriodWithButtonsFilterOutput($event)"
          [showButtons]="true"></cgdis-portal-permamonitor-period-filter>
      }
      @case ('periodEvolution') {
        <cgdis-portal-permamonitor-period-evaluation-filter
          (periodFilterOutput)="setPeriodEvolutionFilterOutput($event)"></cgdis-portal-permamonitor-period-evaluation-filter>
      }
      @case ('periodEvolutionNoButtons') {
        <cgdis-portal-permamonitor-period-evaluation-filter
          (periodFilterOutput)="setPeriodEvolutionNoButtonsFilterOutput($event)"
          [hideButtons]="true"></cgdis-portal-permamonitor-period-evaluation-filter>
      }
    }
  }
</div>
