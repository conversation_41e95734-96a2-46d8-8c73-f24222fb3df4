import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  SimpleChanges,
} from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { JsonPipe } from '@angular/common';
import { SharedModule } from '@app/common/shared/shared.module';
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { TableViewService } from '@permamonitor/permamonitor/common/view-selector/table-view/table-view.service';
import { ScheduleRow } from '@app/operational/service-plan/schedule/schedule-row.model';
import { DatetimeService } from '@eportal/core';
import { Subject, Subscription } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'cgdis-portal-table-body-info-box',
  standalone: true,
  imports: [TranslateModule, JsonPipe, SharedModule, MatProgressSpinner],
  templateUrl: './table-view-info-box.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TableBodyInfoBoxComponent implements OnChanges, OnDestroy {
  @Input() requestData: any;
  slotDetails: ScheduleRow;
  slotPositions: any[];
  backupGroupName: string;
  protected isLoading: boolean = false;
  private fetchTimeout: NodeJS.Timeout;
  private dataSubscription: Subscription | null = null;
  private _unsubscribe$ = new Subject<void>();

  constructor(
    private cd: ChangeDetectorRef,
    private tableViewService: TableViewService,
    private dateTimeService: DatetimeService,
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.requestData) {
      const newRequestData = changes.requestData.currentValue;

      // Cancel the ongoing request if requestData becomes an empty object
      if (!newRequestData || Object.keys(newRequestData).length === 0) {
        this.cancelRequest();
      } else {
        this.getSlotData(newRequestData);
      }
    }
  }

  getStartDateTimeString() {
    return this.dateTimeService.format(this.requestData.startDateTime, 'HH:mm');
  }

  getEndDateTimeString() {
    return this.dateTimeService.format(this.requestData.endDateTime, 'HH:mm');
  }

  getPositionInfo(positionTecId: number) {
    const filteredUser = this.slotDetails?.prestations?.filter(
      (user) => user.position?.tecid === positionTecId,
    );
    if (filteredUser?.length) {
      return (
        filteredUser[0].person.cgdisRegistrationNumber +
        ' ' +
        filteredUser[0].person.lastName.toUpperCase() +
        ' ' +
        filteredUser[0].person.firstName
      );
    } else {
      return '-';
    }
  }

  getSlotData(requestData: any) {
    if (requestData && requestData.slotId) {
      this.isLoading = true;

      const { category, startDateTime, slotId } = requestData;

      if (this.fetchTimeout) {
        clearTimeout(this.fetchTimeout);
      }

      this.fetchTimeout = setTimeout(() => {
        this.dataSubscription = this.tableViewService
          .getSlotDetails(category, startDateTime, slotId)
          .pipe(takeUntil(this._unsubscribe$))
          .subscribe({
            next: (value) => {
              this.slotPositions = value.positions;
              this.slotDetails = value.details;
              this.backupGroupName = value.backupGroupName;
              this.isLoading = false;
              this.cd.detectChanges();
            },
            error: (err) => {
              console.error('Error fetching slot data:', err);
              this.isLoading = false;
              this.cd.detectChanges();
            },
          });
      }, 300);
    }
  }

  cancelRequest() {
    this.isLoading = false;

    if (this.fetchTimeout) {
      clearTimeout(this.fetchTimeout);
    }

    if (this.dataSubscription) {
      this.dataSubscription.unsubscribe();
      this.dataSubscription = null;
    }

    this.slotDetails = null;
    this.slotPositions = [];
    this.cd.detectChanges();
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }
}
