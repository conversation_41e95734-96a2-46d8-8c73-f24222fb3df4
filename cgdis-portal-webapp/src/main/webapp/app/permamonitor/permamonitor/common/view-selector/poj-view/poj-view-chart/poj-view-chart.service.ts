import { Injectable } from '@angular/core';
import { DatetimeModel, DatetimeService, RestService } from '@eportal/core';
import { PermPojScheduleChartModel } from '@app/model/permamonitor/poj/perm-poj-schedule-chart.model';
import { PermCategoryModel } from '@app/model/permamonitor/perm-category.model';
import { Category } from '@permamonitor/permamonitor/common/constants';

@Injectable()
export class PojViewChartService {
  constructor(
    private restService: RestService,
    private dateTimeService: DatetimeService,
  ) {}

  getNext24Hours(
    dateTime: DatetimeModel,
    category: Category,
    baseEntityTecid?: number,
    subcategory?: PermCategoryModel,
  ) {
    const startDate = dateTime;
    const endDate = this.dateTimeService.add(dateTime, 1, 'day');
    const params: Record<string, any> = {};
    if (baseEntityTecid !== undefined) {
      params.baseEntityTecid = baseEntityTecid;
    }

    if (subcategory !== undefined && subcategory.tecid !== undefined) {
      params.subcategory = subcategory.tecid;
    }
    return this.restService
      .all<PermPojScheduleChartModel>(
        'permamonitor',
        'schedules',
        category,
        'poj',
        this.dateTimeService.format(startDate, 'YYYY-MM-DD'),
        this.dateTimeService.format(endDate, 'YYYY-MM-DD'),
      )
      .get(params);
  }

  getLast7Days(
    dateTime: DatetimeModel,
    category: Category,
    baseEntityTecid?: number,
    subcategory?: PermCategoryModel,
  ) {
    const startDate = dateTime;
    const endDate = this.dateTimeService.add(dateTime, -8, 'day');
    const params: Record<string, any> = {};
    if (baseEntityTecid !== undefined) {
      params.baseEntityTecid = baseEntityTecid;
    }
    if (subcategory !== undefined && subcategory.tecid !== undefined) {
      params.subcategory = subcategory.tecid;
    }
    return this.restService
      .all<PermPojScheduleChartModel>(
        'permamonitor',
        'schedules',
        category,
        'poj',
        this.dateTimeService.format(endDate, 'YYYY-MM-DD'),
        this.dateTimeService.format(startDate, 'YYYY-MM-DD'),
      )
      .get(params);
  }

  getNext7Days(
    dateTime: DatetimeModel,
    category: Category,
    baseEntityTecid?: number,
    subcategory?: PermCategoryModel,
  ) {
    const startDate = dateTime;
    const endDate = this.dateTimeService.add(dateTime, 8, 'day');
    const params: Record<string, any> = {};
    if (baseEntityTecid !== undefined) {
      params.baseEntityTecid = baseEntityTecid;
    }
    if (subcategory !== undefined && subcategory.tecid !== undefined) {
      params.subcategory = subcategory.tecid;
    }
    return this.restService
      .all<PermPojScheduleChartModel>(
        'permamonitor',
        'schedules',
        category,
        'poj',
        this.dateTimeService.format(startDate, 'YYYY-MM-DD'),
        this.dateTimeService.format(endDate, 'YYYY-MM-DD'),
      )
      .get(params);
  }

  getLast30Days(
    dateTime: DatetimeModel,
    category: Category,
    baseEntityTecid?: number,
    subcategory?: PermCategoryModel,
  ) {
    const startDate = dateTime;
    const endDate = this.dateTimeService.add(dateTime, -30, 'day');
    const params: Record<string, any> = {};
    if (baseEntityTecid !== undefined) {
      params.baseEntityTecid = baseEntityTecid;
    }
    if (subcategory !== undefined && subcategory.tecid !== undefined) {
      params.subcategory = subcategory.tecid;
    }
    return this.restService
      .all<PermPojScheduleChartModel>(
        'permamonitor',
        'schedules',
        category,
        'poj',
        this.dateTimeService.format(endDate, 'YYYY-MM-DD'),
        this.dateTimeService.format(startDate, 'YYYY-MM-DD'),
      )
      .get(params);
  }
}
