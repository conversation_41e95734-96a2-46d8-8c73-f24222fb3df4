import { ChangeDetectorRef } from '@angular/core';

export interface InfoboxTable {
  name?: string;
  start_time?: string;
  end_time?: string;
}

export abstract class InfoBoxBase {
  hoverLoading: boolean = false;
  infobox: InfoboxTable = {};
  protected hoverTimeout: NodeJS.Timeout;

  protected constructor(protected cd: ChangeDetectorRef) {}

  abstract pointToObject(point: any): any;

  setInfobox(point: any) {
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout);
    }
    this.hoverLoading = true;
    this.hoverTimeout = setTimeout(() => {
      this.infobox = this.pointToObject(point);
      this.hoverLoading = false;
      this.cd.detectChanges();
    }, 500);
    this.cd.detectChanges();
  }

  resetInfobox() {
    if (this.hoverTimeout) {
      clearTimeout(this.hoverTimeout); // Clear timeout when mouse leaves
    }
    this.hoverLoading = false;
    this.infobox = {};
    this.cd.detectChanges();
  }
}
