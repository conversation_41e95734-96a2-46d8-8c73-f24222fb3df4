<!--<p>poj-view-table works!</p>-->
@if (filterInput) {
  <cgdis-portal-cgdisdatatable
    [datatableService]="pojViewTableService"
    [enableFullscreenLoading]="true"
    [pageSize]="13"
    [hideResultsCount]="true"
    [startSearch]="false"

  >
    <ep-datatable-column [columnName]="'date'" [flexGrow]="1.5" [minWidth]="140"
                         [sortable]="false">
      <ng-template epDatatableHeader>
        <span [translate]="'permamonitor.view.poj.table.date'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{ getDateString(context.value) | defaultValue:"-" }}
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'selectedHourReady'" [flexGrow]="1.5" [sortable]="false">
      <ng-template epDatatableHeader>
        <span [translate]="'permamonitor.view.poj.table.armed'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <cgdis-portal-poj-view-table-value [value]="context.value"
                                           [limits]="context.row"></cgdis-portal-poj-view-table-value>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'avgReady'" [flexGrow]="1.5" [sortable]="false">
      <ng-template epDatatableHeader>
        <span [translate]="'permamonitor.view.poj.table.armedAverage'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <cgdis-portal-poj-view-table-value [decimal]="true" [value]="context.value"
                                           [limits]="context.row"></cgdis-portal-poj-view-table-value>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'percentAvg'" [flexGrow]="1.5"
                         [sortable]="false">
      <ng-template epDatatableHeader>
        <span [translate]="'permamonitor.view.poj.table.pojPercent'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{ getPercent(context.row.avgReady, context.row.pojOptimal) }}
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'minReady'" [flexGrow]="1.5" [sortable]="false">
      <ng-template epDatatableHeader>
        <span [translate]="'permamonitor.view.poj.table.armedMin'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <cgdis-portal-poj-view-table-value [value]="context.value"
                                           [limits]="context.row"></cgdis-portal-poj-view-table-value>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'percentMin'" [flexGrow]="1.5"
                         [sortable]="false">
      <ng-template epDatatableHeader>
        <span [translate]="'permamonitor.view.poj.table.pojPercent'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{ getPercent(context.row.minReady, context.row.pojOptimal) }}
        <!--        {{ context.value | defaultValue:"-" }}-->
      </ng-template>
    </ep-datatable-column>

  </cgdis-portal-cgdisdatatable>

}
