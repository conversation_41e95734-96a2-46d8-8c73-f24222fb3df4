<div>
  <div class="map-view-content">
    <div class="information">
      <cgdis-portal-map-view-info-box
        [requestData]="infobox"
      ></cgdis-portal-map-view-info-box>

    </div>
    <div class="chart-wrapper">
      @if (isDrilledDown) {
        <cgdis-portal-button-secondary class="drillup-button" [compact]="true" (click)="drillupToZones()"
        >{{ 'permamonitor.view.map.back-to-zones-button' | translate }}
        </cgdis-portal-button-secondary>
      }
      <cgdis-portal-legend [className]="'map-legend'" [items]="legendItems"></cgdis-portal-legend>
      <cgdis-portal-spinner [loading]="!firstInitialized"></cgdis-portal-spinner>
      <highcharts-chart
        *ngIf="chartOptions "
        [Highcharts]="Highcharts"
        [constructorType]="chartConstructor"
        [ngClass]="{ 'fade-out': !isChartVisible, 'fade-in': isChartVisible }"
        [options]="chartOptions"
        class="fade"

        style="width: 100%; display: block;"
      >
      </highcharts-chart>


    </div>
  </div>
</div>
