import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { NgClass } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { MapViewService } from '@permamonitor/permamonitor/common/view-selector/map-view/map-view.service';
import { Subject, Subscription } from 'rxjs';
import { DatetimeModel, DatetimeService } from '@eportal/core';
import { CompletionDayStatus } from '@app/model/completion-day-status.enum';
import { takeUntil } from 'rxjs/operators';
import { PermScheduleWithCounterModel } from '@app/model/permamonitor/counters/perm-schedule-with-counter.model';
import { PermScheduleWithCounterCriticityModel } from '@app/model/permamonitor/counters/perm-schedule-with-counter-criticity.model';
import { SharedModule } from '@app/common/shared/shared.module';
import { LegendItem } from '@app/common/modules/legend/legend-item';
import { LegendModule } from '@app/common/modules/legend/legend.module';

@Component({
  selector: 'cgdis-portal-map-view-info-box',
  standalone: true,
  imports: [TranslateModule, NgClass, SharedModule, LegendModule],
  templateUrl: './map-view-info-box.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class MapViewInfoBoxComponent implements OnChanges, OnDestroy, OnInit {
  @Input() requestData: any;
  @Input() hoverLoading: boolean;

  protected isLoading: boolean = false;
  protected regionData: PermScheduleWithCounterModel = undefined;
  protected criticityConfig: PermScheduleWithCounterCriticityModel = undefined;
  protected legendItems: LegendItem[];
  private fetchTimeout: NodeJS.Timeout;
  private dataSubscription: Subscription | null = null;
  private _unsubscribe$ = new Subject<void>();

  constructor(
    private cd: ChangeDetectorRef,
    private mapViewService: MapViewService,
    private dateTimeService: DatetimeService,
  ) {}

  ngOnInit(): void {
    this.setLegendItems();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.requestData) {
      const newRequestData = changes.requestData.currentValue;

      // Cancel the ongoing request if requestData becomes an empty object
      if (!newRequestData || Object.keys(newRequestData).length === 0) {
        this.cancelRequest();
      } else {
        this.getRegionData(newRequestData);
      }
    }
  }

  setLegendItems() {
    this.legendItems = [
      new LegendItem({
        id: '1',
        labelKey: 'service_plan.legend.status.complete',
        classes: ['-complete'],
      }),
      new LegendItem({
        id: '2',
        labelKey: 'service_plan.legend.status.partial',
        classes: ['-partial'],
      }),
      new LegendItem({
        id: '3',
        labelKey: 'service_plan.legend.status.degraded',
        classes: ['-degraded'],
      }),
      new LegendItem({
        id: '4',
        labelKey: 'service_plan.legend.status.incomplete',
        classes: ['-incomplete'],
      }),
      new LegendItem({
        id: '6',
        labelKey: 'service_plan.legend.status.empty',
        classes: ['-empty'],
      }),
    ];
    this.cd.markForCheck();
  }

  getDateTimeString(dateTime: DatetimeModel) {
    return this.dateTimeService.format(dateTime, 'HH:mm');
  }

  getStatusColor(status: CompletionDayStatus) {
    const occupationColorClasses: Record<CompletionDayStatus, string> = {
      COMPLETE: '-complete',
      INCOMPLETE: '-incomplete',
      DEGRADED: '-degraded',
      PARTIAL: '-partial',
      EMPTY: '-empty',
      UNKNOWN: '-unknown',
    };

    return occupationColorClasses[status ? status : 'EMPTY'];
  }

  getCompletionStatusNb(
    completionStatus: Record<CompletionDayStatus, number>,
  ): {
    all: number;
    fullfilled: number;
  } {
    const sum = Object.values(completionStatus).reduce(
      (acc, value) => acc + value,
      0,
    );

    const keysToSum: CompletionDayStatus[] = [
      CompletionDayStatus.COMPLETE,
      CompletionDayStatus.PARTIAL,
      CompletionDayStatus.DEGRADED,
    ];

    const sumFulfilled = keysToSum.reduce(
      (acc, key) => acc + (completionStatus[key] || 0),
      0,
    );

    return { all: sum, fullfilled: sumFulfilled };
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }

  getRegionData(requestData: any) {
    if (requestData && requestData.tecid) {
      this.isLoading = true;

      if (this.fetchTimeout) {
        clearTimeout(this.fetchTimeout);
      }

      this.fetchTimeout = setTimeout(() => {
        this.dataSubscription = this.mapViewService
          .getMapDetails({
            category: requestData.category,
            dateTime: requestData.periodEvolution,
            baseEntityTecid: requestData.tecid,
            isFavorite: requestData.isFavorite,
          })
          .pipe(takeUntil(this._unsubscribe$))
          .subscribe({
            next: (value) => {
              this.regionData = value.schedule;
              this.criticityConfig = value.criticityConfig;
              this.isLoading = false;
              this.cd.detectChanges();
            },
            error: (err) => {
              this.isLoading = false;
              this.cd.detectChanges();
            },
          });
      }, 300);
    }
  }

  cancelRequest() {
    this.isLoading = false;

    if (this.fetchTimeout) {
      clearTimeout(this.fetchTimeout);
    }

    if (this.dataSubscription) {
      this.dataSubscription.unsubscribe();
      this.dataSubscription = null;
    }

    this.regionData = null;
    this.cd.detectChanges();
  }
}
