import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { JsonPipe, NgClass } from '@angular/common';
import {
  MatTab,
  MatTabGroup,
  MatTabNavPanel,
  MatTabsModule,
} from '@angular/material/tabs';
import { PermamonitorFiltersComponent } from '@permamonitor/permamonitor/common/filter/filters.component';
import { Category, View } from '@permamonitor/permamonitor/common/constants';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import {
  ActivatedRoute,
  NavigationEnd,
  Router,
  RouterEvent,
  Scroll,
} from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  standalone: true,
  selector: 'cgdis-portal-permamonitor-view-selector',
  templateUrl: './view-selector.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgClass,
    MatTabGroup,
    MatTab,
    PermamonitorFiltersComponent,
    JsonPipe,
    MatTabsModule,
  ],
})
export class PermamonitorViewSelectorComponent implements OnInit, OnDestroy {
  @Input() tabPanel: MatTabNavPanel;
  @Input() views: View[];

  @Output() viewSelectedOutput: EventEmitter<View> = new EventEmitter<View>();
  viewSelected: View;
  isMobile: boolean;

  // FILTERS
  protected filterOutput: any;
  private _unsubscribe$ = new Subject<void>();

  constructor(
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
    private router: Router,
    private route: ActivatedRoute,
  ) {
    this.breakpointObserver
      .observe(['(max-width: 992px)'])
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((result: BreakpointState) => {
        this.isMobile = result.matches;
        if (this.isMobile && this.viewSelected === 'Table') {
          this.goTo('Live');
        } else if (!this.isMobile && this.viewSelected === 'Live') {
          this.goTo('Table');
        }
      });
  }

  private _selectedCategory: Category;

  get selectedCategory(): Category {
    return this._selectedCategory;
  }

  @Input()
  set selectedCategory(value: Category) {
    if (this._selectedCategory !== value) {
      this._selectedCategory = value;
    }
  }

  ngOnInit() {
    this.router.events
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((event) => {
        let navigationEvent: RouterEvent;
        if (event instanceof Scroll) {
          navigationEvent = (event as Scroll).routerEvent;
        } else {
          navigationEvent = event as RouterEvent;
        }
        if (navigationEvent instanceof NavigationEnd) {
          if (this.route.firstChild) {
            const fragments = this.route.firstChild.snapshot.url;
            const matchedView = this.views.find(
              (view) => view.toLowerCase() === fragments[0].path.toLowerCase(),
            );
            this.viewSelected = matchedView;
            if (this.viewSelected === 'Table' && this.isMobile) {
              this.goTo('Live');
            } else if (this.viewSelected === 'Live' && !this.isMobile) {
              this.goTo('Table');
            }
            this.cd.markForCheck();
          }
        }
      });
  }

  goTo(view: View) {
    this.viewSelected = view;
    this.router.navigate([view.toLowerCase()], { relativeTo: this.route });
    this.cd.detectChanges();
  }

  isViewVisible(view: View): boolean {
    // Responsive Views
    if (view === 'Live' && !this.isMobile) {
      return false;
    }
    if (view === 'Table' && this.isMobile) {
      return false;
    }

    // Default
    return true;
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }
}
