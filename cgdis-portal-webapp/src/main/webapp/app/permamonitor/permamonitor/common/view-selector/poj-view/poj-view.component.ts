import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { PojViewTableComponent } from '@permamonitor/permamonitor/common/view-selector/poj-view/poj-view-table/poj-view-table.component';
import { PojViewChartComponent } from '@permamonitor/permamonitor/common/view-selector/poj-view/poj-view-chart/poj-view-chart.component';
import { Category } from '@permamonitor/permamonitor/common/constants';
import { SharedModule } from '@app/common/shared/shared.module';

@Component({
  selector: 'cgdis-portal-poj-view',
  standalone: true,
  imports: [PojViewTableComponent, PojViewChartComponent, SharedModule],
  templateUrl: './poj-view.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PojViewComponent {
  @Input() filterInput: any;
  @Input() category: Category;
}
