import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import Highcharts from 'highcharts';
import xrange from 'highcharts/modules/xrange';
import { HighchartsChartModule } from 'highcharts-angular';
import { ScrollModule } from '@app/common/modules/scroll/scroll.module';
import { TranslateModule } from '@ngx-translate/core';
import { VehicleShift } from '@permamonitor/permamonitor/views/view-base';
import { LegendModule } from '@app/common/modules/legend/legend.module';
import { LegendItem } from '@app/common/modules/legend/legend-item';
import { TableBodyInfoBoxComponent } from '@permamonitor/permamonitor/common/view-selector/table-view/table-view-info-box/table-view-info-box.component';
import { TableViewService } from '@permamonitor/permamonitor/common/view-selector/table-view/table-view.service';
import { PermScheduleRowModel } from '@app/model/permamonitor/schedule/perm-schedule-row.model';
import { DateModel, DateService, DatetimeService } from '@eportal/core';
import { CompletionDayStatus } from '@app/model/completion-day-status.enum';
import { Category } from '@permamonitor/permamonitor/common/constants';
import { SharedModule } from '@app/common/shared/shared.module';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';

// Initialize the xrange module
xrange(Highcharts);

interface Infobox {
  name?: string;
  start_time?: string;
  end_time?: string;
}

@Component({
  selector: 'cgdis-portal-permamonitor-table-body',
  standalone: true,
  imports: [
    HighchartsChartModule,
    ScrollModule,
    TranslateModule,
    LegendModule,
    TableBodyInfoBoxComponent,
    SharedModule,
  ],
  providers: [TableViewService],
  templateUrl: './table-view.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TableBodyComponent implements OnChanges, OnInit, OnDestroy {
  @Input() data: VehicleShift[];
  @Input() filterInput: any;
  @Input() category: Category;
  HighchartsStickyAxis: typeof Highcharts = Highcharts;
  Highcharts: typeof Highcharts = Highcharts;
  legendItems: LegendItem[];
  chartOptions: Highcharts.Options = {};
  xAxisOptions: Highcharts.Options;
  infobox: Infobox = {};
  occupationColorClasses: Record<CompletionDayStatus, string> = {
    COMPLETE: '-complete',
    INCOMPLETE: '-incomplete',
    DEGRADED: '-degraded',
    PARTIAL: '-partial',
    EMPTY: '-empty',
    UNKNOWN: '-unknown',
  };
  isLoading: boolean = false;

  schedules: PermScheduleRowModel[];
  private resetInfoboxTimeoutId: any = null;
  private _unsubscribe$ = new Subject<void>();

  private isTablet: boolean = false;

  constructor(
    private cd: ChangeDetectorRef,
    private tableViewService: TableViewService,
    private dateTimeService: DatetimeService,
    private dateService: DateService,
    private breakpointObserver: BreakpointObserver,
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.filterInput && this.filterInput) {
      this.fetchTableData();
    }
  }

  ngOnInit() {
    this.initLegend();

    this.breakpointObserver
      .observe(['(max-width: 1200px)'])
      .subscribe((result: BreakpointState) => {
        this.isTablet = result.matches;

        this.cd.markForCheck();
      });
  }

  fetchTableData() {
    this.isLoading = true;
    let baseEntityTecId = undefined;
    if (this.filterInput.region) {
      baseEntityTecId = this.filterInput.region.group
        ? this.filterInput.region.group.tecid
        : this.filterInput.region.zone.tecid;
    }

    this.tableViewService
      .getAllSlots({
        category: this.filterInput.category,
        startDateTime: this.filterInput.period,
        baseEntityTecid: baseEntityTecId,
        subcategory: this.filterInput.type,
        isFavorite: this.filterInput.isFavorite,
      })
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((value) => {
        this.schedules = value.rows;
        this.redrawChart(this.filterInput.period);
        this.isLoading = false;
      });
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }

  initLegend() {
    this.legendItems = [
      new LegendItem({
        id: '1',
        labelKey: 'service_plan.legend.status.complete',
        classes: ['-complete'],
      }),
      new LegendItem({
        id: '2',
        labelKey: 'service_plan.legend.status.partial',
        classes: ['-partial'],
      }),
      new LegendItem({
        id: '3',
        labelKey: 'service_plan.legend.status.degraded',
        classes: ['-degraded'],
      }),
      new LegendItem({
        id: '4',
        labelKey: 'service_plan.legend.status.incomplete',
        classes: ['-incomplete'],
      }),
      new LegendItem({
        id: '6',
        labelKey: 'service_plan.legend.status.empty',
        classes: ['-empty'],
      }),
    ];
  }

  setInfobox(point: any) {
    this.infobox = this.pointToObject(point);
    this.cd.detectChanges();
  }

  resetInfobox() {
    this.infobox = {};
    this.cd.detectChanges();
  }

  pointToObject(point: any): any {
    return {
      category: this.filterInput.category,
      slotId: point.slotId,
      name: point.name,
      startDateTime: point.startDateTime,
      endDateTime: point.endDateTime,
    };
  }

  generateStickyChartOptions(startDate?: DateModel) {
    const { startOfPeriod, endOfPeriod, cetOffset } = this.getTimeRange(
      this.dateService.modeltoJSDate(startDate),
    );

    const that = this;
    this.xAxisOptions = {
      chart: {
        backgroundColor: '#ffffff',
        height: 30,
        marginTop: 30,
        marginLeft: 170,
        marginRight: 10,
      },
      credits: {
        enabled: false,
      },

      title: {
        text: null,
      },
      xAxis: {
        type: 'datetime',
        min: startOfPeriod + cetOffset,
        max: endOfPeriod + cetOffset,
        tickInterval: 3600 * 1000,
        gridLineColor: '#ffffff',
        labels: {
          y: -6,
          rotation: 0,
          style: {
            fontSize: '10px',
            color: '#ffffff',
          },
          formatter: function () {
            const date = new Date(this.value); // Convert the x-value to a Date object
            return that.isTablet
              ? Highcharts.dateFormat('%H', date.getTime())
              : Highcharts.dateFormat('%H:%M', date.getTime()); // Format as hour (24-hour format)
          },
        },
        tickColor: '#ffffff',
        tickWidth: 2,
        opposite: true,
      },
      yAxis: {
        title: {
          text: null,
        },
        tickInterval: 1,
        labels: {
          style: {
            color: '#E0E0E3',
          },
        },
      },
      series: [
        {
          type: 'line',
          data: [
            [startOfPeriod, null],
            [endOfPeriod, null],
          ],
          enableMouseTracking: false,
          showInLegend: false,
        },
      ],
    };
  }

  getChartHeight() {
    const headerHeight = 13;
    return this.schedules.length * 30 + headerHeight;
  }

  generateChartOptions(startDate?: DateModel) {
    const { startOfPeriod, endOfPeriod } = this.getTimeRange(
      this.dateService.modeltoJSDate(startDate),
    );

    const componentInstance = this;

    if (this.schedules) {
      this.chartOptions = {
        chart: {
          type: 'xrange',
          backgroundColor: '#22343c',
          height: this.getChartHeight(),
          marginTop: 0,
          marginLeft: 170,
          marginRight: 0,
        },
        credits: {
          enabled: false,
        },
        legend: {
          enabled: false,
        },
        title: {
          text: null,
        },

        xAxis: {
          type: 'datetime',
          min: startOfPeriod,
          max: endOfPeriod,
          tickInterval: 3600 * 1000, // One tick per hour
          opposite: true,
          plotLines: [
            {
              value: Date.now(),
              dashStyle: 'Solid',
              zIndex: 5,
              color: '$c-primary',
            },
          ],
        },
        yAxis: [
          {
            title: {
              text: null,
            },
            categories: this.schedules.map(
              (schedule) => schedule.servicePlan.portalLabel,
            ),
            reversed: true,
            tickInterval: 1,
            labels: {
              style: {
                fontSize: '10px',
                color: '$c-secondary',
                fontWeight: '600',
              },
            },
          },
        ],
        plotOptions: {
          series: {
            states: {
              inactive: {
                enabled: false,
              },
            },
          },
          xrange: {
            borderRadius: 0,
            borderWidth: 0,
            groupPadding: 0.0,
            pointPadding: 0.0,
            grouping: false,
            colorByPoint: false,
            point: {
              events: {
                mouseOver: function () {
                  this.series.points.forEach((point) => {
                    if (point !== this) {
                      point.graphic.addClass('non-hovered');
                    }
                  });
                  if (componentInstance.resetInfoboxTimeoutId) {
                    clearTimeout(componentInstance.resetInfoboxTimeoutId);
                    componentInstance.resetInfoboxTimeoutId = null;
                  }
                  componentInstance.setInfobox(this);
                },
                mouseOut: function () {
                  this.series.points.forEach((point) => {
                    point.graphic.removeClass('non-hovered');
                  });
                  componentInstance.resetInfoboxTimeoutId = setTimeout(() => {
                    componentInstance.resetInfobox();
                    componentInstance.resetInfoboxTimeoutId = null;
                  }, 100);
                },
              },
            },
          },
        },
        tooltip: {
          enabled: false,
        },
        series: this.schedules.map((schedule, index) => ({
          name: schedule.servicePlan.portalLabel,
          type: 'xrange',
          data: schedule.slots.map((slot) => ({
            x: Date.parse(
              this.dateTimeService
                .modeltoJSDate(slot.slotStartDateTime)
                .toString(),
            ), // Parse the combined date-time string for the start time
            x2: Date.parse(
              this.dateTimeService
                .modeltoJSDate(slot.slotEndDateTime)
                .toString(),
            ), // Parse the combined date-time string for the end time
            y: index,
            name: schedule.servicePlan.portalLabel, // Display the time portion in the label
            className: `status fill ${this.occupationColorClasses[slot.status ? slot.status : 'EMPTY']}`,
            startDateTime: slot.slotStartDateTime,
            endDateTime: slot.slotEndDateTime,
            slotId: slot.slot.tecid,
          })),
        })),
      };
    }
  }

  private redrawChart(startDate?: DateModel) {
    this.chartOptions = null;
    this.xAxisOptions = null;
    setTimeout(() => {
      this.generateChartOptions(startDate);
      this.generateStickyChartOptions(startDate);
      this.cd.markForCheck();
    }, 0);
  }

  private getTimeRange(startDate?: Date): {
    startOfPeriod: number;
    endOfPeriod: number;
    cetOffset: number;
  } {
    const now = startDate || new Date();
    const isToday = this.isSameDate(now, new Date());
    const cetOffset = -now.getTimezoneOffset() * 60 * 1000;
    let startOfPeriod;
    const startHour = isToday ? new Date().getUTCHours() : now.getUTCHours();
    if (!isToday) {
      startOfPeriod = Date.UTC(
        now.getUTCFullYear(),
        now.getUTCMonth(),
        now.getUTCDate(),
        startHour,
      );
    } else {
      startOfPeriod = Date.UTC(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        startHour,
      );
    }

    return {
      startOfPeriod,
      endOfPeriod: startOfPeriod + 24 * 3600 * 1000,
      cetOffset,
    };
  }

  private isSameDate(date1: Date, date2: Date): boolean {
    return (
      date1.getFullYear() === date2.getFullYear() &&
      date1.getMonth() === date2.getMonth() &&
      date1.getDate() === date2.getDate()
    );
  }
}
