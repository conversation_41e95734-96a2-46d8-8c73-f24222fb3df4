import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { SharedModule } from '@app/common/shared/shared.module';
import { INgxSelectOption, NgxSelectModule } from 'ngx-select-ex';
import { Category } from '@permamonitor/permamonitor/common/constants';
import { ActivatedRoute, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { CategorySelectorService } from '@permamonitor/permamonitor/common/category-selector/category-selector.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
  standalone: true,
  selector: 'cgdis-portal-permamonitor-category-selector',
  templateUrl: './category-selector.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SharedModule, NgxSelectModule, FormsModule],
  providers: [CategorySelectorService],
})
export class PermamonitorCategorySelectorComponent
  implements OnInit, OnDestroy
{
  protected categories!: Category[];
  protected initalCategory: Category = undefined;
  private isInitialized = false;
  private _unsubscribe$ = new Subject<void>();

  constructor(
    private cd: ChangeDetectorRef,
    private route: ActivatedRoute,
    private router: Router,
    private categoryService: CategorySelectorService,
  ) {}

  ngOnInit() {
    this.initCategories();
  }

  initCategories() {
    this.categoryService
      .getAllCategories()
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((value) => {
        this.categories = value;
        const urlCategory = this.router.url.split('/')[2];
        let matchedCategory: Category = undefined;

        if (urlCategory !== undefined) {
          matchedCategory = this.categories.find(
            (category) => category.toLowerCase() === urlCategory.toLowerCase(),
          );
        }

        if (matchedCategory !== undefined) {
          this.initalCategory = matchedCategory;
          this.isInitialized = true;
        } else {
          this.initalCategory = this.categories[0];
          this.goTo(this.categories[0]);
        }
        this.cd.markForCheck();
      });
  }

  goTo(category: Category) {
    const lastView = this.router.url.split('/')[3];

    this.router.navigate([category.toLowerCase(), lastView], {
      relativeTo: this.route,
    });
    this.cd.detectChanges();
  }

  onCategoryChange(event: INgxSelectOption[]) {
    if (this.isInitialized) {
      this.isInitialized = false;
    } else {
      const category = event[0].value as Category;
      this.goTo(category);
    }
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }
}
