import { Injectable } from '@angular/core';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { RestService } from '@eportal/core';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { PermPojScheduleRowModel } from '@app/model/permamonitor/poj/perm-poj-schedule-row.model';

@Injectable()
export class PojViewTableService extends CgdisDatatableService<PermPojScheduleRowModel> {
  constructor(
    private restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService);
    super.initDataResourceList(
      restService.all('permamonitor', 'schedules', 'poj', 'table'),
    );
  }
}
