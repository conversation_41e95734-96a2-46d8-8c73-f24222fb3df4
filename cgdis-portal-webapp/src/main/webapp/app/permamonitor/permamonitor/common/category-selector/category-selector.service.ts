import { Injectable } from '@angular/core';
import { RestService } from '@eportal/core';
import { map } from 'rxjs/operators';
import { PermCategoryModel } from '@app/model/permamonitor/perm-category.model';

@Injectable()
export class CategorySelectorService {
  constructor(private _restService: RestService) {}

  getAllCategories() {
    return this._restService
      .all('permamonitor', 'categories', 'all')
      .get()
      .pipe(
        map((response: PermCategoryModel[]) =>
          response.map((item) => item.name),
        ),
      );
  }
}
