import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  OnInit,
  Output,
} from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { FilterTemplateComponent } from '@app/common/modules/filter-template/filter-template.component';

export type IsPrimaryFilterOutput = boolean;

@Component({
  selector: 'cgdis-portal-permamonitor-is-primary-filter',
  standalone: true,
  imports: [TranslateModule, FilterTemplateComponent],
  templateUrl: './is-primary-filter.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IsPrimaryFilterComponent implements OnInit {
  @Output() isPrimaryFilterOutput = new EventEmitter<IsPrimaryFilterOutput>();

  currentIsPrimaryFilter = false;

  ngOnInit() {
    this.emitFilterOutput();
  }

  toggleIsPrimary(): void {
    this.currentIsPrimaryFilter = !this.currentIsPrimaryFilter;
    this.emitFilterOutput();
  }

  emitFilterOutput() {
    this.isPrimaryFilterOutput.emit(this.currentIsPrimaryFilter);
  }
}
