<div class="info-box">
  @if (requestData.name) {

    <h4 class="title">{{ requestData.name }}
      ({{ regionData ? getCompletionStatusNb(regionData.completionStatuses).fullfilled : '-' }}
      / {{ criticityConfig?.optValue | defaultValue }})</h4>

    @if (!isLoading) {
      @if (regionData.rows.length) {
        @for (row of regionData.rows; track row) {
          <!--        <div>{{ row | json }}</div>-->
          <div class="service-plan-row">
            <dt class="legend-box__item status" [ngClass]="getStatusColor(row.slots[0]?.rowSlot.status)">●</dt>
            <div class="service-plan-row-text">
              <div>{{ getDateTimeString(row.slots[0]?.rowSlot.slotStartDateTime) }}
                - {{ getDateTimeString(row.slots[0]?.rowSlot.slotEndDateTime) }} {{ row.servicePlan.portalLabel }}
              </div>
              <div>({{ row.slots[0]?.nbFulfilledPositions }}/{{ row.slots[0]?.nbPositions }})</div>
            </div>
          </div>

        }
      } @else {
        <span [translate]="'permamonitor.view.map.no-config-available'"></span>

      }


    } @else {
      <span [translate]="'fetching-text'"></span>
    }
  } @else {
    <h3>{{ 'permamonitor.view.map.information.title' | translate }} </h3>

  }
  <cgdis-portal-legend [items]="legendItems" [legendClasses]="['infobox-legends']"></cgdis-portal-legend>

</div>
