import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  SimpleChanges,
} from '@angular/core';
import Highcharts from 'highcharts';
import { HighchartsChartModule } from 'highcharts-angular';
import { Category } from '@permamonitor/permamonitor/common/constants';
import { DatetimeModel, DatetimeService } from '@eportal/core';
import { PojViewChartService } from '@permamonitor/permamonitor/common/view-selector/poj-view/poj-view-chart/poj-view-chart.service';
import { PermPojScheduleChartModel } from '@app/model/permamonitor/poj/perm-poj-schedule-chart.model';
import { SharedModule } from '@app/common/shared/shared.module';
import { TranslateService } from '@ngx-translate/core';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';

@Component({
  selector: 'cgdis-portal-poj-view-chart',
  standalone: true,
  imports: [HighchartsChartModule, SharedModule],
  templateUrl: './poj-view-chart.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [PojViewChartService],
})
export class PojViewChartComponent implements OnChanges, OnDestroy {
  @Input() filterInput: any;
  @Input() category: Category;
  @Input() type: 'next24hours' | 'last7days' | 'next7days' | 'last30days';
  chartOptions: any;

  chartData: PermPojScheduleChartModel[];
  protected readonly Highcharts = Highcharts;

  protected loading: boolean;
  private _unsubscribe$ = new Subject<void>();

  constructor(
    protected cd: ChangeDetectorRef,
    private dateTimeService: DatetimeService,
    private pojChartService: PojViewChartService,
    private translateService: TranslateService,
  ) {}

  processNewBarData(
    chartData: PermPojScheduleChartModel[],
    minDate: Date,
    maxDate: Date,
  ): {
    redSeries: [number, number][];
    yellowSeries: [number, number][];
    greenSeries: [number, number][];
    blueSeries: [number, number][];
  } {
    const redSeries: [number, number][] = [];
    const yellowSeries: [number, number][] = [];
    const blueSeries: [number, number][] = [];
    const greenSeries: [number, number][] = [];
    const maxValue = this.calculateMaxValue();

    const maxDateTimestamp = maxDate.getTime();
    const minDateTimestamp = minDate.getTime();

    chartData.forEach((dataPoint) => {
      const timestamp = this.convertToTimestamp(dataPoint.datetime);
      if (timestamp === null) {
        return; // Skip if the timestamp is null (e.g., due to daylight saving time)
      }
      if (timestamp >= minDateTimestamp && timestamp <= maxDateTimestamp) {
        const { pojCritical, pojOptimal, pojUnacceptable } = dataPoint;

        redSeries.push([timestamp, pojUnacceptable]);
        yellowSeries.push([timestamp, pojOptimal - pojUnacceptable]);
        // yellowSeries.push([timestamp, pojCritical - pojUnacceptable]);
        // blueSeries.push([timestamp, pojOptimal - pojCritical]);
        greenSeries.push([timestamp, maxValue - pojOptimal]);
      }
    });

    return { redSeries, yellowSeries, greenSeries, blueSeries };
  }

  ngOnChanges(changes: SimpleChanges) {
    if (!changes.filterInput || !this.filterInput) {
      return;
    }

    const previousValue = changes.filterInput.previousValue?.periodEvolution;
    const currentValue = changes.filterInput.currentValue.periodEvolution;

    if (!previousValue || !currentValue) {
      // Fetch data if there was no previous value or the current value is null
      this.fetchData();
      return;
    }

    // Compare date parts (excluding hours)
    const previousDatePart = this.dateTimeService.format(
      previousValue,
      'DD/MM/YYYY',
    );
    const currentDatePart = this.dateTimeService.format(
      currentValue,
      'DD/MM/YYYY',
    );

    if (previousDatePart !== currentDatePart) {
      // Fetch data if the date part changes
      this.fetchData();
      return;
    }

    // Compare hours
    if (
      previousValue.hours !== currentValue.hours &&
      this.type !== 'next24hours'
    ) {
      // No fetch if only hours change
      return;
    }

    // Fetch data if any other part changes (minutes, seconds, etc.)
    this.fetchData();
  }

  fetchData() {
    let baseTecId;
    this.loading = true;
    if (this.filterInput.region.group) {
      baseTecId = this.filterInput.region.group.tecid;
    } else {
      baseTecId = this.filterInput.region.zone.tecid;
    }
    switch (this.type) {
      case 'next24hours':
        this.pojChartService
          .getNext24Hours(
            this.filterInput.periodEvolution,
            this.category,
            baseTecId,
            this.filterInput.type,
          )
          .pipe(takeUntil(this._unsubscribe$))
          .subscribe((value) => {
            this.chartData = value;
            this.redrawChart();
            this.loading = false;
          });
        break;
      case 'last7days':
        this.pojChartService
          .getLast7Days(
            this.filterInput.periodEvolution,
            this.category,
            baseTecId,
            this.filterInput.type,
          )
          .pipe(takeUntil(this._unsubscribe$))
          .subscribe((value) => {
            this.chartData = value;
            this.redrawChart();
            this.loading = false;
          });
        break;

      case 'next7days':
        this.pojChartService
          .getNext7Days(
            this.filterInput.periodEvolution,
            this.category,
            baseTecId,
            this.filterInput.type,
          )
          .pipe(takeUntil(this._unsubscribe$))
          .subscribe((value) => {
            this.chartData = value;
            this.redrawChart();
            this.loading = false;
          });
        break;

      case 'last30days':
        this.pojChartService
          .getLast30Days(
            this.filterInput.periodEvolution,
            this.category,
            baseTecId,
            this.filterInput.type,
          )
          .pipe(takeUntil(this._unsubscribe$))
          .subscribe((value) => {
            this.chartData = value;
            this.redrawChart();
            this.loading = false;
          });
        break;
    }
  }

  redrawChart() {
    this.chartOptions = null;
    setTimeout(() => {
      this.generateChartOptions();
      this.cd.markForCheck();
    }, 0);
  }

  calculateMaxValue(): number {
    const maxArmed = Math.max(
      ...this.chartData.map(({ totalReady }) => totalReady),
    );

    const maxPojIdeal = Math.max(
      ...Object.values(this.chartData).map(({ pojIdeal }) => pojIdeal),
    );

    const maxValue = Math.max(maxArmed, maxPojIdeal) * 1.1;

    return Math.ceil(maxValue);
  }

  generateNext24HoursChartOptionsData(baseDate: Date) {
    const endDate = new Date(baseDate);
    const startDate = new Date(baseDate);
    endDate.setHours(startDate.getHours() + 24);
    startDate.setHours(startDate.getHours());

    const minDate = startDate.getTime();
    const maxDate = endDate.getTime();

    const { redSeries, yellowSeries, greenSeries, blueSeries } =
      this.processNewBarData(this.chartData, startDate, endDate);
    const armedLineSeries = this.processLineData();

    return this.createChartOptions(
      redSeries,
      yellowSeries,
      greenSeries,
      blueSeries,
      armedLineSeries,
      [],
      this.translateService.instant('permamonitor.view.poj.chart.next24'),
      minDate,
      maxDate,
    );
  }

  generateLast7DaysChartOptionsData(baseDate: Date) {
    const endDate = new Date(baseDate);
    const startDate = new Date(baseDate);

    endDate.setDate(endDate.getDate());
    endDate.setHours(0, 0, 0, 0);
    endDate.setHours(endDate.getHours());

    startDate.setDate(endDate.getDate() - 7);
    startDate.setHours(0, 0, 0, 0);
    startDate.setHours(startDate.getHours());

    const minDate = startDate.getTime();
    const maxDate = endDate.getTime();

    const { redSeries, yellowSeries, greenSeries, blueSeries } =
      this.processNewBarData(this.chartData, startDate, endDate);
    const armedLineSeries = this.processLineData();

    return this.createChartOptions(
      redSeries,
      yellowSeries,
      greenSeries,
      blueSeries,
      armedLineSeries,
      [],
      this.translateService.instant('permamonitor.view.poj.chart.last7'),
      minDate,
      maxDate,
    );
  }

  generateNext7DaysChartOptionsData(baseDate: Date) {
    const startDate = new Date(baseDate);
    startDate.setHours(0, 0, 0, 0);
    startDate.setHours(startDate.getHours());

    const endDate = new Date(baseDate);
    endDate.setDate(endDate.getDate() + 7);
    endDate.setHours(0, 0, 0, 0);
    endDate.setHours(endDate.getHours());

    const minDate = startDate.getTime();
    const maxDate = endDate.getTime();

    const { redSeries, yellowSeries, greenSeries, blueSeries } =
      this.processNewBarData(this.chartData, startDate, endDate);
    const armedLineSeries = this.processLineData();

    return this.createChartOptions(
      redSeries,
      yellowSeries,
      greenSeries,
      blueSeries,
      armedLineSeries,
      [],
      this.translateService.instant('permamonitor.view.poj.chart.next7'),
      minDate,
      maxDate,
    );
  }

  generateLast30DaysChartOptionsData(baseDate: Date) {
    const endDate = new Date(baseDate);
    const startDate = new Date(baseDate);

    endDate.setDate(endDate.getDate());
    endDate.setHours(0, 0, 0, 0);
    endDate.setHours(endDate.getHours());

    startDate.setDate(endDate.getDate() - 30);
    startDate.setHours(0, 0, 0, 0);
    startDate.setHours(startDate.getHours());

    const minDate = startDate.getTime();
    const maxDate = endDate.getTime();

    const { redSeries, yellowSeries, greenSeries, blueSeries } =
      this.processNewBarData(this.chartData, startDate, endDate);

    const armedLineSeries = this.processLineData();

    return this.createChartOptions(
      redSeries,
      yellowSeries,
      greenSeries,
      blueSeries,
      armedLineSeries,
      [],
      this.translateService.instant('permamonitor.view.poj.chart.last30'),
      minDate,
      maxDate,
    );
  }

  generateChartOptions() {
    const baseDate = this.dateTimeService.modeltoJSDate(
      this.filterInput.periodEvolution,
    );

    if (this.type === 'next24hours') {
      this.chartOptions = this.generateNext24HoursChartOptionsData(baseDate);
    } else if (this.type === 'next7days') {
      this.chartOptions = this.generateNext7DaysChartOptionsData(baseDate);
    } else if (this.type === 'last7days') {
      this.chartOptions = this.generateLast7DaysChartOptionsData(baseDate);
    } else if (this.type === 'last30days') {
      this.chartOptions = this.generateLast30DaysChartOptionsData(baseDate);
    }
    this.cd.markForCheck();
  }

  processLineData() {
    const armedLineSeries: [number, number][] = [];

    this.chartData.forEach(({ datetime, totalReady }) => {
      const timestamp = this.convertToTimestamp(datetime);
      if (timestamp === null) {
        return; // Skip if the timestamp is null (e.g., due to daylight saving time)
      }
      armedLineSeries.push([timestamp, totalReady]);
    });

    return armedLineSeries;
  }

  /**
   * Convert a DatetimeModel to a timestamp.
   * This method checks if the hours of the DatetimeModel match the hours of the base date.
   * If they don't match, it returns null to indicate a potential issue with daylight saving time.
   *
   * @param dateTime
   */
  convertToTimestamp(dateTime: DatetimeModel): number {
    const baseDate = this.dateTimeService.modeltoJSDate(dateTime);
    if (dateTime.hours !== baseDate.getHours()) {
      // case of daylight saving time
      return null;
    }
    return baseDate.getTime();
  }

  createChartOptions(
    redSeries: [number, number][],
    yellowSeries: [number, number][],
    greenSeries: [number, number][],
    blueSeries: [number, number][],
    armedLineSeries: [number, number][],
    tooltipData: any[],
    chartTitle: string,
    minDate: number, // Explicit start of the x-axis
    maxDate: number, // Explicit end of the x-axis
  ) {
    const isHourlyChart = this.type === 'next24hours';
    return {
      time: {
        timezone: 'Europe/Luxembourg',
        useUTC: true,
      },
      legend: {
        enabled: true,
        align: 'center',
        verticalAlign: 'top',
        layout: 'horizontal',
        itemStyle: {
          fontSize: '10px',
        },
      },
      chart: {
        type: 'column',
        height: isHourlyChart ? '550' : 'null',
      },
      credits: {
        enabled: false,
      },
      title: {
        text: chartTitle,
        style: {
          fontSize: '14px',
          fontWeight: 'bold',
          color: '#252c36',
        },
        margin: 0,
      },
      xAxis: {
        type: 'datetime', // Use datetime for the x-axis
        title: {
          text: '',
        },
        labels: {
          format: isHourlyChart ? '{value:%Hh}' : '{value:%d/%m}', // Hourly or daily format
          style: {
            fontSize: '10px',
            // fontWeight: 'bold',
            color: '#333',
          },
        },
        min: minDate, // Set explicit x-axis start
        max: maxDate, // Set explicit x-axis end
        tickInterval: isHourlyChart
          ? 3600000 // 1 hour in milliseconds
          : 24 * 3600000, // 1 day in milliseconds
        startOnTick: true,
        endOnTick: false,
      },
      yAxis: {
        title: {
          text: '',
        },

        endOnTick: false, // Align the end of the chart to a tick

        labels: {
          style: {
            fontSize: '10px',
            // fontWeight: 'bold',
            color: '#333',
          },
        },
        max: this.calculateMaxValue(),
      },
      tooltip: {
        shared: false,
        formatter: function () {
          if (this.series.type === 'line') {
            // Tooltip for the line series
            return `
        <b>Armed:</b> ${this.y}`;
          } else if (this.series.type === 'column') {
            // Tooltip for the bar series
            return `
        <b>${this.series.name}:</b> ${this.y}`;
          }
          return '';
        },
      },

      plotOptions: {
        column: {
          stacking: 'normal',
          pointPadding: 0,
          groupPadding: 0,
          borderWidth: 0,
          crisp: false,
          pointPlacement: 'on',
          enableMouseTracking: false,
        },
      },
      series: [
        {
          type: 'column',
          name: 'Optimal',
          data: greenSeries,
          color: 'rgba(0,119,0,0.3)',
        },
        // {
        //   type: 'column',
        //   name: 'Acceptable',
        //   data: blueSeries,
        //   color: 'rgba(255,115,0,0.3)',
        // },

        {
          type: 'column',
          name: 'Critique',
          data: yellowSeries,
          color: 'rgba(255,115,0,0.3)',
        },
        {
          type: 'column',
          name: 'Non-Acceptable',
          data: redSeries,
          color: 'rgba(255,0,0,0.3)',
        },

        {
          type: 'line',
          name: 'Ligne Armement',
          data: armedLineSeries,
          color: '#596dff',
          linecap: 'square',
          step: 'start',
          marker: {
            enabled: false,
          },
        },
      ],
    };
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }
}
