<div class="info-box">
  @if (requestData.name) {

    <h4 class="title">{{ requestData.name }}</h4>

    <div class="info-group">

      <span>{{ getStartDateTimeString() }} - {{ getEndDateTimeString() }}</span>

    </div>
    @if (!isLoading) {
      @for (position of slotPositions; track position) {
        <div class="info-group">
          <span class="label">{{ position.label }}</span>
          <span class="data">{{ getPositionInfo(position.tecid) }}</span>
        </div>
      }
      <div class="backup-group" [ngClass]="{'inactive':backupGroupName === null}">
        <cgdis-portal-icon [icon]="'icon-bell'"></cgdis-portal-icon>
        @if (backupGroupName) {
          {{ backupGroupName }}
        } @else {
          -
        }
      </div>
    } @else {
      <span [translate]="'fetching-text'"></span>
    }
  } @else {
    <h3>{{ 'permamonitor.view.table.information.title' | translate }} </h3>

  }

</div>
