import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { FilterTemplateComponent } from '@app/common/modules/filter-template/filter-template.component';

export type ToggleFilterOutput = boolean;

@Component({
  selector: 'cgdis-portal-permamonitor-toggle-filter',
  standalone: true,
  imports: [TranslateModule, FilterTemplateComponent],
  templateUrl: './toggle-filter.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ToggleFilterComponent implements OnInit {
  @Input() labelKey: string;

  @Output() toggleFilterOutput = new EventEmitter<ToggleFilterOutput>();

  currentToggleFilter = false;

  ngOnInit() {
    this.emitFilterOutput();
  }

  toggleFilter(): void {
    this.currentToggleFilter = !this.currentToggleFilter;
    this.emitFilterOutput();
  }

  emitFilterOutput() {
    this.toggleFilterOutput.emit(this.currentToggleFilter);
  }
}
