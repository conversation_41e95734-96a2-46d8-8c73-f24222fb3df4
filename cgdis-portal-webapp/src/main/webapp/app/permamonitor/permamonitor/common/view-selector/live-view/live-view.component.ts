import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { JsonPipe, NgClass } from '@angular/common';
import { LiveViewService } from '@permamonitor/permamonitor/common/view-selector/live-view/live-view.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { DatetimeModel, DatetimeService } from '@eportal/core';
import { PermScheduleRowModel } from '@app/model/permamonitor/schedule/perm-schedule-row.model';
import { Category } from '@permamonitor/permamonitor/common/constants';
import { CompletionDayStatus } from '@app/model/completion-day-status.enum';
import { SharedModule } from '@app/common/shared/shared.module';
import { SimplePopupModule } from '@app/common/modules/popup/simple-popup.module';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { LiveViewPopupComponent } from '@app/common/modules/popup/permamonitor/live-view-popup/live-view-popup.component';
import { PermLiveViewPopupInputData } from '@permamonitor/permamonitor/common/view-selector/live-view/models/PermLiveViewPopupInputData';

@Component({
  selector: 'cgdis-portal-live-view',
  standalone: true,
  imports: [
    JsonPipe,
    NgClass,
    SharedModule,
    SimplePopupModule,
    MatDialogModule,
  ],
  templateUrl: './live-view.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [LiveViewService],
})
export class LiveViewComponent implements OnChanges, OnDestroy, OnInit {
  @Input() filterInput: any = {};
  @Input() category: Category;
  isLoading: boolean = false;
  rows: PermScheduleRowModel[];
  occupationColorClasses: Record<CompletionDayStatus, string> = {
    COMPLETE: '-complete',
    INCOMPLETE: '-incomplete',
    DEGRADED: '-degraded',
    PARTIAL: '-partial',
    EMPTY: '-empty',
    UNKNOWN: '-unknown',
  };
  private _unsubscribe$ = new Subject<void>();

  constructor(
    private liveViewService: LiveViewService,
    private dateTimeService: DatetimeService,
    private cd: ChangeDetectorRef,
    private dialog: MatDialog,
  ) {}

  openLiveViewPopup(data: any): void {
    let dialogData: PermLiveViewPopupInputData = {};
    dialogData.category = this.category as Category;
    dialogData.slotId = data.slots[0].slot.tecid;
    dialogData.startDateTime = data.slots[0].slotStartDateTime;
    dialogData.title = data.servicePlan.portalLabel;
    this.dialog.open(LiveViewPopupComponent, {
      data: dialogData,
      width: '500px',
      panelClass: 'simple-popup-mobile',
    });
  }

  ngOnInit() {
    if (this.category === 'Compagnie') {
      this.fetchLiveViewData();
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.filterInput && this.filterInput) {
      this.fetchLiveViewData();
    }
  }

  fetchLiveViewData() {
    this.isLoading = true;
    let baseEntityTecId = undefined;
    if (this.filterInput?.region) {
      baseEntityTecId = this.filterInput.region.group
        ? this.filterInput.region.group.tecid
        : this.filterInput.region.zone.tecid;
    }

    this.liveViewService
      .getAllSlots({
        category: this.category,
        startDateTime: this.dateTimeService.now(),
        endDateTime: this.dateTimeService.now(),
        baseEntityTecid: baseEntityTecId,
        subcategory: this.filterInput?.type,
        isFavorite: this.filterInput?.isFavorite,
      })
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((value) => {
        this.rows = value.rows;
        console.log(value);
        this.isLoading = false;
        this.cd.markForCheck();
      });
  }

  getTimeString(startTime: DatetimeModel, endTime: DatetimeModel) {
    return (
      this.dateTimeService.format(startTime, 'DD/MM HH:mm') +
      ' - ' +
      this.dateTimeService.format(endTime, 'HH:mm')
    );
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }
}
