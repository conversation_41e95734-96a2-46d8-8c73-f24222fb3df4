import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import {
  DateModel,
  DateService,
  DatetimeModel,
  DatetimeService,
} from '@eportal/core';
import { DatepickerModule } from '@app/common/modules/datepicker/datepicker.module';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FilterTemplateComponent } from '@app/common/modules/filter-template/filter-template.component';
import { SharedModule } from '@app/common/shared/shared.module';
import { SliderModule } from '@app/common/modules/slider/slider.module';
import { SliderConfig } from '@app/common/modules/slider/slider-config';
import { SliderFormatter } from '@app/common/modules/slider/slider-formatter';
import { SliderTimeFormatter } from '@app/common/modules/slider/NOUISliderUtils';
import { FormsModule } from '@angular/forms';
import { DatepickerConfig } from '@app/common/modules/datepicker/model/datepicker-config.model';
import { PermFilterHistoryService } from '@permamonitor/permamonitor/views/filter-history.service';

export type PeriodEvolutionFilterOutput = DatetimeModel;

@Component({
  selector: 'cgdis-portal-permamonitor-period-evaluation-filter',
  standalone: true,
  imports: [
    DatepickerModule,
    TranslateModule,
    FilterTemplateComponent,
    SharedModule,
    SliderModule,
    FormsModule,
  ],
  templateUrl: './period-evolution-filter.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeriodEvolutionFilterComponent implements OnInit {
  @Input() hideButtons = false;
  isCurrentPeriod: boolean = true;
  // Adjust range for a 24-hour period in minutes
  sliderConfig: SliderConfig = {
    range: {
      min: 0, // start of the day in minutes
      max: 1380, // end of the day in minutes (24*60)
    },
    rangeConnection: true, // Single connection for a single handle slider
    step: 60, // Optional: adjust to control step interval
  };
  sliderCurrentTime: number[];
  sliderPointsFormatter: SliderFormatter;
  @Output() periodFilterOutput: EventEmitter<DatetimeModel> =
    new EventEmitter();
  @Output() periodFilterInit: EventEmitter<string> = new EventEmitter();
  isPlay: boolean = false;
  currentPeriodFilterOutput: string;
  currentDateInput: string;
  protected configuration: DatepickerConfig;
  private playInterval: NodeJS.Timer;
  private currentMinutesOfTheDay: number = 0;
  private lastPeriodEvolution: DatetimeModel;

  constructor(
    private dateTimeService: DatetimeService,
    private dateService: DateService,
    private translationService: TranslateService,
    private cd: ChangeDetectorRef,
    private permFilterHistory: PermFilterHistoryService,
  ) {}

  get currentDate(): DatetimeModel {
    const currentDateTime = this.dateTimeService.now();
    currentDateTime.minute = 0;
    return currentDateTime;
  }

  getDateModel() {
    return this.dateService.parse(this.currentDateInput);
  }

  ngOnInit() {
    this.initializeDatepicker();
    this.initializeSliderFormatter();
    this.handleLastPeriodEvolution();
    this.emitFilterOutput();
  }

  setSliderToCurrentTodayTime() {
    const currentDateTime = this.dateTimeService.now();
    this.setSliderValue(currentDateTime.hours * 60);
  }

  public onTimeSlotChanged(timeSlots: number[]): void {
    const minutes = timeSlots[0];

    const hours = Math.round(minutes / 60);
    if (minutes > 1380) {
      this.setSliderValue(1380); // Limit to 23:00
      return;
    }

    this.setSliderValue(minutes);
    const currentDateTime = this.dateTimeService.parse(
      this.currentPeriodFilterOutput,
      'DD/MM/YYYY HH:mm',
    );

    currentDateTime.hours = hours;

    this.updateCurrentPeriodFilterOutput(currentDateTime);

    this.emitFilterOutput();
  }

  togglePlay() {
    this.isPlay = !this.isPlay;

    const playSpeedInMs = 2000;

    if (this.isPlay) {
      let currentDateTime = this.dateTimeService.parse(
        this.currentPeriodFilterOutput,
        'DD/MM/YYYY HH:mm',
      );
      this.updateCurrentPeriodFilterOutput(currentDateTime);

      // Initialize the global currentMinutesOfTheDay
      this.currentMinutesOfTheDay = currentDateTime.hours * 60;

      // Immediately execute the playback logic
      this.executePlayback(currentDateTime);

      // Set up interval for subsequent executions
      clearInterval(this.playInterval);
      this.playInterval = setInterval(() => {
        this.executePlayback(currentDateTime);
      }, playSpeedInMs);
    } else {
      this.stopPlay();
    }
  }

  onSlide(event: any) {
    const snappedMinutes = Math.round(event[0] / 60) * 60;
    this.setSliderValue(snappedMinutes);
  }

  getCurrentSliderTimeInHoursAndMinutes() {
    const currentMinutes = Math.round(this.sliderCurrentTime[0]); // Ensure integer
    const hours = Math.floor(currentMinutes / 60);
    const mins = currentMinutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }

  setSliderValue(minutes: number) {
    const clampedMinutes = Math.round(minutes / 60) * 60; // Snap to nearest hour
    this.sliderCurrentTime = [clampedMinutes];
    this.cd.markForCheck();
  }

  changePeriod($event: DateModel): void {
    const date = this.dateService.format($event, 'DD/MM/YYYY');
    const dateAndSelectedTime =
      date + ' ' + this.getCurrentSliderTimeInHoursAndMinutes();
    const dateTime = this.dateTimeService.parse(
      dateAndSelectedTime,
      'DD/MM/YYYY HH:mm',
    );

    const todayDate = this.dateService.format(
      this.dateService.now(),
      'DD/MM/YYYY',
    );

    if (date === todayDate) {
      this.setSliderToCurrentTodayTime();
      const today = this.dateTimeService.now();
      this.updateCurrentPeriodFilterOutput(today);
    } else {
      this.updateCurrentPeriodFilterOutput(dateTime);
    }
    this.stopPlay();
    this.emitFilterOutput();
  }

  onEnter($event: any) {
    this.changePeriod(this.dateService.parse(this.currentDateInput));
  }

  addDays(dayCount: number) {
    let currentDateTime = this.dateTimeService.parse(
      this.currentPeriodFilterOutput,
      'DD/MM/YYYY HH:mm',
    );

    currentDateTime = this.dateTimeService.add(
      currentDateTime,
      dayCount,
      'day',
    );

    this.updateCurrentPeriodFilterOutput(currentDateTime);
    this.emitFilterOutput();
  }

  updateCurrentPeriodFilterOutput(dateTime: DatetimeModel) {
    dateTime.minute = 3;
    this.currentPeriodFilterOutput = this.dateTimeService.format(
      dateTime,
      'DD/MM/YYYY HH:mm',
    );

    this.currentDateInput = this.dateTimeService.format(dateTime, 'DD/MM/YYYY');
  }

  emitFilterOutput() {
    const filterDate = this.dateTimeService.parse(
      this.currentPeriodFilterOutput,
      'DD/MM/YYYY HH:mm',
    );
    filterDate.minute = 0;
    this.permFilterHistory.setLastFilter(filterDate);
    this.periodFilterOutput.emit(filterDate);
  }

  private initializeDatepicker() {
    this.configuration = new DatepickerConfig({
      autoClose: true,
      todayButton: true,
    });
  }

  private initializeSliderFormatter() {
    this.sliderPointsFormatter = new SliderTimeFormatter(
      this.translationService.instant('default.day_more'),
    );
  }

  private handleLastPeriodEvolution() {
    this.lastPeriodEvolution = this.permFilterHistory.getLastFilter();

    if (!this.lastPeriodEvolution) {
      this.setTodayPeriod();
      return;
    }

    const lastFilterIsToday = this.isLastFilterToday(this.lastPeriodEvolution);

    if (this.lastPeriodEvolution?.hours === undefined && lastFilterIsToday) {
      this.lastPeriodEvolution = this.dateTimeService.now();
      this.setSliderToCurrentTodayTime();
    } else if (this.lastPeriodEvolution?.hours === undefined) {
      this.setSliderValue(0);
    } else {
      this.setSliderValue(this.lastPeriodEvolution.hours * 60);
    }

    this.updateCurrentPeriodFilterOutput(this.lastPeriodEvolution);
  }

  private isLastFilterToday(lastPeriod: any): boolean {
    return (
      this.dateService.format(this.dateService.now(), 'DD/MM/YYYY') ===
      this.dateService.format(lastPeriod, 'DD/MM/YYYY')
    );
  }

  private setTodayPeriod() {
    this.updateCurrentPeriodFilterOutput(this.currentDate);
    this.setSliderToCurrentTodayTime();
  }

  private executePlayback(currentDateTime: DatetimeModel) {
    this.currentMinutesOfTheDay += 60;

    if (this.currentMinutesOfTheDay >= 1380) {
      this.currentMinutesOfTheDay = 0;
      currentDateTime = this.dateTimeService.add(currentDateTime, 1, 'day');
    }

    this.setSliderValue(this.currentMinutesOfTheDay);

    currentDateTime.hours = this.currentMinutesOfTheDay / 60;
    this.updateCurrentPeriodFilterOutput(currentDateTime);

    this.emitFilterOutput(); // Emit the new time
  }

  private stopPlay() {
    clearInterval(this.playInterval);
    this.isPlay = false;
  }
}
