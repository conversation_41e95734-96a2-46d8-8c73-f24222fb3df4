import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
  Output,
} from '@angular/core';
import {
  RegionFilterComponent,
  RegionFilterOutput,
} from '@permamonitor/permamonitor/common/filter/filters/region-filter/region-filter.component';
import {
  PeriodFilterComponent,
  PeriodFilterOutput,
} from '@permamonitor/permamonitor/common/filter/filters/period-filter/period-filter.component';
import {
  IsPrimaryFilterComponent,
  IsPrimaryFilterOutput,
} from '@permamonitor/permamonitor/common/filter/filters/is-primary-filter/is-primary-filter.component';
import {
  TypeFilterComponent,
  TypeFilterOutput,
} from '@permamonitor/permamonitor/common/filter/filters/type-filter/type-filter.component';
import {
  PeriodEvolutionFilterComponent,
  PeriodEvolutionFilterOutput,
} from '@permamonitor/permamonitor/common/filter/filters/period-evolution-filter/period-evolution-filter.component';
import { PermCategoryModel } from '@app/model/permamonitor/perm-category.model';
import { DynamicFilterGroupService } from '@permamonitor/permamonitor/common/filter/filters/dynamic-filter-group/dynamic-filter-group.service';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ActiveAssignmentsInformationModule } from '@app/general-information/active-assignments-information/active-assignments-information.module';
import { Category } from '@permamonitor/permamonitor/common/constants';

export type FilterType = keyof FilterTypes;

export interface FilterTypeOutputMap {
  region?: RegionFilterOutput;
  period?: PeriodFilterOutput;
  periodEvolution?: PeriodEvolutionFilterOutput;
  isFavorite?: IsPrimaryFilterOutput;
  type?: TypeFilterOutput;
}

export interface FilterTypes {
  region?: RegionFilterOutput;
  period?: PeriodFilterOutput;
  periodWithButtons?: PeriodFilterOutput;
  periodEvolution?: PeriodEvolutionFilterOutput;
  periodEvolutionNoButtons?: PeriodEvolutionFilterOutput;
  isFavorite?: IsPrimaryFilterOutput;
  typeIncsa?: TypeFilterOutput;
  typeAutres?: TypeFilterOutput;
  typeLocation?: TypeFilterOutput;
  type?: TypeFilterOutput;
}

@Component({
  selector: 'cgdis-portal-permamonitor-dynamic-filter-group',
  standalone: true,
  imports: [
    RegionFilterComponent,
    PeriodFilterComponent,
    IsPrimaryFilterComponent,
    TypeFilterComponent,
    PeriodEvolutionFilterComponent,
    ActiveAssignmentsInformationModule,
  ],
  providers: [DynamicFilterGroupService],
  templateUrl: './dynamic-filter-group.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DynamicFilterGroupComponent implements OnInit, OnDestroy {
  @Input() filters: FilterType[] = [];
  @Output() filterOutput = new EventEmitter<FilterTypeOutputMap>();

  currentFilterOutput: FilterTypes = {};
  protected autresTypes: PermCategoryModel[];
  protected incsaTypes: PermCategoryModel[];
  private initializationStatus: { [key in FilterType]?: boolean } = {};
  private _unsubscribe$ = new Subject<void>();

  constructor(
    private cd: ChangeDetectorRef,
    private dynamicFilterGroupService: DynamicFilterGroupService,
  ) {}

  ngOnInit() {
    if (this.filters.includes('typeIncsa')) {
      this.getSubcategories('INCSA', true);
    }

    if (this.filters.includes('typeAutres')) {
      this.getSubcategories('Autres', true);
    }
  }

  getSubcategories(category: Category, includeAll: boolean) {
    this.dynamicFilterGroupService
      .getAllSubcategoriesByCategory(category, includeAll)
      .pipe(takeUntil(this._unsubscribe$))
      .subscribe((value) => {
        if (category === 'INCSA') {
          this.incsaTypes = value;
        }
        if (category === 'Autres') {
          this.autresTypes = value;
        }
        this.cd.markForCheck();
      });
  }

  ngOnDestroy(): void {
    this._unsubscribe$.next();
    this._unsubscribe$.complete();
  }

  handleFilterOutput(filterType: FilterType, output: any) {
    this.currentFilterOutput[filterType] = output;

    if (this.filters.includes(filterType)) {
      this.markInitialized(filterType);
    } else {
      this.emitFilterOutput();
    }
  }

  emitFilterOutput(): void {
    this.cleanUpFilterOutput();

    this.filterOutput.emit({ ...this.currentFilterOutput });

    this.cd.markForCheck();
  }

  cleanUpFilterOutput() {
    if ('typeIncsa' in this.currentFilterOutput) {
      this.currentFilterOutput['type'] = this.currentFilterOutput['typeIncsa'];
      delete this.currentFilterOutput['typeIncsa'];
    }

    if ('typeAutres' in this.currentFilterOutput) {
      this.currentFilterOutput['type'] = this.currentFilterOutput['typeAutres'];
      delete this.currentFilterOutput['typeAutres'];
    }

    if ('periodEvolutionNoButtons' in this.currentFilterOutput) {
      this.currentFilterOutput['periodEvolution'] =
        this.currentFilterOutput['periodEvolutionNoButtons'];
      delete this.currentFilterOutput['periodEvolutionNoButtons'];
    }

    if ('periodWithButtons' in this.currentFilterOutput) {
      this.currentFilterOutput['period'] =
        this.currentFilterOutput['periodWithButtons'];
      delete this.currentFilterOutput['periodWithButtons'];
    }
  }

  setRegionFilterOutput(regionFilterOutput: RegionFilterOutput) {
    this.handleFilterOutput('region', {
      zone: regionFilterOutput.zone,
      group: regionFilterOutput.group,
    });
  }

  setIsPrimaryFilterOutput(isPrimaryFilterOutput: IsPrimaryFilterOutput) {
    this.handleFilterOutput('isFavorite', isPrimaryFilterOutput);
  }

  setTypeGisComFilterOutput(typeFilterOutput: TypeFilterOutput) {
    this.handleFilterOutput('typeAutres', typeFilterOutput);
  }

  setTypeRegionFilterOutput(typeFilterOutput: TypeFilterOutput) {
    this.handleFilterOutput('typeLocation', typeFilterOutput);
  }

  setPeriodFilterOutput(periodFilterOutput: PeriodFilterOutput) {
    this.handleFilterOutput('period', periodFilterOutput);
  }

  setPeriodWithButtonsFilterOutput(periodFilterOutput: PeriodFilterOutput) {
    this.handleFilterOutput('periodWithButtons', periodFilterOutput);
  }

  setPeriodEvolutionFilterOutput(
    periodEvolutionFilterOutput: PeriodEvolutionFilterOutput,
  ) {
    this.handleFilterOutput('periodEvolution', periodEvolutionFilterOutput);
  }

  setPeriodEvolutionNoButtonsFilterOutput(
    periodEvolutionFilterOutput: PeriodEvolutionFilterOutput,
  ) {
    this.handleFilterOutput(
      'periodEvolutionNoButtons',
      periodEvolutionFilterOutput,
    );
  }

  setIsSauvetageFilterOutput(isSauvetageFilterOutput: TypeFilterOutput) {
    this.handleFilterOutput('typeIncsa', isSauvetageFilterOutput);
  }

  private markInitialized(filterType: FilterType) {
    this.initializationStatus[filterType] = true;

    const allTrackedFiltersInitialized = this.filters.every(
      (filter) => this.initializationStatus[filter],
    );
    if (allTrackedFiltersInitialized) {
      this.emitFilterOutput();
    }
  }
}
