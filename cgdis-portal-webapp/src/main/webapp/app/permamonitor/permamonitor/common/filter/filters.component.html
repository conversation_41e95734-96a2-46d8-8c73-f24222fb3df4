<ng-container
  *ngTemplateOutlet="filterPanelTemplate; context: {filters: filters, filterOutput: filterOutput}"></ng-container>

<ng-template #filterPanelTemplate let-filterOutput="filterOutput" let-filters="filters">
<!--  <cgdis-portal-panel>-->
<!--    <div class="panel-body">-->
<!--      <cgdis-portal-permamonitor-dynamic-filter-group-->
<!--        (filterOutput)="setFilterOutput($event)"-->
<!--        [filters]="filters">-->
<!--      </cgdis-portal-permamonitor-dynamic-filter-group>-->
<!--    </div>-->
<!--  </cgdis-portal-panel>-->
<!--</ng-template>-->
<mat-expansion-panel [expanded]="true" class="accordion__group panel-body">
  <mat-expansion-panel-header class="general-information-header" role="heading">
    <mat-panel-title class="mb-0">
      <span [translate]="'permamonitor.filter.title'" class="accordion__title"></span>
    </mat-panel-title>
  </mat-expansion-panel-header>
  <ng-template matExpansionPanelContent>
    <cgdis-portal-permamonitor-dynamic-filter-group
      (filterOutput)="setFilterOutput($event)"
      [filters]="filters">
    </cgdis-portal-permamonitor-dynamic-filter-group>
  </ng-template>
</mat-expansion-panel>
