import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { DateModel, DateService } from '@eportal/core';
import { DatepickerModule } from '@app/common/modules/datepicker/datepicker.module';
import { TranslateModule } from '@ngx-translate/core';
import { FilterTemplateComponent } from '@app/common/modules/filter-template/filter-template.component';
import { SharedModule } from '@app/common/shared/shared.module';
import { DatepickerConfig } from '@app/common/modules/datepicker/model/datepicker-config.model';
import { PermFilterHistoryService } from '@permamonitor/permamonitor/views/filter-history.service';

export type PeriodFilterOutput = DateModel;

@Component({
  selector: 'cgdis-portal-permamonitor-period-filter',
  standalone: true,
  imports: [
    DatepickerModule,
    TranslateModule,
    FilterTemplateComponent,
    SharedModule,
  ],
  templateUrl: './period-filter.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PeriodFilterComponent implements OnInit {
  isCurrentPeriod: boolean = true;
  @Output() periodFilterOutput: EventEmitter<PeriodFilterOutput> =
    new EventEmitter();
  currentPeriodFilterOutput: string;
  @Input() showButtons = false;
  protected configuration: DatepickerConfig;

  constructor(
    private dateService: DateService,
    private permFilterHistory: PermFilterHistoryService,
  ) {}

  get currentDate(): string {
    return this.dateService.format(this.dateService.now(), 'DD/MM/YYYY');
  }

  ngOnInit() {
    const lastPeriod = this.dateService.format(
      this.permFilterHistory.getLastFilter(),
      'DD/MM/YYYY',
    );

    this.currentPeriodFilterOutput = lastPeriod ? lastPeriod : this.currentDate;
    this.isCurrentPeriod = this.currentDate === this.currentPeriodFilterOutput;
    this.configuration = new DatepickerConfig({
      autoClose: true,
    });
    this.emitFilterOutput();
  }

  changePeriod($event: DateModel): void {
    this.currentPeriodFilterOutput = this.dateService.format(
      $event,
      'DD/MM/YYYY',
    );
    this.emitFilterOutput();
  }

  changePeriodToCurrent(): void {
    this.isCurrentPeriod = !this.isCurrentPeriod;
    if (this.isCurrentPeriod) {
      this.currentPeriodFilterOutput = this.currentDate;
      this.emitFilterOutput();
    }
  }

  addDays(dayCount: number) {
    let currentDateTime = this.dateService.parse(
      this.currentPeriodFilterOutput,
      'DD/MM/YYYY',
    );

    currentDateTime = this.dateService.add(currentDateTime, dayCount, 'day');

    this.changePeriod(currentDateTime);
    this.emitFilterOutput();
  }

  emitFilterOutput() {
    this.permFilterHistory.setLastFilter(
      this.dateService.parse(this.currentPeriodFilterOutput),
    );
    this.periodFilterOutput.emit(
      this.dateService.parse(this.currentPeriodFilterOutput),
    );
  }
}
