import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { PojViewTableService } from '@permamonitor/permamonitor/common/view-selector/poj-view/poj-view-table/poj-view-table.service';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { InputModule } from '@app/common/modules/input/input.module';
import { EpDatatableModule, SearchOperator } from '@eportal/components';
import { SharedModule } from '@app/common/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { UntypedFormControl } from '@angular/forms';
import { Category } from '@permamonitor/permamonitor/common/constants';
import { DateService, DatetimeModel, DatetimeService } from '@eportal/core';
import { PojViewTableValueComponent } from '@permamonitor/permamonitor/common/view-selector/poj-view/poj-view-table/poj-view-table-value/poj-view-table-value.component';

@Component({
  selector: 'cgdis-portal-poj-view-table',
  standalone: true,
  imports: [
    DatatableModule,
    InputModule,
    EpDatatableModule,
    SharedModule,
    TranslateModule,
    PojViewTableValueComponent,
  ],
  templateUrl: './poj-view-table.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [PojViewTableService],
})
export class PojViewTableComponent implements OnInit, OnChanges {
  @Input() filterInput: any;
  @Input() category: Category;
  private regionControl = new UntypedFormControl();
  private startDateControl = new UntypedFormControl();
  private categoryNameControl = new UntypedFormControl();
  private subCategoryTecidControl = new UntypedFormControl();
  private filterInitialization = {
    startDate: false,
    type: false,
    region: false,
    category: false,
  };

  constructor(
    public pojViewTableService: PojViewTableService,
    private dateTimeService: DatetimeService,
    private dateService: DateService,
  ) {}

  ngOnChanges(changes: SimpleChanges) {
    if (changes.filterInput && this.filterInput) {
      const newStartDateValue = this.dateTimeService.format(
        this.filterInput.periodEvolution,
        'YYYY-MM-DD HH:mm:ss',
      );

      if (this.filterInput.type && this.filterInput.type.tecid) {
        this.subCategoryTecidControl.setValue(this.filterInput.type.tecid);
      } else {
        if (this.subCategoryTecidControl.value !== null) {
          this.subCategoryTecidControl.setValue(null);
        }
        this.filterInitialization.type = true;
        this.checkIfFiltersInitializedAndStartSearching();
      }

      if (this.startDateControl.value !== newStartDateValue) {
        this.startDateControl.setValue(newStartDateValue);
        this.filterInitialization.startDate = true;
        this.checkIfFiltersInitializedAndStartSearching();
      }

      const newRegionValue = this.filterInput.region.group
        ? this.filterInput.region.group.tecid
        : this.filterInput.region.zone.tecid;

      if (this.regionControl.value !== newRegionValue) {
        this.regionControl.setValue(newRegionValue);
        this.filterInitialization.region = true;
        this.checkIfFiltersInitializedAndStartSearching();
      }
    }
    if (changes.category && this.category) {
      this.categoryNameControl.setValue(this.category);
      this.filterInitialization.category = true;
      this.checkIfFiltersInitializedAndStartSearching();
    }
  }

  calculatePercent(value: number, total: number): string {
    if (total === 0 || value === 0) {
      return '0';
    }
    return parseFloat(((value / total) * 100).toFixed(1)).toString();
  }

  getPercent(avgReady: number, compareValue: number): string {
    return this.calculatePercent(avgReady, compareValue);
  }

  getCurrentHour() {
    return this.dateTimeService.format(
      this.filterInput.periodEvolution,
      'HH:mm',
    );
  }

  getDateString(dateTime: DatetimeModel) {
    return (
      this.dateService.dayName(dateTime) +
      ', ' +
      this.dateTimeService.format(dateTime, 'DD/MM/YY')
    );
  }

  ngOnInit() {
    this.pojViewTableService.addFilterWithFormControl(
      'baseEntityTecid',
      this.regionControl,
      { inUrl: false, operator: SearchOperator.eq },
    );
    this.pojViewTableService.addFilterWithFormControl(
      'startDate',
      this.startDateControl,
      { inUrl: false, operator: SearchOperator.eq },
    );
    this.pojViewTableService.addFilterWithFormControl(
      'categoryName',
      this.categoryNameControl,
      { inUrl: false, operator: SearchOperator.eq },
    );
    this.pojViewTableService.addFilterWithFormControl(
      'subCategoryTecid',
      this.subCategoryTecidControl,
      { inUrl: false, operator: SearchOperator.eq },
    );
  }

  private allFiltersInitialized(): boolean {
    return Object.values(this.filterInitialization).every(Boolean);
  }

  private checkIfFiltersInitializedAndStartSearching() {
    if (this.allFiltersInitialized()) {
      setTimeout(() => {
        this.pojViewTableService.setCanStartSearching();
      }, 250);
    }
  }
}
