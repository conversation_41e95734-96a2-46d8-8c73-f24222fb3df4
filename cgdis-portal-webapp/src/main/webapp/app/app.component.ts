import {
  ChangeDetector<PERSON><PERSON>,
  Component,
  <PERSON><PERSON><PERSON><PERSON>,
  OnInit,
  Renderer2,
} from '@angular/core';
import {
  ConfigService,
  ConfigurableComponent,
  DateService,
  IEPortalConfig,
} from '@eportal/core';
import { Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { MainService } from './common/shared/services/main.service';
import { ConnectedUserService } from './security/connected-user.service';
import { TranslationService } from './security/translation.service';
import _ from 'lodash';

import { UserPreference } from '@app/security/model/user-preferences.enum';
import { Title } from '@angular/platform-browser';
import { CGDISConfig } from '@app/model/cgdis-config.model';
import { SimplePopupDataModel } from '@app/common/modules/popup/simple-popup-data.model';
import { WarningLogasPopupComponent } from '@app/common/modules/popup/warning-logas-popup/warning-logas-popup.component';
import { ConnectedUser } from '@app/security/model/connected-user.model';
import { MatDialog } from '@angular/material/dialog';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';

@Component({
  selector: 'cgdis-portal-app',
  templateUrl: './app.component.html',
  styles: [],
  providers: [TranslationService],
})
export class AppComponent
  extends ConfigurableComponent
  implements OnDestroy, OnInit
{
  isMobile: boolean = false;

  private bodyClassesSubscription: Subscription;

  private connectedUserSubscription: Subscription;

  subscriptions: Subscription[] = [];

  constructor(
    _configService: ConfigService,
    private _translate: TranslateService,
    private mainService: MainService,
    private connectedUserService: ConnectedUserService,
    private renderer: Renderer2,
    private dateService: DateService,
    private dialog: MatDialog,
    //private toastr: ToastsManager,
    private titleService: Title,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    super(_configService);
    // this.toastr.setRootViewContainerRef(vcr);
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  /**
   * Application initialization
   */
  ngOnInit(): void {
    super.ngOnInit();
    this.initTest();
    this.bodyClassesSubscription = this.mainService
      .getBodyClasses()
      .subscribe((value) => {
        if (value != undefined) {
          this.addBodyClasses(value.add);
          this.removeBodyClasses(value.remove);
        }
      });
    this.connectedUserSubscription = this.connectedUserService
      .getPreferences()
      .subscribe((preferences) => {
        this.dateService.setDefaultFirstDayOfWeek(
          +preferences.defined[UserPreference.FIRST_DAY_OF_WEEK],
        );
      });
    this.connectedUserSubscription = this.connectedUserService
      .getConnectedUser()
      .subscribe((user) => {
        if (user.impersonated && user.userDetails != null) {
          this.alertLogas(user.userDetails);
        }
      });
  }

  ngOnDestroy(): void {
    super.ngOnDestroy();
    this.bodyClassesSubscription.unsubscribe();

    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  onConfigChange(config: IEPortalConfig): any {
    this.initTranslate(config);
  }

  /**
   * Add classes on body
   * @param {string[]} classes
   */
  addBodyClasses(classes: string[]) {
    if (classes != undefined) {
      _.each(classes, (oneClass) => {
        this.renderer.addClass(document.body, oneClass);
      });
    }
  }

  /**
   * Remove classes on body
   * @param {string[]} classes
   */
  removeBodyClasses(classes: string[]) {
    if (classes != undefined) {
      _.each(classes, (oneClass) => {
        this.renderer.removeClass(document.body, oneClass);
      });
    }
  }

  initTranslate(config: IEPortalConfig) {
    // Set the default language for translation strings, and the current language.
    this._translate.setDefaultLang(config.i18n.defaults.lang);

    const langs: string[] = [];

    config.i18n.availables.forEach((langConfig: any) => {
      langs.push(langConfig.lang);
    });

    this._translate.addLangs(langs);

    if (this.connectedUserService.getCurrentCgdisPortalUser()) {
      let lang =
        this.connectedUserService.getCurrentCgdisPortalUser().userPreference
          .defined['LANGUAGE'];
      this._translate.use(lang.toLowerCase());
    } else if (this._translate.getBrowserLang() !== undefined) {
      this._translate.use(this._translate.getBrowserLang());
    } else {
      this._translate.use(config.i18n.defaults.lang); // Set your language here
    }

    this.dateService.setLocale(this._translate.currentLang);
  }

  public initTest() {
    let configurations = this.connectedUserService.getConfiguration();

    // Retrieve the correct configuration
    let redirectConfiguration: CGDISConfig = _.find(configurations, [
      'key',
      'cgdis.environment.is-test',
    ]);
    if (redirectConfiguration && redirectConfiguration.value === 'true') {
      this.titleService.setTitle('Portail CGDIS [test]');
    } else {
      this.titleService.setTitle('Portail CGDIS');
    }
  }

  alertLogas(user: ConnectedUser) {
    this.dialog.open(WarningLogasPopupComponent, {
      data: new SimplePopupDataModel({
        title: 'logas.warning',
        message: 'logas.connected',
        messageParams: {
          userName: user.firstname + ' ' + user.lastname,
        },
      }),
      panelClass: this.isMobile ? 'simple-popup-mobile' : ['simple-popup'],
    });
  }
}
