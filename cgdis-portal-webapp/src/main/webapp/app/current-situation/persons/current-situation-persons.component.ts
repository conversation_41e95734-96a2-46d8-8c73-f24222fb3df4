import { take } from 'rxjs/operators';
import {
  ChangeDetectorRef,
  Component,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { CurrentSituationPersonsService } from './current-situation-persons.service';
import { DatetimeModel, DatetimeService, IPage } from '@eportal/core';
import { AvailablePerson } from '@app/model/person/person-availability';
import { CurrentSituationPersonContactInformationComponent } from '../person-contact-information/current-situation-person-contact-information.component';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { NgScrollbar } from 'ngx-scrollbar';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';

@Component({
  selector: 'cgdis-portal-current-situation-persons',
  templateUrl: './current-situation-persons.component.html',
  styleUrls: ['../current-situation.component.scss'],
  providers: [CurrentSituationPersonsService],
})
export class CurrentSituationPersonsComponent
  implements OnInit, OnChanges, OnDestroy
{
  static DEFAULT_PAGE_SIZE = 3000;
  /**
   * The scrollbar
   */

  /**
   * The entity id
   */
  @Input() entityId: number;

  /**
   * The entity name
   */
  @Input() entityName: string;

  @ViewChild(NgScrollbar, { static: true }) scrollable: NgScrollbar;

  private pageSize = CurrentSituationPersonsComponent.DEFAULT_PAGE_SIZE;

  /**
   * ALl persons available
   */
  public availablePersons: AvailablePerson[];

  /**
   * The number of person available (default: page size)
   */
  public maximumPersonAvailable = 10;

  /**
   * Model for search bar
   */
  public searchText: string;

  /**
   * The current date time model
   */
  public currentDateTime: DatetimeModel;

  public loadPerson = false;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private personsService: CurrentSituationPersonsService,
    private cd: ChangeDetectorRef,
    private popupService: SimplePopupService,
    private dateTimeService: DatetimeService,
    private breakpointObserver: BreakpointObserver,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  /**
   * On init (load available persons)
   */
  public ngOnInit(): void {
    this.loadAvailablePersons('');
    this.currentDateTime = this.dateTimeService.now();
  }

  /**
   * On changes (entity)
   * @param changes
   */
  public ngOnChanges(changes: SimpleChanges): void {
    if (changes.entityId) {
      this.entityId = changes.entityId.currentValue;
      if (this.scrollable) {
        this.scrollable.scrollTo({ top: 0 }).then(() => {});
      }
      this.pageSize = CurrentSituationPersonsComponent.DEFAULT_PAGE_SIZE;
      this.loadAvailablePersons('');
    }
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  /**
   *
   * @param search
   */
  private loadAvailablePersons(search: string): void {
    this.loadPerson = true;
    this.personsService
      .getPersonsAvailable(this.entityId, search, this.pageSize)
      .pipe(take(1))
      .subscribe((personsPage: IPage<AvailablePerson>) => {
        this.availablePersons = personsPage.content;
        this.maximumPersonAvailable = personsPage.totalElements;
        this.loadPerson = false;
        this.currentDateTime = this.dateTimeService.now();
        this.cd.markForCheck();
      });
  }

  /**
   * Show contact information for selected person
   * @param person
   */
  public selectPerson(person: AvailablePerson): void {
    let popupDialog = this.popupService.open(
      CurrentSituationPersonContactInformationComponent,
      {
        panelClass: !this.isMobile ? ['simple-popup'] : ['simple-popup-mobile'],
        data: {
          content: person,
        },
      },
    );
    popupDialog.afterOpened().subscribe(() => {
      document.body.style.overflow = 'hidden';
    });

    popupDialog.afterClosed().subscribe(() => {
      document.body.style.overflow = null;
    });
  }

  /**
   * Search persons
   */
  public executeSearch(): void {
    this.loadAvailablePersons(this.searchText);
  }

  /**
   * Search persons (infinite scroll)
   */
  public executeSearchNext(): void {
    if (this.pageSize <= this.maximumPersonAvailable) {
      this.pageSize += CurrentSituationPersonsComponent.DEFAULT_PAGE_SIZE;
      this.loadAvailablePersons(this.searchText);
    }
  }

  /**
   * Add time model to date time and return the result
   */
  public calcDateTime(datetime: DatetimeModel, duration: any): DatetimeModel {
    let hours = Math.floor(duration / 3600);
    let remainingSeconds = duration % 3600;
    let minutes = Math.floor(remainingSeconds / 60);
    let seconds = Math.floor(remainingSeconds % 60);

    let newDatetime = this.dateTimeService.add(datetime, minutes, 'minute');
    newDatetime = this.dateTimeService.add(newDatetime, hours, 'hours');
    newDatetime = this.dateTimeService.add(newDatetime, seconds, 'seconds');

    return newDatetime;
  }
}
