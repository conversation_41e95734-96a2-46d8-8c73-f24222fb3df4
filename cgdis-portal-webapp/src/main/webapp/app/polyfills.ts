/***************************************************************************************************
 * Load `$localize` onto the global scope - used if i18n tags appear in Angular templates.
 */
import "@angular/localize/init";
/**
 * Created by francois on 8/02/17.
 */
import "core-js/es6";
import "core-js/es6/reflect";
import "core-js/es7/reflect";
import "core-js/es6/symbol";
import "core-js/es6/object";
import "core-js/es7/object";
import "core-js/es6/function";
import "core-js/es6/parse-int";
import "core-js/es6/parse-float";
import "core-js/es6/number";
import "core-js/es6/math";
import "core-js/es6/string";
import "core-js/es6/date";
import "core-js/es6/array";
import "core-js/es6/regexp";
import "core-js/es6/map";
import "core-js/es6/set";
import "core-js/es7/array";
import "web-animations-js";
import "zone.js";
import "classlist.js";
// import 'intl';
// require('zone.js');
/*
if (process.env.ENV === 'production') {
    // Production
} else {
    // Development and test
    Error['stackTraceLimit'] = Infinity;
    // require('zone.js/dist/long-stack-trace-zone');
}
*/
