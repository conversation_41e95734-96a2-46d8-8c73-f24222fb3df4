import {
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { AuditModel } from '@app/model/audit/audit.model';
import { DatetimeModel, DatetimeService } from '@eportal/core';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';

@Component({
  selector: 'cgdis-portal-audit-management-model-detail',
  templateUrl: './audit-management-model-detail.component.html',
  styleUrls: ['./_audit-management-model-detail.scss'],
})
export class AuditManagementModelDetailComponent implements OnInit, OnDestroy {
  @Input() audit: AuditModel;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private dateTimeService: DatetimeService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit(): void {}

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  public getFormattedDateTime(date: DatetimeModel): string {
    if (date != null) {
      return this.dateTimeService.format(date, 'DD/MM/YYYY   HH:mm:ss');
    } else {
      return '-';
    }
  }
}
