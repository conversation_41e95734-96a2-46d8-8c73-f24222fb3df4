<div class="row ">

    <div  *ngIf="!audit; else auditPrestationInformation">

    </div>
    <ng-template #auditPrestationInformation>
        <div class="col-sm-12 ">
            <table>

                <tr *ngIf="audit.actionType === 'WEEK'">
                    <td class="list-value">
                        <span [innerHTML]="'audit.copy_prestation.week' | translate:{fromDate: getFormattedDate(audit.fromDatetime), targetDate : getFormattedDate(audit.targetDatetime) , servicePlanName : audit.servicePlanName, user : audit.personTecid.firstName+' '+audit.personTecid.lastName}"></span>
                    </td>
                </tr>

                <tr *ngIf="audit.actionType === 'DAY'">
                    <td class="list-value">
                        <span [innerHTML]="'audit.copy_prestation.day'  | translate:{fromDate: getFormattedDate(audit.fromDatetime), targetDate : getFormattedDate(audit.targetDatetime) , servicePlanName : audit.servicePlanName, user : audit.personTecid.firstName+' '+audit.personTecid.lastName}"></span>
                    </td>
                </tr>

                <tr *ngIf="audit.actionType === 'SLOT'">
                    <td class="list-value">
                        <span [innerHTML]="'audit.copy_prestation.slot'  | translate:{firstSlot: getFormattedTime(audit.fromDatetime), secondSlot : getFormattedTime(audit.targetDatetime) , servicePlanName : audit.servicePlanName, user : audit.personTecid.firstName+' '+audit.personTecid.lastName}"></span>
                    </td>
                </tr>

            </table>
        </div>
    </ng-template>
</div>