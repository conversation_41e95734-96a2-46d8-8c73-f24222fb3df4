import { Component, Input, OnInit } from '@angular/core';
import { AuditCopyPrestation } from '@app/model/audit/audit.model';
import { DateService, DatetimeModel, DatetimeService } from '@eportal/core';

@Component({
  selector: 'cgdis-portal-audit-management-copy-prestation-detail',
  templateUrl: './audit-management-copy-prestation-detail.component.html',
  styleUrls: ['./_audit-management-copy-prestation-detail.scss'],
})
export class AuditManagementCopyPrestationDetailComponent implements OnInit {
  @Input() audit: AuditCopyPrestation;

  constructor(
    private dateService: DateService,
    private dateTimeService: DatetimeService,
  ) {}

  ngOnInit(): void {}

  public getFormattedDate(date: DatetimeModel): string {
    if (date != null) {
      return this.dateTimeService.format(date, 'DD/MM/YYYY');
    } else {
      return '-';
    }
  }

  public getFormattedTime(date: DatetimeModel): string {
    if (date != null) {
      return this.dateTimeService.format(date, 'DD/MM/YYYY à HH:mm');
    } else {
      return '-';
    }
  }
}
