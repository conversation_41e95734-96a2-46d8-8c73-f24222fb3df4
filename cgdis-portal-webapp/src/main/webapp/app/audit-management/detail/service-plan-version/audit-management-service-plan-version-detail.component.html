<div class="row ">

    <div  *ngIf="!audit; else auditPrestationInformation">

    </div>
    <ng-template #auditPrestationInformation>
        <div class="col-sm-12 ">
            <table>
                <tr *ngIf="isMobile">
                    <td class="list-label">
                        <span>{{'audit.actionDateTime' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{getFormattedDateTime(audit.actionDatetime)}}</span>
                    </td>
                </tr>
                <tr *ngIf="isMobile">
                    <td class="list-label">
                        <span>{{'audit.person' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{audit.personTecid.lastName}} {{audit.personTecid.firstName}}</span>
                    </td>
                </tr>
                <tr *ngIf="isMobile">
                    <td class="list-label">
                        <span>{{'audit.type.title' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{'audit.type.'+audit.type | translate}}</span>
                    </td>
                </tr>
                <tr *ngIf="isMobile">
                    <td class="list-label">
                        <span>{{'audit.actionType.title' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{'audit.actionType.'+audit.actionType | translate}}</span>
                    </td>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'audit.service_plan_version.name' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{audit.versionLabel}}</span>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'audit.service_plan_version.spname' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{audit.servicePlanName}}</span>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'audit.service_plan_version.startDate' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{getFormattedDate(audit.startDate) | translate}}</span>
                    </td>
                </tr>

                <tr *ngIf="audit.endDate">
                    <td class="list-label">
                        <span>{{'audit.service_plan_version.endDate' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{getFormattedDate(audit.endDate) | translate}}</span>
                    </td>
                </tr>


              <tr>
                <td class="list-label">
                  <span>{{'audit.service_plan.type' | translate}}</span>
                </td>
                <td class="list-value">
                  <span>{{'service_plan.versions.type.' +audit.servicePlanType | translate}}</span>
                </td>
              </tr>


            </table>
        </div>
    </ng-template>
</div>
