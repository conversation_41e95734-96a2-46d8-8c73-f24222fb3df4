import {
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { AuditVersionModel } from '@app/model/audit/audit.model';
import {
  DateModel,
  DateService,
  DatetimeModel,
  DatetimeService,
} from '@eportal/core';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';

@Component({
  selector: 'cgdis-portal-audit-management-model-version-detail',
  templateUrl: './audit-management-model-version-detail.component.html',
  styleUrls: ['./_audit-management-model-version-detail.scss'],
})
export class AuditManagementModelVersionDetailComponent
  implements OnInit, OnDestroy
{
  @Input() audit: AuditVersionModel;
  dateFormat = 'DD/MM/YYYY';

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private dateService: DateService,
    private dateTimeService: DatetimeService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
        }),
    );
  }

  ngOnInit(): void {}

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  public getFormattedDate(date: DateModel): string {
    if (date != null) {
      return this.dateService.format(date, this.dateFormat);
    } else {
      return '-';
    }
  }

  public getFormattedDateTime(date: DatetimeModel): string {
    if (date != null) {
      return this.dateTimeService.format(
        date,
        this.dateFormat.concat('  HH:mm:ss'),
      );
    } else {
      return '-';
    }
  }
}
