<div class="row ">

  <div  *ngIf="!audit; else auditPrestationInformation">

  </div>
  <ng-template #auditPrestationInformation>
    <div class="col-sm-12 ">
      <table>

        <tr *ngIf="audit.actionType === 'SPLIT'">
          <td class="list-value">
            <span [innerHTML]="'audit.slot.split' | translate:{
            startDate: getFormattedTime(audit.startDatetime),
            endDate : getFormattedTime(audit.endDatetime) ,
            targetDate : getFormattedTime(audit.targetDatetime),
            user : audit.personTecid.firstName+' '+audit.personTecid.lastName,
            servicePlanName: audit.servicePlans[0].portalLabel
            }"></span>
          </td>
        </tr>

        <tr *ngIf="audit.actionType === 'MERGE'">
          <td class="list-value">
            <span [innerHTML]="'audit.slot.merge' | translate:{
            startDate: getFormattedDate(audit.startDatetime),
            endDate : getFormattedDate(audit.endDatetime) ,
            targetDate : getFormattedTime(audit.targetDatetime),
            user : audit.personTecid.firstName+' '+audit.personTecid.lastName,
            servicePlanName: audit.servicePlans[0].portalLabel
            }"></span></td>
        </tr>

      </table>
    </div>
  </ng-template>
</div>
