.list-label {
  padding-left: 1rem;
  font-weight: 500;
  @media (min-width: 768px) {
    padding-right: 3rem;
    padding-left: 6rem;
  }
  font-size: 1.2em;
  &::after {
    content: ':';
  }
}
.list-value {
  padding-left: 1rem;
  font-weight: 100;
  font-size: 1.2em;
  @media (min-width: 768px) {
    padding-left: 6rem;
  }
  b {
    font-weight: bold;
  }
}

table {
  margin-bottom: 10px;
}

td {
  padding-top: 0px;
  padding-bottom: 0px;
  @media (min-width: 768px) {
    height: 1em !important;
  }
  vertical-align: center;
}

ul {
  padding: 0;
  list-style-type: none;
}

tr {
  background-color: transparent !important;
  @media (max-width: 768px) {
    display: grid;
    margin-bottom: 1rem;
  }
}

strong {
  font-weight: 500;
}

b {
  font-weight: 500;
}
