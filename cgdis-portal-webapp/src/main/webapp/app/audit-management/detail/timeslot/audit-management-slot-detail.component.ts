import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
} from '@angular/core';
import { AuditTimeSlot } from '@app/model/audit/audit.model';
import { DateService, DatetimeModel, DatetimeService } from '@eportal/core';

@Component({
  selector: 'cgdis-portal-audit-management-slot-detail',
  templateUrl: './audit-management-slot-detail.component.html',
  styleUrls: ['./_audit-management-slot-detail.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AuditManagementSlotDetailComponent implements OnInit {
  @Input() audit: AuditTimeSlot;

  constructor(
    private dateService: DateService,
    private dateTimeService: DatetimeService,
  ) {}

  ngOnInit(): void {}

  public getFormattedDate(date: DatetimeModel): string {
    if (date != null) {
      return this.dateTimeService.format(date, 'DD/MM/YYYY HH:mm');
    } else {
      return '-';
    }
  }

  public getFormattedTime(date: DatetimeModel): string {
    if (date != null) {
      return this.dateTimeService.format(date, 'DD/MM/YYYY HH:mm');
    } else {
      return '-';
    }
  }
}
