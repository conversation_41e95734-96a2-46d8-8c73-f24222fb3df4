<div class="row ">

    <div  *ngIf="!audit; else auditPrestationInformation">

    </div>
    <ng-template #auditPrestationInformation>
        <div class="col-sm-4 ">
            <table>

                <tr *ngIf="isMobile">
                    <td class="list-label">
                        <span>{{'audit.actionDateTime' | translate}}</span>
                    </td>
                    <td class="list-value">
                        {{getFormattedDateTime(audit.actionDatetime)}}
                    </td>
                </tr>
                <tr *ngIf="isMobile">
                    <td class="list-label">
                        <span>{{'audit.person' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{audit.personTecid.lastName}} {{audit.personTecid.firstName}}</span>
                    </td>
                </tr>
                <tr *ngIf="isMobile">
                    <td class="list-label">
                        <span>{{'audit.type.title' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{'audit.type.'+audit.type | translate}}</span>
                    </td>
                </tr>
                <tr *ngIf="isMobile">
                    <td class="list-label">
                        <span>{{'audit.actionType.title' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{'audit.actionType.'+audit.actionType | translate}}</span>
                    </td>
                </tr>
                <tr>
                    <td class="list-label">
                        <span>{{'audit.prestation.slot' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span [translate]="'audit.prestation.date' " [translateParams]="{date: getFormattedDate(audit.prestationDate), startDatetime : getFormattedTime(audit.startDatetime) , endDatetime : getFormattedTime(audit.endDatetime)}"></span>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'audit.prestation.person' | translate}}</span>
                    </td>
                    <td class="list-value">
                      <span>{{ audit.prestationPersonTecid.lastName }} {{ audit.prestationPersonTecid.firstName }}
                        ({{ audit.prestationPersonTecid.cgdisRegistrationNumber }})</span>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'audit.prestation.service_plan' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{audit.servicePlanPortalLabel}}</span>
                    </td>
                </tr>

                <tr>
                    <td class="list-label">
                        <span>{{'audit.prestation.position' | translate}}</span>
                    </td>
                    <td class="list-value">
                        <span>{{audit.positionName}}</span>
                    </td>
                </tr>

              <tr>
                <td class="list-label">
                  <span>{{'audit.prestation.bypass_function' | translate}}</span>
                </td>
                <td class="list-value">
                  <span>{{audit.byPassFunction ? '✔' : '✘'}}</span>
                </td>
              </tr>


            </table>
        </div>
    </ng-template>
</div>
