import {
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { AuditPrestation } from '@app/model/audit/audit.model';
import {
  DateModel,
  DateService,
  DatetimeModel,
  DatetimeService,
} from '@eportal/core';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';

@Component({
  selector: 'cgdis-portal-audit-management-prestation-detail',
  templateUrl: './audit-management-prestation-detail.component.html',
  styleUrls: ['./_audit-management-prestation-detail.scss'],
})
export class AuditManagementPrestationDetailComponent
  implements OnInit, OnDestroy
{
  @Input() audit: AuditPrestation;
  dateFormat = 'DD/MM/YYYY';

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private dateTimeService: DatetimeService,
    private dateService: DateService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
          this.dateFormat = this.isMobile ? 'DD/MM/YY' : 'DD/MM/YYYY';
        }),
    );
  }

  ngOnInit(): void {}

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  public getFormattedDate(date: DateModel): string {
    if (date != null) {
      return this.dateService.format(date, this.dateFormat);
    } else {
      return '-';
    }
  }

  public getFormattedTime(date: DatetimeModel): string {
    if (date != null) {
      return this.dateTimeService.format(date, 'HH:mm');
    } else {
      return '-';
    }
  }

  public getFormattedDateTime(date: DatetimeModel): string {
    if (date != null) {
      return this.dateTimeService.format(
        date,
        this.dateFormat.concat('   HH:mm:ss'),
      );
    } else {
      return '-';
    }
  }
}
