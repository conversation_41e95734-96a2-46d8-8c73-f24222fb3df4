import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FormControl } from '@angular/forms';
import { DateModel } from '@eportal/core';
import { AuditTypeEnum } from '@app/model/audit/audit.enum';
import { AuditManagementAssignmentFunctionOperationalService } from '@app/audit-management/functionoperational/assignment/audit-management-assignment-function-operational.service';
import { AuditManagementModule } from '@app/audit-management/audit-management.module';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { AuditManagementAssignmentFunctionOperationalEntityColumnComponent } from '@app/audit-management/functionoperational/assignment/column/entity/audit-management-assignment-function-operational-entity-column.component';
import { AuditManagementAssignmentFunctionOperationalFunctionColumnComponent } from '@app/audit-management/functionoperational/assignment/column/fo/audit-management-assignment-function-operational-function-column.component';
import { AuditManagementAssignmentFunctionOperationalPersonColumnComponent } from '@app/audit-management/functionoperational/assignment/column/person/audit-management-assignment-function-operational-person-column.component';

@Component({
  selector: 'cgdis-portal-audit-management-assignment-function-operational',
  standalone: true,
  imports: [
    AuditManagementModule,
    DatatableModule,
    AuditManagementAssignmentFunctionOperationalEntityColumnComponent,
    AuditManagementAssignmentFunctionOperationalFunctionColumnComponent,
    AuditManagementAssignmentFunctionOperationalPersonColumnComponent,
  ],
  templateUrl:
    './audit-management-assignment-function-operational.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [AuditManagementAssignmentFunctionOperationalService],
})
export class AuditManagementAssignmentFunctionOperationalComponent {
  @Input() dateFormControl: FormControl<DateModel>;

  protected readonly AuditTypeEnum = AuditTypeEnum;

  constructor(
    protected auditService: AuditManagementAssignmentFunctionOperationalService,
  ) {}
}
