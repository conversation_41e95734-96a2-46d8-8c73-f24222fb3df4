import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
} from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { AuditAssignmentFunctionOperational } from '@app/model/audit/audit.model';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { EpDatatableModule } from '@eportal/components';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector:
    'cgdis-portal-audit-management-assignment-function-operational-person-column',
  standalone: true,
  imports: [EpDatatableModule, DatatableModule, TranslateModule],
  templateUrl:
    './audit-management-assignment-function-operational-person-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () => AuditManagementAssignmentFunctionOperationalPersonColumnComponent,
      ),
    },
  ],
})
export class AuditManagementAssignmentFunctionOperationalPersonColumnComponent extends CgdisDatatableColumnComponent<AuditAssignmentFunctionOperational> {
  @Input()
  auditService: CgdisDatatableService<AuditAssignmentFunctionOperational>;
}
