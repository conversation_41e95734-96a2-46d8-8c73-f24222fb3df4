import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
} from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { AuditAssignmentFunctionOperational } from '@app/model/audit/audit.model';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector:
    'cgdis-portal-audit-management-assignment-function-operational-function-column',
  standalone: true,
  imports: [DatatableModule, EpDatatableModule, TranslateModule],
  templateUrl:
    './audit-management-assignment-function-operational-function-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () =>
          AuditManagementAssignmentFunctionOperationalFunctionColumnComponent,
      ),
    },
  ],
})
export class AuditManagementAssignmentFunctionOperationalFunctionColumnComponent extends CgdisDatatableColumnComponent<AuditAssignmentFunctionOperational> {
  @Input()
  auditService: CgdisDatatableService<AuditAssignmentFunctionOperational>;
}
