<ep-datatable-column [flexGrow]="1" [columnName]="'entity.name'">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.function-operationals.assignments.entity'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <div class="text-wrap">{{ cast(context.row).entity.name }}</div>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-select-filter [allowClear]="true"
                                          [possibleValues]="possibleValues"
                                          [filterName]="'fo_assignment_entity'"
                                          [datatableService]="auditService"></cgdis-portal-datatable-select-filter>
  </ng-template>

</ep-datatable-column>
