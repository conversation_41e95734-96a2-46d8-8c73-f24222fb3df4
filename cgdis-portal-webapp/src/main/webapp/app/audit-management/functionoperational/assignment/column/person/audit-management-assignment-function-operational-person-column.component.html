<ep-datatable-column [flexGrow]="1" [columnName]="'assignPerson.cgdisRegistrationNumber'">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.function-operationals.assignments.person'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <div
      class="text-wrap">{{ cast(context.row).assignPerson.lastName + ' ' + cast(context.row).assignPerson.firstName }}
      ({{ cast(context.row).assignPerson.cgdisRegistrationNumber }})
    </div>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-text-filter [allowClear]="true"
                                        [filterName]="'fo_assignment_person'"
                                        [datatableService]="auditService"></cgdis-portal-datatable-text-filter>
  </ng-template>

</ep-datatable-column>
