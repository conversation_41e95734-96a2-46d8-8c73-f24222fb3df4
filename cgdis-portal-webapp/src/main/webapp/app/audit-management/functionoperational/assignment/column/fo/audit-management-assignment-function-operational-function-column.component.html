<ep-datatable-column [flexGrow]="1" [columnName]="'functionOperationalPortalLabel'">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.function-operationals.assignments.function'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <div class="text-wrap">{{ cast(context.row).functionOperationalPortalLabel }}</div>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-text-filter [allowClear]="true"

                                        [filterName]="'fo_assignment_functionOperationalPortalLabel'"
                                        [datatableService]="auditService"></cgdis-portal-datatable-text-filter>
  </ng-template>

</ep-datatable-column>
