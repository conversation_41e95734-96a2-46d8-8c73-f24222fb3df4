<cgdis-portal-cgdisdatatable
  [datatableService]="auditService"
  [id]="'audit-assignment-fos'"
  [sorts]="[{dir:'desc',prop:'tecid'}]"
  [showDetails]="'MOBILE'"
  [autoMobileFilters]="true"
  [class]="'entity__table'">

  <!-- Action date time -->
  <cgdis-portal-audit-action-date-column
    cgdisDatatableColumn
    [dateFormControl]="dateFormControl"
    [flexGrow]="1"
    [auditService]="auditService">
  </cgdis-portal-audit-action-date-column>


  <cgdis-portal-audit-person-tecid-column
    cgdisDatatableColumn
    [flexGrow]="1"
    [columnHiddenMobile]="true"
  ></cgdis-portal-audit-person-tecid-column>

  <cgdis-portal-audit-person-cgdis-registration-number-column
    cgdisDatatableColumn
    [columnHiddenMobile]="true"
    [flexGrow]="1"
    [auditService]="auditService"
  ></cgdis-portal-audit-person-cgdis-registration-number-column>


  <cgdis-portal-audit-management-assignment-function-operational-entity-column
    cgdisDatatableColumn
    [columnHiddenMobile]="true"
    [auditService]="auditService"
  ></cgdis-portal-audit-management-assignment-function-operational-entity-column>


  <cgdis-portal-audit-management-assignment-function-operational-function-column
    cgdisDatatableColumn
    [columnHiddenMobile]="true"
    [auditService]="auditService"
  ></cgdis-portal-audit-management-assignment-function-operational-function-column>

  <cgdis-portal-audit-management-assignment-function-operational-person-column
    cgdisDatatableColumn
    [columnHiddenMobile]="true"
    [auditService]="auditService"
  ></cgdis-portal-audit-management-assignment-function-operational-person-column>


  <!--  <cgdis-portal-admin-management-function-operational-list-intervention-type-column-->
  <!--    cgdisDatatableColumn-->
  <!--    [columnHiddenMobile]="true"-->
  <!--    [auditService]="auditService"-->
  <!--  ></cgdis-portal-admin-management-function-operational-list-intervention-type-column>-->


  <cgdis-portal-audit-action-type-column
    cgdisDatatableColumn
    [flexGrow]="1"
    [auditService]="auditService"
    [auditTypes]="[AuditTypeEnum.FO_ASSIGNMENT]"
  ></cgdis-portal-audit-action-type-column>


  <!--  <ng-template #template let-row="row">-->
  <!--    <cgdis-portal-audit-management-function-operational-export-detail-->
  <!--      [audit]="row"></cgdis-portal-audit-management-function-operational-export-detail>-->
  <!--  </ng-template>-->

</cgdis-portal-cgdisdatatable>

