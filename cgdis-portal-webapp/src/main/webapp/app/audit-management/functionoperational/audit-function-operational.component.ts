import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { MatAccordion } from '@angular/material/expansion';
import { ExpansionPanelComponent } from '@app/common/modules/expansion-panel/expansion-panel.component';
import { ExpansionPanelContentDirective } from '@app/common/modules/expansion-panel/expansion-panel-content.directive';
import { AuditManagementFunctionOperationalListComponent } from '@app/audit-management/functionoperational/list/audit-management-function-operational-list.component';
import { FormControl } from '@angular/forms';
import { DateModel, DateService } from '@eportal/core';
import { AuditManagementFunctionOperationalExportComponent } from '@app/audit-management/functionoperational/export/audit-management-function-operational-export.component';
import {
  AuditManagementAssignmentFunctionOperationalComponent
} from '@app/audit-management/functionoperational/assignment/audit-management-assignment-function-operational.component';

@Component({
  selector: 'cgdis-portal-audit-function-operational',
  standalone: true,
  imports: [
    MatAccordion,
    ExpansionPanelComponent,
    ExpansionPanelContentDirective,
    AuditManagementFunctionOperationalListComponent,
    AuditManagementFunctionOperationalExportComponent,
    AuditManagementAssignmentFunctionOperationalComponent,
  ],
  templateUrl: './audit-function-operational.component.html',
  styleUrl: './audit-function-operational.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AuditFunctionOperationalComponent implements OnInit {
  dateFormControl: FormControl<DateModel>;

  constructor(private dateService: DateService) {}

  ngOnInit(): void {
    this.dateFormControl = new FormControl<DateModel>(this.dateService.now());
  }
}
