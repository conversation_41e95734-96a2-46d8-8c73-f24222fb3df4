import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
} from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { AuditFunctionOperational } from '@app/model/audit/audit.model';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector:
    'cgdis-portal-admin-management-function-operational-list-portal-label-column',
  standalone: true,
  imports: [DatatableModule, EpDatatableModule, TranslateModule],
  templateUrl:
    './admin-management-function-operational-list-portal-label-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () => AdminManagementFunctionOperationalListPortalLabelColumnComponent,
      ),
    },
  ],
})
export class AdminManagementFunctionOperationalListPortalLabelColumnComponent extends CgdisDatatableColumnComponent<AuditFunctionOperational> {
  @Input() auditService: CgdisDatatableService<AuditFunctionOperational>;
}
