<ep-datatable-column [flexGrow]="1" [columnName]="'status'">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.function-operationals.list.status'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <div class="text-wrap">{{ 'function-operationals.status.' + cast(context.row).status | translate }}</div>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-select-filter [allowClear]="true"
                                          [possibleValues]="possiblValues"
                                          [filterName]="'fo_status'"
                                          [datatableService]="auditService"></cgdis-portal-datatable-select-filter>
  </ng-template>

</ep-datatable-column>
