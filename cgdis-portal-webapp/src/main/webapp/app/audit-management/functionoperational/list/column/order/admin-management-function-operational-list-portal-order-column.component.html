<ep-datatable-column [flexGrow]="1" [columnName]="'order'">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.function-operationals.list.order'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <div class="text-wrap">{{ cast(context.row).order }}</div>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-number-filter [allowClear]="true"
                                          [filterConfig]="filterConfig"
                                          [filterName]="'fo_order'"
                                          [datatableService]="auditService"></cgdis-portal-datatable-number-filter>
  </ng-template>

</ep-datatable-column>
