<ep-datatable-column [flexGrow]="1" [columnName]="'label'">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.function-operationals.list.label'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <div class="text-wrap">{{ cast(context.row).label }}</div>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-text-filter [allowClear]="true"
                                        [filterName]="'fo_label'"
                                        [datatableService]="auditService"></cgdis-portal-datatable-text-filter>
  </ng-template>

</ep-datatable-column>
