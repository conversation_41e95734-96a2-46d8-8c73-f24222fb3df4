import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
} from '@angular/core';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { TranslateModule } from '@ngx-translate/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { AuditFunctionOperational } from '@app/model/audit/audit.model';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';

@Component({
  selector:
    'cgdis-portal-admin-management-function-operational-list-label-column',
  standalone: true,
  imports: [DatatableModule, EpDatatableModule, TranslateModule],
  templateUrl:
    './admin-management-function-operational-list-label-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () => AdminManagementFunctionOperationalListLabelColumnComponent,
      ),
    },
  ],
})
export class AdminManagementFunctionOperationalListLabelColumnComponent extends CgdisDatatableColumnComponent<AuditFunctionOperational> {
  @Input() auditService: CgdisDatatableService<AuditFunctionOperational>;
}
