import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
} from '@angular/core';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import {
  EpDatatableModule,
  FilterConfig,
  SearchOperator,
} from '@eportal/components';
import { TranslateModule } from '@ngx-translate/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { AuditFunctionOperational } from '@app/model/audit/audit.model';

@Component({
  selector:
    'cgdis-portal-admin-management-function-operational-list-portal-order-column',
  standalone: true,
  imports: [DatatableModule, EpDatatableModule, TranslateModule],
  templateUrl:
    './admin-management-function-operational-list-portal-order-column.component.html',

  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () => AdminManagementFunctionOperationalListPortalOrderColumnComponent,
      ),
    },
  ],
})
export class AdminManagementFunctionOperationalListPortalOrderColumnComponent extends CgdisDatatableColumnComponent<AuditFunctionOperational> {
  @Input() auditService: CgdisDatatableService<AuditFunctionOperational>;
  filterConfig = new FilterConfig({
    operator: SearchOperator.eq,
  });
}
