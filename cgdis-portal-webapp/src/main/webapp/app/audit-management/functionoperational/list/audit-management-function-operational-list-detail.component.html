<div class="row ">

  <div *ngIf="!audit; else detail">
    -
  </div>
  <ng-template #detail>
    <div class="col-sm-12 ">
      <table>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.actionDateTime' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.actionDatetime | dateTimeFormat }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.person' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.personTecid.lastName }} {{ audit.personTecid.firstName }}
              ({{ audit.personTecid.cgdisRegistrationNumber }})</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.type.title' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ 'audit.type.' + audit.type | translate }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.actionType.title' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ 'audit.actionType.' + audit.actionType | translate }}</span>
          </td>
        </tr>


        <tr>
          <td class="list-label">
            <span [translate]="'audit.function-operationals.list.label'"></span>
          </td>
          <td class="list-value">
            <span>{{ audit.label }}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span [translate]="'audit.function-operationals.list.portallabel'"></span>
          </td>
          <td class="list-value">
            <span>{{ audit.portalLabel }}</span>
          </td>
        </tr>


        <tr>
          <td class="list-label">
            <span [translate]="'audit.function-operationals.list.status'"></span>
          </td>
          <td class="list-value">
            <span>{{ 'function-operationals.status.' + audit.status | translate }}</span>
          </td>
        </tr>


        <tr>
          <td class="list-label">
            <span [translate]="'audit.function-operationals.list.order'"></span>
          </td>
          <td class="list-value">
            <span>{{ audit.order }}</span>
          </td>
        </tr>


        <tr>
          <td class="list-label">
            <span [translate]="'audit.function-operationals.list.tags'"></span>
          </td>
          <td class="list-value">
            @if (audit.tags) {
              <ul>
                @for (oneTag of audit?.tags; track oneTag.tagName) {
                  <li><span>{{ oneTag.tagName }}</span></li>
                }

              </ul>
            } @else {
              -
            }


          </td>
        </tr>


        <tr>
          <td class="list-label">
            <span [translate]="'audit.function-operationals.list.interventiontype'"></span>
          </td>
          <td class="list-value">
            <span>{{ 'intervention.types.' + audit.interventionType.label | translate }}</span>
          </td>
        </tr>


      </table>
    </div>
  </ng-template>
</div>
