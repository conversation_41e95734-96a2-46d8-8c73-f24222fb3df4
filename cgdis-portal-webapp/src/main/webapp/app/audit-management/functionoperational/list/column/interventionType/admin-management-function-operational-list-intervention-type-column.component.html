<ep-datatable-column [flexGrow]="1" [columnName]="'interventionType.tecid'">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.function-operationals.list.interventiontype'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <div class="text-wrap">{{ 'intervention.types.' + cast(context.row).interventionType.label | translate }}</div>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-select-filter [allowClear]="true"
                                          [possibleValues]="allTypes"
                                          [filterName]="'fo_interventionType.tecid'"
                                          [datatableService]="auditService"></cgdis-portal-datatable-select-filter>
  </ng-template>

</ep-datatable-column>
