import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { AuditFunctionOperational } from '@app/model/audit/audit.model';
import { TranslateModule } from '@ngx-translate/core';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { FunctionOperationalStatus } from '@app/model/function-operational-status.enum';

@Component({
  selector:
    'cgdis-portal-admin-management-function-operational-list-portal-status-column',
  standalone: true,
  imports: [DatatableModule, EpDatatableModule, TranslateModule],
  templateUrl:
    './admin-management-function-operational-list-portal-status-column.component.html',

  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () => AdminManagementFunctionOperationalListPortalStatusColumnComponent,
      ),
    },
  ],
})
export class AdminManagementFunctionOperationalListPortalStatusColumnComponent
  extends CgdisDatatableColumnComponent<AuditFunctionOperational>
  implements OnInit
{
  @Input() auditService: CgdisDatatableService<AuditFunctionOperational>;

  possiblValues: FieldOption<FunctionOperationalStatus>[];

  ngOnInit(): void {
    this.possiblValues = Object.values(FunctionOperationalStatus).map((key) => {
      const status: FunctionOperationalStatus = FunctionOperationalStatus[key];
      return new FieldOption<FunctionOperationalStatus>({
        I18NLabel: 'function-operationals.status.' + status,
        value: status,
      });
    });
  }
}
