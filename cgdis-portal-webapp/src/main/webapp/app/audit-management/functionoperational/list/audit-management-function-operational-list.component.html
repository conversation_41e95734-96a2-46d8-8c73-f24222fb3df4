<cgdis-portal-cgdisdatatable
  [datatableService]="auditService"
  [id]="'audit-fos-list'"
  [sorts]="[{dir:'desc',prop:'tecid'}]"
  [showDetails]="'MOBILE'"
  [autoMobileFilters]="true"
  [class]="'entity__table'">

  <!-- Action date time -->
  <cgdis-portal-audit-action-date-column
    cgdisDatatableColumn
    [dateFormControl]="dateFormControl"
    [flexGrow]="1"
    [auditService]="auditService">
  </cgdis-portal-audit-action-date-column>


  <cgdis-portal-audit-person-tecid-column
    cgdisDatatableColumn
    [flexGrow]="2"
    [columnHiddenMobile]="true"
  ></cgdis-portal-audit-person-tecid-column>

  <cgdis-portal-audit-person-cgdis-registration-number-column
    cgdisDatatableColumn
    [columnHiddenMobile]="true"
    [flexGrow]="1"
    [auditService]="auditService"
  ></cgdis-portal-audit-person-cgdis-registration-number-column>

  <cgdis-portal-admin-management-function-operational-list-label-column
    cgdisDatatableColumn
    [columnHiddenMobile]="true"
    [auditService]="auditService"
  ></cgdis-portal-admin-management-function-operational-list-label-column>

  <cgdis-portal-admin-management-function-operational-list-portal-label-column
    cgdisDatatableColumn
    [columnHiddenMobile]="true"
    [auditService]="auditService"
  ></cgdis-portal-admin-management-function-operational-list-portal-label-column>


  <cgdis-portal-admin-management-function-operational-list-portal-status-column
    cgdisDatatableColumn
    [columnHiddenMobile]="true"
    [auditService]="auditService"
  ></cgdis-portal-admin-management-function-operational-list-portal-status-column>


  <cgdis-portal-admin-management-function-operational-list-portal-order-column
    cgdisDatatableColumn
    [columnHiddenMobile]="true"
    [auditService]="auditService"
  ></cgdis-portal-admin-management-function-operational-list-portal-order-column>

  <cgdis-portal-admin-management-function-operational-list-tags-column
    cgdisDatatableColumn
    [columnHiddenMobile]="true"
    [auditService]="auditService"
  ></cgdis-portal-admin-management-function-operational-list-tags-column>


  <cgdis-portal-admin-management-function-operational-list-intervention-type-column
    cgdisDatatableColumn
    [columnHiddenMobile]="true"
    [auditService]="auditService"
  ></cgdis-portal-admin-management-function-operational-list-intervention-type-column>


  <cgdis-portal-audit-action-type-column
    cgdisDatatableColumn
    [flexGrow]="1"
    [auditService]="auditService"
    [auditTypes]="[AuditTypeEnum.FO]"
  ></cgdis-portal-audit-action-type-column>


  <ng-template #template let-row="row">
    <cgdis-portal-audit-management-function-operational-list-detail
      [audit]="row"></cgdis-portal-audit-management-function-operational-list-detail>
  </ng-template>

</cgdis-portal-cgdisdatatable>

