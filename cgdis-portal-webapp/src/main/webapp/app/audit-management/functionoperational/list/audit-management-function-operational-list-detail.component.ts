import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { AuditFunctionOperational } from '@app/model/audit/audit.model';
import { NgIf } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from '@app/common/shared/shared.module';

@Component({
  selector: 'cgdis-portal-audit-management-function-operational-list-detail',
  standalone: true,
  imports: [NgIf, TranslateModule, SharedModule],
  templateUrl:
    './audit-management-function-operational-list-detail.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AuditManagementFunctionOperationalListDetailComponent {
  @Input() audit: AuditFunctionOperational;
}
