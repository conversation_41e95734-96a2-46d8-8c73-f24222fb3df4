<ep-datatable-column [flexGrow]="1" [columnName]="'portalLabel'">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.function-operationals.list.portallabel'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <div class="text-wrap">{{ cast(context.row).portalLabel }}</div>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-text-filter [allowClear]="true"
                                        [filterName]="'fo_portalLabel'"
                                        [datatableService]="auditService"></cgdis-portal-datatable-text-filter>
  </ng-template>

</ep-datatable-column>
