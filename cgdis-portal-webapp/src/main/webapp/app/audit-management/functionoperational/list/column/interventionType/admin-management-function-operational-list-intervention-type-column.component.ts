import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { TranslateModule } from '@ngx-translate/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { AuditFunctionOperational } from '@app/model/audit/audit.model';
import { InterventionTypesService } from '@app/common/shared/services/intervention-types.service';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { InterventionType } from '@app/model/intervention-type.model';

@Component({
  selector:
    'cgdis-portal-admin-management-function-operational-list-intervention-type-column',
  standalone: true,
  imports: [DatatableModule, EpDatatableModule, TranslateModule],
  templateUrl:
    './admin-management-function-operational-list-intervention-type-column.component.html',

  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () =>
          AdminManagementFunctionOperationalListInterventionTypeColumnComponent,
      ),
    },
  ],
})
export class AdminManagementFunctionOperationalListInterventionTypeColumnComponent
  extends CgdisDatatableColumnComponent<AuditFunctionOperational>
  implements OnInit
{
  @Input() auditService: CgdisDatatableService<AuditFunctionOperational>;

  protected allTypes: FieldOption<number>[] = [];

  constructor(private interventionTypeService: InterventionTypesService) {
    super();
  }

  ngOnInit(): void {
    this.interventionTypeService.getAll().subscribe({
      next: (interventionTypes: InterventionType[]) => {
        this.allTypes = interventionTypes.map((oneType) => {
          return new FieldOption<number>({
            I18NLabel: 'intervention.types.' + oneType.label,
            value: oneType.tecid,
          });
        });
      },
    });
  }
}
