<ep-datatable-column [flexGrow]="1" [columnName]="'tags'">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.function-operationals.list.tags'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>

    @if (cast(context.row).tags) {
      <div class="text-wrap">{{ displayedTags(cast(context.row).tags) }}</div>
    } @else {
      <div class="text-wrap">-</div>
    }
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-text-filter [allowClear]="true"
                                        [filterName]="'fo_tags'"
                                        [datatableService]="auditService"></cgdis-portal-datatable-text-filter>
  </ng-template>

</ep-datatable-column>
