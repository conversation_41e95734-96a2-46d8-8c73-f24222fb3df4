import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { AuditManagementModule } from '@app/audit-management/audit-management.module';
import { AuditManagementFunctionOperationalListService } from '@app/audit-management/functionoperational/list/audit-management-function-operational-list.service';
import { FormControl } from '@angular/forms';
import { DateModel } from '@eportal/core';
import { AuditTypeEnum } from '@app/model/audit/audit.enum';
import { AdminManagementFunctionOperationalListLabelColumnComponent } from '@app/audit-management/functionoperational/list/column/label/admin-management-function-operational-list-label-column.component';
import { AdminManagementFunctionOperationalListPortalLabelColumnComponent } from '@app/audit-management/functionoperational/list/column/portalLabel/admin-management-function-operational-list-portal-label-column.component';
import { AdminManagementFunctionOperationalListPortalStatusColumnComponent } from '@app/audit-management/functionoperational/list/column/status/admin-management-function-operational-list-portal-status-column.component';
import { AdminManagementFunctionOperationalListPortalOrderColumnComponent } from '@app/audit-management/functionoperational/list/column/order/admin-management-function-operational-list-portal-order-column.component';
import { AdminManagementFunctionOperationalListInterventionTypeColumnComponent } from '@app/audit-management/functionoperational/list/column/interventionType/admin-management-function-operational-list-intervention-type-column.component';
import { AdminManagementFunctionOperationalListTagsColumnComponent } from '@app/audit-management/functionoperational/list/column/tags/admin-management-function-operational-list-tags-column.component';
import { AuditManagementFunctionOperationalListDetailComponent } from '@app/audit-management/functionoperational/list/audit-management-function-operational-list-detail.component';

@Component({
  selector: 'cgdis-portal-audit-management-function-operational',
  standalone: true,
  imports: [
    DatatableModule,
    AuditManagementModule,
    AdminManagementFunctionOperationalListLabelColumnComponent,
    AdminManagementFunctionOperationalListPortalLabelColumnComponent,
    AdminManagementFunctionOperationalListPortalStatusColumnComponent,
    AdminManagementFunctionOperationalListPortalOrderColumnComponent,
    AdminManagementFunctionOperationalListInterventionTypeColumnComponent,
    AdminManagementFunctionOperationalListTagsColumnComponent,
    AuditManagementFunctionOperationalListDetailComponent,
  ],
  templateUrl: './audit-management-function-operational-list.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [AuditManagementFunctionOperationalListService],
})
export class AuditManagementFunctionOperationalListComponent {
  @Input() dateFormControl: FormControl<DateModel>;

  constructor(
    protected auditService: AuditManagementFunctionOperationalListService,
  ) {}

  protected readonly AuditTypeEnum = AuditTypeEnum;
}
