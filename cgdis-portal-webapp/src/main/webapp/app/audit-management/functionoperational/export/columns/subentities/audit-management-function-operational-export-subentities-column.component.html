<ep-datatable-column [flexGrow]="1" [columnName]="'subentities'">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.function-operationals.export.subentities'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <div class="text-wrap">{{ (cast(context.row).subentities ? 'default.yes' : 'default.no') | translate }}</div>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-select-filter [allowClear]="true"
                                          [possibleValues]="possibleValues"
                                          [filterName]="'fo_export_subentities'"
                                          [datatableService]="auditService"></cgdis-portal-datatable-select-filter>
  </ng-template>

</ep-datatable-column>
