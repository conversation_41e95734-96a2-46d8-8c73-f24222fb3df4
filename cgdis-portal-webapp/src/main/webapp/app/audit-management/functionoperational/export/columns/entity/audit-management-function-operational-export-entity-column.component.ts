import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { AuditFunctionOperationalExport } from '@app/model/audit/audit.model';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { TranslateModule } from '@ngx-translate/core';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { take } from 'rxjs/operators';
import { EntityService } from '@app/common/shared/services/entity.service';
import { EntityFieldOptionService } from '@app/common/shared/services/entity-field-option.service';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';

@Component({
  selector:
    'cgdis-portal-audit-management-function-operational-export-entity-column',
  standalone: true,
  imports: [DatatableModule, EpDatatableModule, TranslateModule],
  templateUrl:
    './audit-management-function-operational-export-entity-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () => AuditManagementFunctionOperationalExportEntityColumnComponent,
      ),
    },
  ],
})
export class AuditManagementFunctionOperationalExportEntityColumnComponent
  extends CgdisDatatableColumnComponent<AuditFunctionOperationalExport>
  implements OnInit
{
  @Input() auditService: CgdisDatatableService<AuditFunctionOperationalExport>;

  protected possibleValues: FieldGroupOption<string, number>[];

  constructor(
    private entitiesService: EntityService,
    private entityFieldOptionService: EntityFieldOptionService,
    private cd: ChangeDetectorRef,
  ) {
    super();
  }

  ngOnInit(): void {
    this.entitiesService
      .getAllForPermissions(['ROLE_PERMISSION_ADMIN_AUDIT_FO'])
      .pipe(take(1))
      .subscribe((allEntities) => {
        this.possibleValues =
          this.entityFieldOptionService.getFieldGroupOptions(allEntities, {
            addUnaffected: false,
            mapEntityToValue: (entity) => entity.tecid,
          }).groups;

        this.cd.markForCheck();
      });
  }
}
