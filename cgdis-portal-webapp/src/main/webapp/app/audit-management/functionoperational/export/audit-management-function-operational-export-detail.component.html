<div class="row ">

  <div *ngIf="!audit; else detail">
    -
  </div>
  <ng-template #detail>
    <div class="col-sm-12 ">
      <table>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.actionDateTime' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.actionDatetime | dateTimeFormat }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.person' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.personTecid.lastName }} {{ audit.personTecid.firstName }}
              ({{ audit.personTecid.cgdisRegistrationNumber }})</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.type.title' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ 'audit.type.' + audit.type | translate }}</span>
          </td>
        </tr>


        <tr>
          <td class="list-label">
            <span [translate]="'audit.function-operationals.export.entity'"></span>
          </td>
          <td class="list-value">
            <span>{{ audit.entity.name }}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span [translate]="'audit.function-operationals.export.subentities'"></span>
          </td>
          <td class="list-value">
            <span>{{ (audit.subentities ? 'default.yes' : 'default.no') | translate }}</span>
          </td>
        </tr>


      </table>
    </div>
  </ng-template>
</div>
