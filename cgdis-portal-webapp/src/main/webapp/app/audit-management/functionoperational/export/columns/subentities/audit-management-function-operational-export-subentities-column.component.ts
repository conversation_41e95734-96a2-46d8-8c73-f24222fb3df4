import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { AuditFunctionOperationalExport } from '@app/model/audit/audit.model';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector:
    'cgdis-portal-audit-management-function-operational-export-subentities-column',
  standalone: true,
  imports: [DatatableModule, EpDatatableModule, TranslateModule],
  templateUrl:
    './audit-management-function-operational-export-subentities-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () =>
          AuditManagementFunctionOperationalExportSubentitiesColumnComponent,
      ),
    },
  ],
})
export class AuditManagementFunctionOperationalExportSubentitiesColumnComponent
  extends CgdisDatatableColumnComponent<AuditFunctionOperationalExport>
  implements OnInit
{
  @Input() auditService: CgdisDatatableService<AuditFunctionOperationalExport>;

  protected possibleValues: FieldOption<boolean>[];

  constructor() {
    super();
  }

  ngOnInit(): void {
    this.possibleValues = [
      { I18NLabel: 'default.yes', value: true },
      { I18NLabel: 'default.no', value: false },
    ];
  }
}
