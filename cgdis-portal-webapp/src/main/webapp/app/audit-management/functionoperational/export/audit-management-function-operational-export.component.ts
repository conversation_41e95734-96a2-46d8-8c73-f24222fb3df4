import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FormControl } from '@angular/forms';
import { DateModel } from '@eportal/core';
import { AuditManagementFunctionOperationalExportService } from '@app/audit-management/functionoperational/export/audit-management-function-operational-export.service';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { AuditManagementModule } from '@app/audit-management/audit-management.module';
import { AuditTypeEnum } from '@app/model/audit/audit.enum';
import { AuditManagementFunctionOperationalExportEntityColumnComponent } from '@app/audit-management/functionoperational/export/columns/entity/audit-management-function-operational-export-entity-column.component';
import { AuditManagementFunctionOperationalExportSubentitiesColumnComponent } from '@app/audit-management/functionoperational/export/columns/subentities/audit-management-function-operational-export-subentities-column.component';
import {
  AuditManagementFunctionOperationalExportDetailComponent
} from '@app/audit-management/functionoperational/export/audit-management-function-operational-export-detail.component';

@Component({
  selector: 'cgdis-portal-audit-management-function-operational-export',
  standalone: true,
  imports: [
    DatatableModule,
    AuditManagementModule,
    AuditManagementFunctionOperationalExportEntityColumnComponent,
    AuditManagementFunctionOperationalExportSubentitiesColumnComponent,
    AuditManagementFunctionOperationalExportDetailComponent,
  ],
  templateUrl: './audit-management-function-operational-export.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [AuditManagementFunctionOperationalExportService],
})
export class AuditManagementFunctionOperationalExportComponent {
  @Input() dateFormControl: FormControl<DateModel>;

  protected readonly AuditTypeEnum = AuditTypeEnum;

  constructor(
    protected auditService: AuditManagementFunctionOperationalExportService,
  ) {}
}
