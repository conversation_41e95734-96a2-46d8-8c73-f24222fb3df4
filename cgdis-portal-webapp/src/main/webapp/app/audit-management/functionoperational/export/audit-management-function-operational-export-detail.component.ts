import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { AuditFunctionOperationalExport } from '@app/model/audit/audit.model';
import { NgIf } from '@angular/common';
import { SharedModule } from '@app/common/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'cgdis-portal-audit-management-function-operational-export-detail',
  standalone: true,
  imports: [NgIf, SharedModule, TranslateModule],
  templateUrl:
    './audit-management-function-operational-export-detail.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AuditManagementFunctionOperationalExportDetailComponent {
  @Input() audit: AuditFunctionOperationalExport;
}
