<!--<ng-container class="people-management">-->
<!--  <cgdis-portal-page-template>-->

<!--    <cgdis-portal-page-header [titleKey]="'layout.navigation.menu.items.audit.list.title'" [subtitleAlign]="true">-->
<!--    </cgdis-portal-page-header>-->
    <cgdis-portal-spinner [loading]="loading" [fullScreen]="true"></cgdis-portal-spinner>

    <mat-accordion *ngIf="!loading">

      <mat-expansion-panel class="accordion__group" [expanded]="true"
                           *cgdisPortalAuthRoles="['ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_PRESTATIONS']">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'audit.prestation_title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-audit-management-list-prestation
              [selectedServicePlanTecid]="selectedServicePlanTecid"
              [selectedSlot]="selectedSlot"
              [dateFormControl]="dateFormControl"
            ></cgdis-portal-audit-management-list-prestation>
          </div>
        </ng-template>
      </mat-expansion-panel>


      <mat-expansion-panel class="accordion__group" [expanded]="false"
                           *cgdisPortalAuthRoles="['ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_PRESTATIONS']">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'audit.copyprestations.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-audit-management-list-copyprestation
              [selectedServicePlanTecid]="selectedServicePlanTecid"
              [selectedSlot]="selectedSlot"
              [dateFormControl]="dateFormControl"
            ></cgdis-portal-audit-management-list-copyprestation>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <mat-expansion-panel class="accordion__group" [expanded]="false"
                           *cgdisPortalAuthRoles="['ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_PRESTATIONS']">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'audit.slot.title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-audit-management-list-slot
              [selectedServicePlanTecid]="selectedServicePlanTecid"
              [selectedSlot]="selectedSlot"
              [dateFormControl]="dateFormControl"
            ></cgdis-portal-audit-management-list-slot>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <mat-expansion-panel class="accordion__group" [expanded]="false"
                           *cgdisPortalAuthRoles="['ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_SERVICE_PLAN']">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'audit.serviceplan_title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-audit-management-list-service-plan
                                                             [dateFormControl]="dateFormControl"
                                                             [servicePlanFormControl]="servicePlanFormControl"
                                                             [servicePlans]="servicePlans"></cgdis-portal-audit-management-list-service-plan>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <mat-expansion-panel class="accordion__group" [expanded]="false"
                           *cgdisPortalAuthRoles="['ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_SP_MODEL']">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'audit.model_title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-audit-management-list-model [dateFormControl]="dateFormControl"
                                                      [actionTypes]="actionTypes"></cgdis-portal-audit-management-list-model>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <mat-expansion-panel class="accordion__group" [expanded]="false"
                           *cgdisPortalAuthRoles="['ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_LOGAS']">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'audit.logas_title'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-audit-management-list-logas [dateFormControl]="dateFormControl"
                                                      [actionTypes]="actionTypes"></cgdis-portal-audit-management-list-logas>
          </div>
        </ng-template>
      </mat-expansion-panel>

      <mat-expansion-panel class="accordion__group" [expanded]="false"
                           *cgdisPortalAuthRoles="['ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_ALLOWANCE']">
        <mat-expansion-panel-header class="general-information-header" role="heading">
          <mat-panel-title class="mb-0">
            <span class="accordion__title" [translate]="'audit.allowance'"></span>
          </mat-panel-title>
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="card-body">
            <cgdis-portal-audit-management-list-allowance [dateFormControl]="dateFormControl"
                                                      [actionTypes]="actionTypes"></cgdis-portal-audit-management-list-allowance>
          </div>
        </ng-template>
      </mat-expansion-panel>


    </mat-accordion>


<!--  </cgdis-portal-page-template>-->
<!--</ng-container>-->



