import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { TranslateService } from '@ngx-translate/core';
import { DatetimeModel, DatetimeService } from '@eportal/core';
import { AuditManagementListServicePlanService } from '@app/audit-management/list/service_plan/audit-management-list-service-plan.service';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import {
  AuditActionTypeEnum,
  AuditTypeEnum,
} from '@app/model/audit/audit.enum';
import { Subscription } from 'rxjs';
import { UntypedFormControl } from '@angular/forms';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { AuditManagementService } from '@app/audit-management/audit-management.service';

@Component({
  selector: 'cgdis-portal-audit-management-list-service-plan',
  templateUrl: './audit-management-list-service-plan.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [AuditManagementListServicePlanService],
})
export class AuditManagementListServicePlanComponent
  implements OnInit, OnDestroy
{
  actionTypes: FieldGroupOption<AuditTypeEnum, AuditActionTypeEnum>[];
  types: FieldOption<string>[];

  @Input() servicePlans: FieldOption<number>[];
  @Input() dateFormControl: UntypedFormControl;
  @Input() servicePlanFormControl: UntypedFormControl;
  subscription: Subscription;
  showFilter = false;

  numberOfFilters: number;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private translateService: TranslateService,
    public auditManagementListServicePlanService: AuditManagementListServicePlanService,
    private dateTimeService: DatetimeService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
    private auditManagementService: AuditManagementService,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit() {
    this.numberOfFilters = 0;
    this.loadTypes();

    this.subscriptions.push(
      this.auditManagementListServicePlanService
        .canExecuteFirstSearch()
        .subscribe(() => {
          this.numberOfFilters =
            this.auditManagementListServicePlanService.getNumberOfFilters();
        }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters =
      this.auditManagementListServicePlanService.getNumberOfFilters();
  }

  public getFormattedDate(date: DatetimeModel): string {
    if (date != null) {
      return this.dateTimeService.format(date, 'DD/MM/YYYY   HH:mm:ss');
    } else {
      return '-';
    }
  }

  private loadTypes(): void {
    const auditTypes = [AuditTypeEnum.PDS, AuditTypeEnum.VERSION_PDS];
    this.types =
      this.auditManagementService.getFieldOptionAuditTypes(auditTypes);
    this.actionTypes =
      this.auditManagementService.getFieldOptionActionTypes(auditTypes);
  }
}
