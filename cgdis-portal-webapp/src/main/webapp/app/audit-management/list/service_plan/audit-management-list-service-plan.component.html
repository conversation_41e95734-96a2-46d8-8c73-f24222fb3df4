<div class="accordion__panel">

    <cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
        <span *ngIf="!showFilter" before-icon>{{'service_plan.filter-link' | translate}} ({{numberOfFilters}})</span>
        <span *ngIf="showFilter" before-icon>{{'service_plan.filter-link-toclose' | translate}} ({{numberOfFilters}})</span>
    </cgdis-portal-button-link>
  <ng-container *ngIf="isMobile">
    <div class="row search-filter" [hidden]="!showFilter">
        <div class="col-md-2">
            <label class="form-label" [translate]="'audit.actionDateTime'"></label>
            <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="informations-datepicker-filter"
                    [customFormControl]="dateFormControl"
                    [initialValue]="dateFormControl.value"
                    [filterName]="'actionDate'"
                    [datatableService]="auditManagementListServicePlanService"></cgdis-portal-datatable-datepicker-filter></div>
        <div class="col-md-2">
            <label class="form-label" [translate]="'audit.type.title'"></label>
            <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true" [possibleValues]="types" [filterName]="'type'"
                                                  [datatableService]="auditManagementListServicePlanService"></cgdis-portal-datatable-select-filter>
        </div>
        <div class="col-md-2">
            <label class="form-label" [translate]="'audit.actionType.title'"></label>
            <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true" [possibleValues]="actionTypes" [filterName]="'actionType'"
                                                  [datatableService]="auditManagementListServicePlanService"></cgdis-portal-datatable-select-filter>
        </div>
        <div class="col-md-2">
            <label class="form-label" [translate]="'audit.type.PDS'"></label>
            <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" *ngIf="servicePlanFormControl.value" [allowClear]="true" [customFormControl]="servicePlanFormControl" [possibleValues]="servicePlans" [filterName]="'serviceplan'"
                                                  [datatableService]="auditManagementListServicePlanService"></cgdis-portal-datatable-select-filter>
            <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" *ngIf="!servicePlanFormControl.value" [allowClear]="true" [possibleValues]="servicePlans" [filterName]="'serviceplan'"
                                                  [datatableService]="auditManagementListServicePlanService"></cgdis-portal-datatable-select-filter>
        </div>
    </div>
  </ng-container>
            <cgdis-portal-cgdisdatatable
                    [datatableService]="auditManagementListServicePlanService"
                    [id]="'member-list'"
                    [sorts]="[{dir:'desc',prop:'tecid'}]"
                    [class]="'entity__table'">



                <!-- Action date time -->
                <ep-datatable-column [columnName]="'actionDatetime'" [flexGrow]="3">
                    <ng-template epDatatableHeader>
                        <span [translate]="'audit.actionDateTime'"></span>
                    </ng-template>

                    <ng-template epDatatableCell let-context>
                        {{getFormattedDate(context.row.actionDatetime)}}
                    </ng-template>
                    <ng-template epDatatableFilter>
                        <cgdis-portal-datatable-datepicker-filter
                                [customFormControl]="dateFormControl"
                                [initialValue]="dateFormControl.value"
                                [filterName]="'actionDate'"
                                [datatableService]="auditManagementListServicePlanService"></cgdis-portal-datatable-datepicker-filter>
                    </ng-template>
                </ep-datatable-column>

                <!-- User -->
                <ep-datatable-column [columnName]="'personTecid.lastName'" [flexGrow]="3">
                    <ng-template epDatatableHeader>
                        <span [translate]="'audit.person'"></span>
                    </ng-template>

                    <ng-template epDatatableCell let-context>
                      <span
                        class="text-wrap">{{ context.row.personTecid.lastName | defaultValue:'-' }} {{ context.row.personTecid.firstName | defaultValue:'-' }}</span>
                    </ng-template>
                </ep-datatable-column>

              <ep-datatable-column *ngIf="!isMobile" [columnName]="'personTecid.cgdisRegistrationNumber'"
                                   [flexGrow]="3">
                <ng-template epDatatableHeader>
                  <span [translate]="'audit.cgdisregistrationnumber'"></span>
                </ng-template>

                <ng-template epDatatableCell let-context>
                  {{ context.row.personTecid.cgdisRegistrationNumber }}
                </ng-template>

                <ng-template epDatatableFilter *ngIf="!isMobile">
                  <cgdis-portal-datatable-text-with-null-filter
                    [filterName]="'personTecid.cgdisRegistrationNumber'"
                    [datatableService]="auditManagementListServicePlanService"></cgdis-portal-datatable-text-with-null-filter>
                </ng-template>
              </ep-datatable-column>

                <!-- Audit type -->
                <ep-datatable-column [columnName]="'type'" [flexGrow]="3">
                    <ng-template epDatatableHeader>
                        <span [translate]="'audit.type.title'"></span>
                    </ng-template>

                    <ng-template epDatatableCell let-context>
                        {{('audit.type.'+context.row.type | translate) | defaultValue:'-'}}
                    </ng-template>
                    <ng-template epDatatableFilter>
                        <cgdis-portal-datatable-select-filter [allowClear]="true" [possibleValues]="types" [filterName]="'type'"
                                                              [datatableService]="auditManagementListServicePlanService"></cgdis-portal-datatable-select-filter>
                    </ng-template>
                </ep-datatable-column>

                <!-- Action type -->
                <ep-datatable-column [columnName]="'actionType'" [flexGrow]="3">
                    <ng-template epDatatableHeader>
                        <span [translate]="'audit.actionType.title'"></span>
                    </ng-template>

                    <ng-template epDatatableCell let-context>
                      <span
                        class="text-wrap">{{ ('audit.actionType.' + context.row.actionType | translate) | defaultValue:'-' }}</span>
                    </ng-template>
                    <ng-template epDatatableFilter>
                        <cgdis-portal-datatable-select-filter [allowClear]="true" [possibleValues]="actionTypes" [filterName]="'actionType'"
                                                              [datatableService]="auditManagementListServicePlanService"></cgdis-portal-datatable-select-filter>
                    </ng-template>
                </ep-datatable-column>

                <!-- Service Plan -->
                <ep-datatable-column [columnName]="'serviceplan'" [flexGrow]="3">
                    <ng-template epDatatableHeader>
                        <span [translate]="'audit.type.PDS'"></span>
                    </ng-template>

                    <ng-template epDatatableCell let-context>
                        <ul class="unstyled-list">
                            <li *ngFor="let servicePlan of context.row.servicePlans" class="block-intervention-types">
                                <span >{{servicePlan.portalLabel | defaultValue:'-'}}</span>
                            </li>
                        </ul>
                    </ng-template>
                    <ng-template epDatatableFilter>
                        <cgdis-portal-datatable-select-filter *ngIf="servicePlanFormControl.value" [allowClear]="true" [customFormControl]="servicePlanFormControl" [possibleValues]="servicePlans" [filterName]="'serviceplan'"
                                                              [datatableService]="auditManagementListServicePlanService"></cgdis-portal-datatable-select-filter>
                        <cgdis-portal-datatable-select-filter *ngIf="!servicePlanFormControl.value" [allowClear]="true" [possibleValues]="servicePlans" [filterName]="'serviceplan'"
                                                              [datatableService]="auditManagementListServicePlanService"></cgdis-portal-datatable-select-filter>

                    </ng-template>
                </ep-datatable-column>

                <ng-template #template let-row="row" >
                    <div *ngIf="row.tecid">
                        <cgdis-portal-audit-management-detail [audit]="row"></cgdis-portal-audit-management-detail>
                    </div>
                </ng-template>

            </cgdis-portal-cgdisdatatable>
</div>
