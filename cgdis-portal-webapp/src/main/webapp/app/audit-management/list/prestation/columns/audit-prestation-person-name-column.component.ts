import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { AuditPrestation } from '@app/model/audit/audit.model';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service'; // @ts-ignore

// @ts-ignore
@Component({
  selector: 'cgdis-portal-audit-prestation-person-name-column',
  templateUrl: './audit-prestation-person-name-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(() => AuditPrestationPersonNameColumnComponent),
    },
  ],
})
export class AuditPrestationPersonNameColumnComponent
  extends CgdisDatatableColumnComponent<AuditPrestation>
  implements OnInit
{
  @Input() auditService: CgdisDatatableService<AuditPrestation>;

  // protected servicePlans: FieldOption<number>[];
  // protected formControl = new FormControl<number>(undefined);

  ngOnInit(): void {}
}
