<ep-datatable-column [columnName]="'positionName'" [flexGrow]="3">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.prestation.headers.position'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <span class="text-wrap">{{ cast(context.row).positionName | defaultValue }}</span>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-text-with-null-filter
      [filterName]="'positionName'"
      [datatableService]="auditService"></cgdis-portal-datatable-text-with-null-filter>
  </ng-template>

</ep-datatable-column>

