<ep-datatable-column [columnName]="'prestationPersonTecid.cgdisRegistrationNumber'" [flexGrow]="2">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.prestation.headers.person'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <span class="text-wrap">{{ cast(context.row).prestationPersonTecid.cgdisRegistrationNumber | defaultValue }}</span>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-text-with-null-filter
      [filterName]="'prestationPersonTecid.cgdisRegistrationNumber'"
      [datatableService]="auditService"></cgdis-portal-datatable-text-with-null-filter>
  </ng-template>

</ep-datatable-column>

