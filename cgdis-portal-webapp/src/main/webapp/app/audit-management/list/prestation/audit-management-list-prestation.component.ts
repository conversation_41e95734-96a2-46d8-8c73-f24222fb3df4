import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { AuditManagementListPrestationService } from '@app/audit-management/list/prestation/audit-management-list-prestation.service';
import { AuditTypeEnum } from '@app/model/audit/audit.enum';
import { UntypedFormControl } from '@angular/forms';
import { DatetimeModel } from '@eportal/core';

@Component({
  selector: 'cgdis-portal-audit-management-list-prestation',
  templateUrl: './audit-management-list-prestation.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [AuditManagementListPrestationService],
})
export class AuditManagementListPrestationComponent {
  @Input() selectedServicePlanTecid: number;
  @Input() selectedSlot: { start: DatetimeModel; end: DatetimeModel };
  @Input() dateFormControl: UntypedFormControl;

  protected readonly AuditTypeEnum = AuditTypeEnum;
  constructor(
    public auditManagementListPrestationService: AuditManagementListPrestationService,
  ) {}
}
