import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core';
import { AuditModel, AuditPrestation } from '@app/model/audit/audit.model';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import _ from 'lodash';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import { ServicePlanService } from '@app/common/shared/services/service-plan.service';
import { take } from 'rxjs/operators';
import { ServicePlanNoJoin } from '@app/model/service-plan.model';
import { FormControl } from '@angular/forms';

@Component({
  selector: 'cgdis-portal-audit-prestation-person-column',
  templateUrl: './audit-prestation-person-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(() => AuditPrestationPersonColumnComponent),
    },
  ],
})
export class AuditPrestationPersonColumnComponent
  extends CgdisDatatableColumnComponent<AuditPrestation>
  implements OnInit
{
  @Input() auditService: CgdisDatatableService<AuditPrestation>;

  // protected servicePlans: FieldOption<number>[];
  // protected formControl = new FormControl<number>(undefined);

  constructor(private servicePlanService: ServicePlanService) {
    super();
  }

  ngOnInit(): void {}
}
