<div class="accordion__panel">


  <cgdis-portal-cgdisdatatable
    [datatableService]="auditManagementListPrestationService"
    [id]="'audit-list-slots'"
    [autoMobileFilters]="true"
    [sorts]="[{dir:'desc',prop:'tecid'}]"
    [class]="'entity__table'">


    <cgdis-portal-audit-action-date-column
      cgdisDatatableColumn
      [dateFormControl]="dateFormControl"
      [auditService]="auditManagementListPrestationService">
    </cgdis-portal-audit-action-date-column>


    <cgdis-portal-audit-person-tecid-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
    ></cgdis-portal-audit-person-tecid-column>

    <cgdis-portal-audit-person-cgdis-registration-number-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [auditService]="auditManagementListPrestationService"
    ></cgdis-portal-audit-person-cgdis-registration-number-column>


    <cgdis-portal-audit-service-plan-column
      cgdisDatatableColumn
      [selectedServicePlanTecid]="selectedServicePlanTecid"
      [columnHiddenMobile]="true"
      [auditService]="auditManagementListPrestationService"
    ></cgdis-portal-audit-service-plan-column>


    <cgdis-portal-audit-slot-start-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [selectedStart]="selectedSlot?.start"
      [auditService]="auditManagementListPrestationService"
    ></cgdis-portal-audit-slot-start-column>


    <cgdis-portal-audit-slot-end-column
      cgdisDatatableColumn
      [selectedEnd]="selectedSlot?.end"
      [columnHiddenMobile]="true"
      [auditService]="auditManagementListPrestationService"
    ></cgdis-portal-audit-slot-end-column>


    <cgdis-portal-audit-slot-target-start-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [auditService]="auditManagementListPrestationService"
    ></cgdis-portal-audit-slot-target-start-column>


    <cgdis-portal-audit-slot-target-end-column
      cgdisDatatableColumn
      [columnHiddenMobile]="true"
      [auditService]="auditManagementListPrestationService"
    ></cgdis-portal-audit-slot-target-end-column>

    <!--    <cgdis-portal-audit-prestation-position-column-->
    <!--      cgdisDatatableColumn-->
    <!--      [columnHiddenMobile]="true"-->
    <!--      [auditService]="auditManagementListPrestationService"-->
    <!--    ></cgdis-portal-audit-prestation-position-column>-->

    <!--    <cgdis-portal-audit-prestation-person-column-->
    <!--      cgdisDatatableColumn-->
    <!--      [columnHiddenMobile]="true"-->
    <!--      [auditService]="auditManagementListPrestationService"-->
    <!--    ></cgdis-portal-audit-prestation-person-column>-->


    <cgdis-portal-audit-action-type-column
      cgdisDatatableColumn
      [auditService]="auditManagementListPrestationService"
      [auditTypes]="[AuditTypeEnum.SLOT]"
    ></cgdis-portal-audit-action-type-column>


    <ng-template #template let-row="row">
      <div *ngIf="row.tecid">
        <cgdis-portal-audit-management-slot-detail
          [audit]="row"></cgdis-portal-audit-management-slot-detail>
      </div>
    </ng-template>


  </cgdis-portal-cgdisdatatable>
</div>
