<ep-datatable-column [columnName]="'endDatetime'" [flexGrow]="3">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.slot.headers.end'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <span class="text-wrap">{{ cast(context.row).endDatetime | dateTimeFormat }}</span>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-datepicker-filter
      [filterName]="'endDate'"
      [initialValue]="initialValue"
      [datatableService]="auditService"></cgdis-portal-datatable-datepicker-filter>


    <cgdis-portal-datatable-text-filter class="informations-datepicker-filter"
                                        [customFormControl]="endDatetimeFormControl"

                                        [filterName]="'endDatetime'"
                                        [hidden]="true"
                                        [filterConfig]="endDatetimeFilterConfig"
                                        [datatableService]="auditService"></cgdis-portal-datatable-text-filter>


  </ng-template>

</ep-datatable-column>

