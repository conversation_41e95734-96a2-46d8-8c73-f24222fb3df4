import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core';
import { AuditTimeSlot } from '@app/model/audit/audit.model';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { FormControl } from '@angular/forms';
import { DateModel, DatetimeModel, DatetimeService } from '@eportal/core';
import { FilterConfig, SearchOperator } from '@eportal/components';

@Component({
  selector: 'cgdis-portal-audit-slot-end-column',
  templateUrl: './audit-slot-end-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(() => AuditSlotEndColumnComponent),
    },
  ],
})
export class AuditSlotEndColumnComponent
  extends CgdisDatatableColumnComponent<AuditTimeSlot>
  implements OnInit
{
  @Input() auditService: CgdisDatatableService<AuditTimeSlot>;

  @Input() set selectedEnd(selectedEnd: DatetimeModel) {
    if (selectedEnd === undefined) {
      this.endDatetimeFormControl.setValue(undefined);
      this.initialValue = undefined;
    } else {
      this.initialValue = selectedEnd;
      this.endDatetimeFormControl.setValue(
        this.datetimeService.format(selectedEnd, 'YYYY-MM-DD HH:mm'),
      );
    }
  }

  protected initialValue: DateModel;

  protected endDatetimeFormControl = new FormControl<string>(undefined);
  protected endDatetimeFilterConfig = new FilterConfig({
    operator: SearchOperator.le,
  });

  constructor(private datetimeService: DatetimeService) {
    super();
  }

  ngOnInit(): void {}
}
