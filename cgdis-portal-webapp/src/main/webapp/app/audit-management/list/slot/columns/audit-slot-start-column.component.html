<ep-datatable-column [columnName]="'startDatetime'" [flexGrow]="3">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.slot.headers.start'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <span class="text-wrap">{{ cast(context.row).startDatetime | dateTimeFormat }}</span>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-datepicker-filter
      [filterName]="'startDate'"
      [initialValue]="initialValue"
      [datatableService]="auditService"></cgdis-portal-datatable-datepicker-filter>


    <cgdis-portal-datatable-text-filter class="informations-datepicker-filter"
                                        [customFormControl]="startDatetimeFormControl"

                                        [filterName]="'startDatetime'"
                                        [hidden]="true"
                                        [filterConfig]="startDatetimeFilterConfig"
                                        [datatableService]="auditService"></cgdis-portal-datatable-text-filter>


  </ng-template>

</ep-datatable-column>

