<ep-datatable-column [columnName]="'targetEndDatetime'" [flexGrow]="3">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.slot.headers.targetend'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <ng-container *ngIf="cast(context.row).actionType === AuditActionTypeEnum.SPLIT; else mergeCell">
      <span class="text-wrap">{{ cast(context.row).endDatetime | dateTimeFormat }}</span>
    </ng-container>

    <ng-template #mergeCell>
      <span class="text-wrap">{{ cast(context.row).startDatetime | dateTimeFormat }}</span>
    </ng-template>


  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-datepicker-filter
      [filterName]="'slotSplitMergeEndDate'"
      [initialValue]="initialValue"
      [datatableService]="auditService"></cgdis-portal-datatable-datepicker-filter>


    <cgdis-portal-datatable-text-filter class="informations-datepicker-filter"
                                        [customFormControl]="endDatetimeFormControl"

                                        [filterName]="'slotSplitMergeEndDatetime'"
                                        [hidden]="true"
                                        [filterConfig]="endDatetimeFilterConfig"
                                        [datatableService]="auditService"></cgdis-portal-datatable-text-filter>


  </ng-template>

</ep-datatable-column>



