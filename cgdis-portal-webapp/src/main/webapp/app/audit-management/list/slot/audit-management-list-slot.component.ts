import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { AuditTypeEnum } from '@app/model/audit/audit.enum';
import { UntypedFormControl } from '@angular/forms';
import { DatetimeModel } from '@eportal/core';
import { AuditManagementListTimeSlotService } from '@app/audit-management/list/slot/audit-management-list-slot.service';

@Component({
  selector: 'cgdis-portal-audit-management-list-slot',
  templateUrl: './audit-management-list-slot.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [AuditManagementListTimeSlotService],
})
export class AuditManagementListSlotComponent {
  @Input() selectedServicePlanTecid: number;
  @Input() selectedSlot: { start: DatetimeModel; end: DatetimeModel };
  @Input() dateFormControl: UntypedFormControl;

  protected readonly AuditTypeEnum = AuditTypeEnum;

  constructor(
    public auditManagementListPrestationService: AuditManagementListTimeSlotService,
  ) {}
}
