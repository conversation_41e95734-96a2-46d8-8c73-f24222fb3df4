import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core';
import { AuditCopyPrestation } from '@app/model/audit/audit.model';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { FormControl } from '@angular/forms';
import { DateModel, DatetimeModel, DatetimeService } from '@eportal/core';
import { FilterConfig, SearchOperator } from '@eportal/components';

@Component({
  selector: 'cgdis-portal-audit-copyprestation-start-column',
  templateUrl: './audit-copyprestation-start-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(() => AuditCopyPrestationStartColumnComponent),
    },
  ],
})
export class AuditCopyPrestationStartColumnComponent
  extends CgdisDatatableColumnComponent<AuditCopyPrestation>
  implements OnInit
{
  @Input() auditService: CgdisDatatableService<AuditCopyPrestation>;

  @Input() set selectedStart(selectedStart: DatetimeModel) {
    if (selectedStart === undefined) {
      this.startDatetimeFormControl.setValue(undefined);
      this.initialValue = undefined;
    } else {
      this.initialValue = selectedStart;
      this.startDatetimeFormControl.setValue(
        this.datetimeService.format(selectedStart, 'YYYY-MM-DD HH:mm'),
      );
    }
  }

  protected initialValue: DateModel;

  protected startDatetimeFormControl = new FormControl<string>(undefined);
  protected startDatetimeFilterConfig = new FilterConfig({
    operator: SearchOperator.ge,
  });

  constructor(private datetimeService: DatetimeService) {
    super();
  }

  ngOnInit(): void {}
}
