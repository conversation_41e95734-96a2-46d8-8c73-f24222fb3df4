<ep-datatable-column [columnName]="'fromDatetime'" [flexGrow]="3">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.copyprestations.headers.start'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <span class="text-wrap">{{ cast(context.row).fromDatetime | dateTimeFormat }}</span>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-datepicker-filter
      [filterName]="'fromDate'"
      [initialValue]="initialValue"
      [datatableService]="auditService"></cgdis-portal-datatable-datepicker-filter>


    <cgdis-portal-datatable-text-filter class="informations-datepicker-filter"
                                        [customFormControl]="startDatetimeFormControl"

                                        [filterName]="'fromDatetime'"
                                        [hidden]="true"
                                        [filterConfig]="startDatetimeFilterConfig"
                                        [datatableService]="auditService"></cgdis-portal-datatable-text-filter>


  </ng-template>

</ep-datatable-column>

