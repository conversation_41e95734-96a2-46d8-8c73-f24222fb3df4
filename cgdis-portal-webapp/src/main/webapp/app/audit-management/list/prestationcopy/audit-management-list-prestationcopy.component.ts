import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { DatetimeModel } from '@eportal/core';
import { AuditTypeEnum } from '@app/model/audit/audit.enum';
import { UntypedFormControl } from '@angular/forms';
import { AuditManagementListCopyPrestationService } from '@app/audit-management/list/prestationcopy/audit-management-list-prestationcopy.service';

@Component({
  selector: 'cgdis-portal-audit-management-list-copyprestation',
  templateUrl: './audit-management-list-prestationcopy.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [AuditManagementListCopyPrestationService],
})
export class AuditManagementListPrestationCopyComponent {
  @Input() selectedServicePlanTecid: number;
  @Input() selectedSlot: { start: DatetimeModel; end: DatetimeModel };
  @Input() dateFormControl: UntypedFormControl;

  protected readonly AuditTypeEnum = AuditTypeEnum;
  constructor(
    public auditManagementListPrestationService: AuditManagementListCopyPrestationService,
  ) {}
}
