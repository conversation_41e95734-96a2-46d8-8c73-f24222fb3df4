import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core';
import { AuditCopyPrestation } from '@app/model/audit/audit.model';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { FormControl } from '@angular/forms';
import { DateModel, DatetimeModel, DatetimeService } from '@eportal/core';
import { FilterConfig, SearchOperator } from '@eportal/components';

@Component({
  selector: 'cgdis-portal-audit-copyprestation-end-column',
  templateUrl: './audit-copyprestation-end-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(() => AuditCopyPrestationEndColumnComponent),
    },
  ],
})
export class AuditCopyPrestationEndColumnComponent
  extends CgdisDatatableColumnComponent<AuditCopyPrestation>
  implements OnInit
{
  @Input() auditService: CgdisDatatableService<AuditCopyPrestation>;

  @Input() set selectedEnd(selectedEnd: DatetimeModel) {
    if (selectedEnd === undefined) {
      this.endDatetimeFormControl.setValue(undefined);
      this.initialValue = undefined;
    } else {
      this.initialValue = selectedEnd;
      this.endDatetimeFormControl.setValue(
        this.datetimeService.format(selectedEnd, 'YYYY-MM-DD HH:mm'),
      );
    }
  }

  // protected servicePlans: FieldOption<number>[];
  // protected formControl = new FormControl<number>(undefined);
  endDatetimeFormControl = new FormControl<string>(undefined);
  endDatetimeFilterConfig = new FilterConfig({ operator: SearchOperator.le });
  initialValue: DateModel;

  constructor(private datetimeService: DatetimeService) {
    super();
  }

  ngOnInit(): void {}
}
