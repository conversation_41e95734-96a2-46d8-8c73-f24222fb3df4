import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { DatetimeModel, DatetimeService } from '@eportal/core';
import { AuditManagementListAllowanceService } from '@app/audit-management/list/allowance/audit-management-list-allowance.service';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import { take } from 'rxjs/operators';
import {
  AuditActionTypeEnum,
  AuditTypeEnum,
} from '@app/model/audit/audit.enum';
import { AuditManagementService } from '@app/audit-management/audit-management.service';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';

@Component({
  selector: 'cgdis-portal-audit-management-list-allowance',
  templateUrl: './audit-management-list-allowance.component.html',
  styles: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [AuditManagementListAllowanceService],
})
export class AuditManagementListAllowanceComponent implements OnInit , OnDestroy{
  actionTypes: FieldGroupOption<AuditTypeEnum, AuditActionTypeEnum>[];
  @Input() dateFormControl: UntypedFormControl;
  showFilter = false;
  isMobile: boolean = false;

  numberOfFilters: number;

  private subscriptions: Subscription[] = [];

  constructor(
    private translateService: TranslateService,
    public auditAllowanceService: AuditManagementListAllowanceService,
    private dateTimeService: DatetimeService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
    private auditManagementService: AuditManagementService,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit() {
    this.numberOfFilters = 0;
    this.loadTypes();
    this.auditAllowanceService
      .canExecuteFirstSearch()
      .pipe(take(1))
      .subscribe(() => {
        this.numberOfFilters = this.auditAllowanceService.getNumberOfFilters([
          'actionDate',
        ]);
      });
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters = this.auditAllowanceService.getNumberOfFilters([
      'actionDate',
    ]);
  }

  public getFormattedDate(date: DatetimeModel): string {
    if (date != null) {
      return this.dateTimeService.format(date, 'DD/MM/YYYY   HH:mm:ss');
    } else {
      return '-';
    }
  }

  private loadTypes(): void {
    const auditTypes = [AuditTypeEnum.ALLOWANCE_CONFIG];
    this.actionTypes =
      this.auditManagementService.getFieldOptionActionTypes(auditTypes);
  }
}
