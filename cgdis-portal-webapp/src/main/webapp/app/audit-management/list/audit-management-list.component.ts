import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import _ from 'lodash';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import { UntypedFormControl } from '@angular/forms';
import {
  DateModel,
  DateService,
  DatetimeModel,
  DatetimeService,
  TimeModel,
  TimeService,
} from '@eportal/core';
import { AuditActionTypeEnum } from '@app/model/audit/audit.enum';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { ServicePlanNoJoin } from '../../model/service-plan.model';
import { ServicePlanService } from '@app/common/shared/services/service-plan.service';
import { ConnectedUserService } from '@app/security/connected-user.service';

@Component({
  selector: 'cgdis-portal-audit-management-list',
  templateUrl: './audit-management-list.component.html',
  providers: [],
})
export class AuditManagementListComponent implements OnInit {
  loading: boolean;
  id: number;
  date: DateModel;
  start: TimeModel;
  end: TimeModel;
  slotStartFormControl = new UntypedFormControl();
  slotEndFormControl = new UntypedFormControl();
  dateFormControl = new UntypedFormControl();
  servicePlanFormControl = new UntypedFormControl();
  actionTypes: FieldOption<string>[];
  types: FieldOption<string>[];
  servicePlans: FieldOption<number>[];
  allServicePlanVersions: ServicePlanNoJoin[];
  private subscriptions: Subscription[] = [];
  protected selectedServicePlanTecid: number;
  protected selectedSlot: { start: DatetimeModel; end: DatetimeModel };

  constructor(
    private route: ActivatedRoute,
    private servicePlanService: ServicePlanService,
    private dateService: DateService,
    private timeService: TimeService,
    private cd: ChangeDetectorRef,
    private dateTimeService: DatetimeService,
    private connectedUserService: ConnectedUserService,
  ) {}

  ngOnInit() {
    this.loadTypes();
    this.loading = true;
    if (this.allServicePlanVersions === undefined) {
      this.allServicePlanVersions = [];
    }
    if (
      this.connectedUserService.hasAnyRoles([
        'ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_SERVICE_PLAN',
        'ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL_PRESTATIONS',
      ])
    ) {
      this.subscriptions.push(
        this.servicePlanService.getAllNoJoin().subscribe((results: any) => {
          this.allServicePlanVersions = results;
          this.initParams();
        }),
      );
    } else {
      this.initParams();
    }
  }

  private initParams() {
    this.route.queryParams.subscribe((params) => {
      this.id = parseInt(params['servicePlanId'], 10);
      this.id = isNaN(this.id) ? undefined : this.id;
      this.selectedServicePlanTecid = this.id;
      this.initializeSP();

      if (params['start'] != null) {
        this.start = this.timeService.parse(params['start'], 'HH:mm');
        this.end = this.timeService.parse(params['end'], 'HH:mm');
        this.date = this.dateService.parse(params['date'], 'DD/MM/YYYY');
        this.initSlotForm();
      }
      this.initSPCustomForm(this.id);
      this.cd.markForCheck();
    });
  }

  public getFormattedDate(date: DatetimeModel): string {
    if (date != null) {
      return this.dateTimeService.format(date, 'DD/MM/YYYY   HH:mm:ss');
    } else {
      return '-';
    }
  }

  private loadTypes(): void {
    let allAction: string[] = [];

    for (let n in AuditActionTypeEnum) {
      allAction.push(
        AuditActionTypeEnum[n as keyof typeof AuditActionTypeEnum],
      );
    }

    this.actionTypes = _.map(allAction, (oneType) => {
      return new FieldGroupOption({
        I18NLabel: 'audit.actionType.' + oneType,
        value: oneType,
      });
    });
  }

  private initializeSP() {
    this.servicePlans = _.map(this.allServicePlanVersions, (oneSP) => {
      return new FieldGroupOption({
        I18NLabel: oneSP.portalLabel,
        value: oneSP.tecid,
      });
    });
  }

  private initSPCustomForm(id: number): void {
    if (id != undefined) {
      this.servicePlanFormControl.setValue(id);
    }
    if (this.start == null) {
      this.dateFormControl.setValue(this.dateService.now());
    }
    this.loading = false;
  }

  private initSlotForm(): void {
    const start = this.dateTimeService.of(this.date, this.start);
    this.slotStartFormControl.setValue(
      this.dateTimeService.format(start, 'YYYY-MM-DD HH:mm'),
    );
    let end = this.dateTimeService.of(this.date, this.end);
    if (end.hours === 0 && end.minute === 0) {
      end = this.dateTimeService.add(end, 1, 'day');
    }
    this.slotEndFormControl.setValue(
      this.dateTimeService.format(end, 'YYYY-MM-DD HH:mm'),
    );
    this.selectedSlot = {
      start,
      end,
    };
    this.loading = false;
  }
}
