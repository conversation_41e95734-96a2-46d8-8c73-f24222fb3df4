import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { UntypedFormControl } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { DatetimeModel, DatetimeService } from '@eportal/core';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import {
  AuditActionTypeEnum,
  AuditTypeEnum,
} from '@app/model/audit/audit.enum';
import { AuditManagementListLogasService } from '@app/audit-management/list/logas/audit-management-list-logas.service';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { Subscription } from 'rxjs';
import { AuditManagementService } from '@app/audit-management/audit-management.service';

@Component({
  selector: 'cgdis-portal-audit-management-list-logas',
  templateUrl: './audit-management-list-logas.component.html',
  styles: [],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [AuditManagementListLogasService],
})
export class AuditManagementListLogasComponent implements OnInit, OnDestroy {
  actionTypes: FieldGroupOption<AuditTypeEnum, AuditActionTypeEnum>[];
  types: FieldOption<string>[];
  @Input() dateFormControl: UntypedFormControl;
  showFilter = false;

  numberOfFilters: number;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private translateService: TranslateService,
    public auditLogasService: AuditManagementListLogasService,
    private dateTimeService: DatetimeService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
    private auditManagementService: AuditManagementService,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 576px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit() {
    this.numberOfFilters = 0;
    this.loadTypes();

    this.subscriptions.push(
      this.auditLogasService.canExecuteFirstSearch().subscribe(() => {
        this.numberOfFilters = this.auditLogasService.getNumberOfFilters();
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters = this.auditLogasService.getNumberOfFilters();
  }

  public getFormattedDate(date: DatetimeModel): string {
    if (date != null) {
      return this.dateTimeService.format(date, 'DD/MM/YYYY   HH:mm:ss');
    } else {
      return '-';
    }
  }

  private loadTypes(): void {
    const auditTypes = [AuditTypeEnum.LOGAS];
    this.types =
      this.auditManagementService.getFieldOptionAuditTypes(auditTypes);
    this.actionTypes =
      this.auditManagementService.getFieldOptionActionTypes(auditTypes);
  }
}
