<div class="accordion__panel">

  <cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
    <span *ngIf="!showFilter" before-icon>{{'service_plan.filter-link' | translate}} ({{numberOfFilters}})</span>
    <span *ngIf="showFilter" before-icon>{{'service_plan.filter-link-toclose' | translate}} ({{numberOfFilters}})</span>
  </cgdis-portal-button-link>
  <ng-container *ngIf="isMobile">
    <div class="row search-filter" [hidden]="!showFilter">
      <div class="col-md-2">
        <label class="form-label" [translate]="'audit.actionDateTime'"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()" class="informations-datepicker-filter"
                                                  [customFormControl]="dateFormControl"
                                                  [initialValue]="dateFormControl.value"
                                                  [filterName]="'actionDate'"
                                                  [datatableService]="auditLogasService"></cgdis-portal-datatable-datepicker-filter>
      </div>

      <div class="col-md-2">
        <label class="form-label" [translate]="'audit.actionType.title'"></label>
        <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true" [possibleValues]="actionTypes"
                                              [filterName]="'actionType'"
                                              [datatableService]="auditLogasService"></cgdis-portal-datatable-select-filter>
      </div>
    </div>
  </ng-container>

  <cgdis-portal-cgdisdatatable
    [datatableService]="auditLogasService"
    [id]="'member-list'"
    [sorts]="[{dir:'desc',prop:'tecid'}]"
    [class]="'entity__table'">


    <!-- Action date time -->
    <ep-datatable-column [columnName]="'actionDatetime'" [flexGrow]="1">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.actionDateTime'"></span>
      </ng-template>

      <ng-template epDatatableCell let-context>
        {{getFormattedDate(context.row.actionDatetime)}}
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter
          [customFormControl]="dateFormControl"
          [initialValue]="dateFormControl.value"
          [filterName]="'actionDate'"
          [datatableService]="auditLogasService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- User -->
    <ep-datatable-column [columnName]="'personTecid.lastName'" [flexGrow]="1">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.person'"></span>
      </ng-template>

      <ng-template epDatatableCell let-context>
        <span class="text-wrap">{{ context.row.personTecid.lastName }} {{ context.row.personTecid.firstName }}</span>
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column *ngIf="!isMobile" [columnName]="'personTecid.cgdisRegistrationNumber'" [flexGrow]="1">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.cgdisregistrationnumber'"></span>
      </ng-template>

      <ng-template epDatatableCell let-context>
        {{ context.row.personTecid.cgdisRegistrationNumber }}
      </ng-template>

      <ng-template epDatatableFilter *ngIf="!isMobile">
        <cgdis-portal-datatable-text-with-null-filter
          [filterName]="'personTecid.cgdisRegistrationNumber'"
          [datatableService]="auditLogasService"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column [columnName]="'personTecid.iamNumber'" [flexGrow]="1">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.impersonatedUserName'"></span>
      </ng-template>

      <ng-template epDatatableCell let-context>
        {{context.row.impersonatedUserName}}
      </ng-template>
    </ep-datatable-column>


    <!-- Action type -->
    <ep-datatable-column [columnName]="'actionType'" [flexGrow]="1">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.actionType.title'"></span>
      </ng-template>

      <ng-template epDatatableCell let-context>
        <span class="text-wrap">{{ 'audit.actionType.' + context.row.actionType | translate }}</span>
      </ng-template>
      <ng-template epDatatableFilter>
        <cgdis-portal-datatable-select-filter [allowClear]="true" [possibleValues]="actionTypes"
                                              [filterName]="'actionType'"
                                              [datatableService]="auditLogasService"></cgdis-portal-datatable-select-filter>
      </ng-template>
    </ep-datatable-column>


  </cgdis-portal-cgdisdatatable>

</div>
