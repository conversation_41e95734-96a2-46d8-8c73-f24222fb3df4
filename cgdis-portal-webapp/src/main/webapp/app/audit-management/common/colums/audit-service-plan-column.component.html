<ep-datatable-column [columnName]="'serviceplan'" [flexGrow]="3">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.service_plan.column'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <ul class="unstyled-list">
      <li *ngFor="let servicePlan of cast(context.row).servicePlans" class="block-intervention-types">
        <span class="text-wrap">{{ servicePlan.portalLabel | defaultValue:'-' }}</span>
      </li>
    </ul>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-select-filter [allowClear]="true"
                                          [possibleValues]="servicePlans"
                                          [customFormControl]="formControl"
                                          [filterName]="'serviceplan'"
                                          [datatableService]="auditService"></cgdis-portal-datatable-select-filter>

  </ng-template>

</ep-datatable-column>

