<ep-datatable-column [columnName]="'personTecid.cgdisRegistrationNumber'" [flexGrow]="flexGrow">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.cgdisregistrationnumber'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    {{ cast(context.row).personTecid.cgdisRegistrationNumber }}
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-text-with-null-filter
      [filterName]="'personTecid.cgdisRegistrationNumber'"
      [allowClear]="true"
      [datatableService]="auditService"></cgdis-portal-datatable-text-with-null-filter>
  </ng-template>
</ep-datatable-column>
