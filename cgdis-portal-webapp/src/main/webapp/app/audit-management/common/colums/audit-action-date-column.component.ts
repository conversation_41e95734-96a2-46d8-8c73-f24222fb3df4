import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
} from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { FormControl } from '@angular/forms';
import { DateModel } from '@eportal/core';
import { Audit } from '@app/model/audit/audit.model';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';

@Component({
  selector: 'cgdis-portal-audit-action-date-column',
  templateUrl: './audit-action-date-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(() => AuditActionDateColumnComponent),
    },
  ],
})
export class AuditActionDateColumnComponent extends CgdisDatatableColumnComponent<Audit> {
  @Input() dateFormControl: FormControl<DateModel>;
  @Input() auditService: CgdisDatatableService<Audit>;
  @Input() flexGrow = 3;
}
