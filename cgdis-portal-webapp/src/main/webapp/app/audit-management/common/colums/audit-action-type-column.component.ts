import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { Audit } from '@app/model/audit/audit.model';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import {
  AuditActionTypeEnum,
  AuditTypeEnum,
} from '@app/model/audit/audit.enum';
import { AuditManagementService } from '@app/audit-management/audit-management.service';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';

@Component({
  selector: 'cgdis-portal-audit-action-type-column',
  templateUrl: './audit-action-type-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(() => AuditActionTypeColumnComponent),
    },
  ],
})
export class AuditActionTypeColumnComponent
  extends CgdisDatatableColumnComponent<Audit>
  implements OnInit
{
  @Input() auditTypes: AuditTypeEnum[];

  @Input() auditService: CgdisDatatableService<Audit>;

  @Input() flexGrow = 3;

  actionTypes: FieldGroupOption<AuditTypeEnum, AuditActionTypeEnum>[];

  constructor(private auditManagementService: AuditManagementService) {
    super();
  }

  ngOnInit(): void {
    this.actionTypes = this.auditManagementService.getFieldOptionActionTypes(
      this.auditTypes,
    );
  }
}
