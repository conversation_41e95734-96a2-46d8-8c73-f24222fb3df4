<ep-datatable-column [columnName]="'actionDatetime'" [flexGrow]="flexGrow">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.actionDateTime'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    {{ cast(context.row).actionDatetime | dateTimeFormat }}
  </ng-template>
  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-datepicker-filter
      [customFormControl]="dateFormControl"
      [initialValue]="dateFormControl.value"
      [filterName]="'actionDate'"
      [datatableService]="auditService"></cgdis-portal-datatable-datepicker-filter>
  </ng-template>
</ep-datatable-column>
