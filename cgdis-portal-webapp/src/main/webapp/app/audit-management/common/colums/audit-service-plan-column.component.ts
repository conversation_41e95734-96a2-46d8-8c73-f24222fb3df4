import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core';
import { AuditModel } from '@app/model/audit/audit.model';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import { ServicePlanService } from '@app/common/shared/services/service-plan.service';
import { take } from 'rxjs/operators';
import { ServicePlanNoJoin } from '@app/model/service-plan.model';
import { FormControl } from '@angular/forms';

@Component({
  selector: 'cgdis-portal-audit-service-plan-column',
  templateUrl: './audit-service-plan-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(() => AuditServicePlanColumnComponent),
    },
  ],
})
export class AuditServicePlanColumnComponent
  extends CgdisDatatableColumnComponent<AuditModel>
  implements OnInit
{
  @Input() auditService: CgdisDatatableService<AuditModel>;

  @Input() set selectedServicePlanTecid(value: number) {
    if (this.formControl.value === value) {
      return;
    }
      this.formControl.setValue(value);
  }

  protected servicePlans: FieldOption<number>[];
  protected formControl = new FormControl<number>(undefined);

  constructor(private servicePlanService: ServicePlanService) {
    super();
  }

  ngOnInit(): void {
    this.load();
  }

  private load() {
    this.servicePlanService
      .getAllNoJoin()
      .pipe(take(1))
      .subscribe({
        next: (results: ServicePlanNoJoin[]) => {
          this.servicePlans = results.map((oneSP) => {
            return new FieldGroupOption({
              I18NLabel: oneSP.portalLabel,
              value: oneSP.tecid,
            });
          });
        },
      });
  }
}
