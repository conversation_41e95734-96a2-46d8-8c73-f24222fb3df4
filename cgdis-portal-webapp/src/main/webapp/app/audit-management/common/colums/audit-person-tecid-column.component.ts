import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
} from '@angular/core';
import { Audit } from '@app/model/audit/audit.model';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';

@Component({
  selector: 'cgdis-portal-audit-person-tecid-column',
  templateUrl: './audit-person-tecid-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(() => AuditPersonTecidColumnComponent),
    },
  ],
})
export class AuditPersonTecidColumnComponent extends CgdisDatatableColumnComponent<Audit> {
  @Input() flexGrow = 3;
}
