<ep-datatable-column [flexGrow]="flexGrow" [columnName]="'actionType'">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.actionType.title'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <div class="text-wrap">{{ 'audit.actionType.' + cast(context.row).actionType | translate }}</div>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-select-filter [allowClear]="true" [possibleValues]="actionTypes"
                                          [filterName]="'actionType'"
                                          [datatableService]="auditService"></cgdis-portal-datatable-select-filter>
  </ng-template>

</ep-datatable-column>
