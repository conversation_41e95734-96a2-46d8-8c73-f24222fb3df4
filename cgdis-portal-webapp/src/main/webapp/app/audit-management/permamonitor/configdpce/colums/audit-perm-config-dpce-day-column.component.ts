import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import {
  AuditPermConfigDpceCopyModel,
  AuditPermConfigDpceModel,
} from '@app/model/audit/audit.model';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { FormControl } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { PermConfigDayType } from '@app/common/types/PermConfigDayType';

@Component({
  selector: 'cgdis-portal-audit-perm-config-dpce-day-column',
  standalone: true,
  imports: [DatatableModule, EpDatatableModule, TranslateModule],
  templateUrl: './audit-perm-config-dpce-day-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(() => AuditPermConfigDpceDayColumnComponent),
    },
  ],
})
export class AuditPermConfigDpceDayColumnComponent
  extends CgdisDatatableColumnComponent<AuditPermConfigDpceModel>
  implements OnInit
{
  @Input() auditService: CgdisDatatableService<AuditPermConfigDpceCopyModel>;

  protected formControl = new FormControl<number>(undefined);
  protected dayValues: FieldOption<PermConfigDayType>[] = [];

  ngOnInit(): void {
    this.initDayValues();
  }

  private initDayValues() {
    this.dayValues = Object.keys(PermConfigDayType).map((key) => {
      return new FieldOption({
        value: key as unknown as PermConfigDayType,
        I18NLabel: 'audit.permamonitor.configdpce.day.' + key,
      });
    });
  }
}
