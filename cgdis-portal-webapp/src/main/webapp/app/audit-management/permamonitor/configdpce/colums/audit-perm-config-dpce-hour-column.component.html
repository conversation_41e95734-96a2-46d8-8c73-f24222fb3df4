<ep-datatable-column [flexGrow]="3" [columnName]="columnName">
  <ng-template epDatatableHeader>
    <span [translate]="headerKey"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <div style="white-space: pre-line;">{{ getValue(context.row) | number:'2.0' }}</div>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-select-filter
      [customFormControl]="formControl"
      [filterName]="filterName"
      [allowClear]="true"
      [possibleValues]="hours"
      [datatableService]="auditService"></cgdis-portal-datatable-select-filter>
  </ng-template>
</ep-datatable-column>
