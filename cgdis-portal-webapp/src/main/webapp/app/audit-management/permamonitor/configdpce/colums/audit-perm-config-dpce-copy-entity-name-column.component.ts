import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { SharedModule } from '@app/common/shared/shared.module';
import {
  AuditModel,
  AuditPermConfigDpceCopyModel,
} from '@app/model/audit/audit.model';
import { EpDatatableModule, SearchOperator } from '@eportal/components';
import { Entity } from '@app/model/entity.model';
import { EntityFilterModule } from '@app/common/modules/entity-filter/entity-filter.module';
import { FormControl } from '@angular/forms';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';

@Component({
  selector: 'cgdis-portal-audit-perm-config-dpce-copy-entity-name-column',
  standalone: true,
  templateUrl:
    './audit-perm-config-dpce-copy-entity-name-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(
        () => AuditPermConfigDpceCopyEntityNameColumnComponent,
      ),
    },
  ],
  imports: [
    SharedModule,
    EpDatatableModule,
    DatatableModule,
    EntityFilterModule,
  ],
})
export class AuditPermConfigDpceCopyEntityNameColumnComponent
  extends CgdisDatatableColumnComponent<AuditPermConfigDpceCopyModel>
  implements OnInit
{
  @Input() auditService: CgdisDatatableService<AuditModel>;
  private entityFormControl = new FormControl<number>(undefined);

  ngOnInit(): void {
    this.auditService.addFilterWithFormControl(
      'permConfigDpceCopyEntityTecid',
      this.entityFormControl,
      {
        operator: SearchOperator.eq,
      },
    );
  }

  selectEntity($event: { entity: Entity; allUnderEntity: boolean }) {
    this.entityFormControl.setValue($event?.entity?.tecid);
  }
}
