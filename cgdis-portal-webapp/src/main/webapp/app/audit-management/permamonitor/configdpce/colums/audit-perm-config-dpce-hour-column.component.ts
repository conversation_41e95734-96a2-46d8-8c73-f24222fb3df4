import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
  OnInit,
} from '@angular/core';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { DecimalPipe } from '@angular/common';
import { EpDatatableModule } from '@eportal/components';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { AuditPermConfigDpceCopyModel } from '@app/model/audit/audit.model';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { FormControl } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'cgdis-portal-audit-perm-config-dpce-hour-column',
  standalone: true,
  imports: [DatatableModule, DecimalPipe, EpDatatableModule, TranslateModule],
  templateUrl: './audit-perm-config-dpce-hour-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(() => AuditPermConfigDpceHourColumnComponent),
    },
  ],
})
export class AuditPermConfigDpceHourColumnComponent
  extends CgdisDatatableColumnComponent<AuditPermConfigDpceCopyModel>
  implements OnInit
{
  @Input() auditService: CgdisDatatableService<AuditPermConfigDpceCopyModel>;

  @Input() columnName: string;
  @Input() filterName: string;
  @Input() headerKey: string;
  @Input() fieldName: 'startHour' | 'endHour' | 'toStartHour' | 'toEndHour';

  protected formControl = new FormControl<number>(undefined);
  protected hours: FieldGroupOption<any, any>[];

  ngOnInit(): void {
    this.initHours();
  }

  private initHours() {
    this.hours = Array.from({ length: 24 }, (_, i) => i).map((i) => {
      return new FieldOption({
        value: i,
        label: i.toString().padStart(2, '0'),
      });
    });
  }

  getValue: (row: AuditPermConfigDpceCopyModel) => number = (row) =>
    row ? row[this.fieldName] : undefined;
}
