<ep-datatable-column [flexGrow]="3" [columnName]="'dayValue'">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.permamonitor.configdpce.dayvalue'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <div
      style="white-space: pre-line;">{{ 'audit.permamonitor.configdpce.day.' + cast(context.row).dayValue | translate }}
    </div>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-select-filter
      [customFormControl]="formControl"
      [filterName]="'permConfigDpceDayValue'"
      [allowClear]="true"
      [possibleValues]="dayValues"
      [datatableService]="auditService"></cgdis-portal-datatable-select-filter>
  </ng-template>
</ep-datatable-column>
