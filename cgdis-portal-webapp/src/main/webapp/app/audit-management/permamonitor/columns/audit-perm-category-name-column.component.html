<ep-datatable-column [flexGrow]="3" [columnName]="columnName">
  <ng-template epDatatableHeader>
    <span [translate]="headerKey"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <div class="text-wrap">{{ cast(context.row).categoryName | defaultValue }}</div>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-text-with-null-filter
      [customFormControl]="formControl"
      [filterName]="filterName"
      [filterConfig]="filterConfig"
      [datatableService]="auditService"></cgdis-portal-datatable-text-with-null-filter>
  </ng-template>

</ep-datatable-column>
