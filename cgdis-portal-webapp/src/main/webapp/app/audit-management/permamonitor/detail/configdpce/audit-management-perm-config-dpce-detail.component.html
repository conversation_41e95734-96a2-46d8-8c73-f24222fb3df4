<div class="row ">

  <div *ngIf="!audit; else auditPrestationInformation">

  </div>
  <ng-template #auditPrestationInformation>
    <div class="col-sm-12 ">
      <table>
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.actionDateTime' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.actionDatetime | dateTimeFormat }}</span>
          </td>
        </tr>
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.person' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.personTecid.lastName }} {{ audit.personTecid.firstName }}</span>
          </td>
        </tr>
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.type.title' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ 'audit.type.' + audit.type | translate }}</span>
          </td>
        </tr>
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.actionType.title' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ 'audit.actionType.' + audit.actionType | translate }}</span>
          </td>
        </tr>
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.permamonitor.configdpce.deploymentplanname' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.deploymentPlanName | defaultValue }}</span>
          </td>
        </tr>

        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.permamonitor.configdpce.entityname' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.entity?.name | defaultValue }}</span>
          </td>
        </tr>

        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.permamonitor.configdpce.categoryname' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.categoryName| defaultValue }}</span>
          </td>
        </tr>

        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.permamonitor.configdpce.dayvalue' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ 'audit.permamonitor.configdpce.day.' + audit.dayValue | translate }}</span>
          </td>
        </tr>

        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.permamonitor.configdpce.starthour' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.startHour | number:'2.0' }}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{ 'audit.permamonitor.configdpce.optimalvalue' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.optValue | defaultValue }}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{ 'audit.permamonitor.configdpce.criticalvalue' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.criticalValue | defaultValue }}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{ 'audit.permamonitor.configdpce.unacceptablevalue' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.unacceptableValue | defaultValue }}</span>
          </td>
        </tr>

      </table>
    </div>
  </ng-template>
</div>
