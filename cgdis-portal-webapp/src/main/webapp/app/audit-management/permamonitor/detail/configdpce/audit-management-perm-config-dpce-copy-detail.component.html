<div class="row ">

  <div *ngIf="!audit; else auditPrestationInformation">

  </div>
  <ng-template #auditPrestationInformation>
    <div class="col-sm-12 ">
      <table>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.actionDateTime' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.actionDatetime | dateTimeFormat }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.person' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.personTecid.lastName }} {{ audit.personTecid.firstName }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.type.title' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ 'audit.type.' + audit.type | translate }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.actionType.title' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ 'audit.actionType.' + audit.actionType | translate }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.permamonitor.configdpcecopy.deploymentplanname' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.deploymentPlanName | defaultValue }}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{ 'audit.permamonitor.configdpcecopy.entityname' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.entity?.name | defaultValue }}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{ 'audit.permamonitor.configdpcecopy.toentityname' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.toEntity?.name | defaultValue }}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{ 'audit.permamonitor.configdpcecopy.categoryname' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.categoryName| defaultValue }}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{ 'audit.permamonitor.configdpcecopy.starthour' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.startHour | number:'2.0' }}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{ 'audit.permamonitor.configdpcecopy.endhour' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.endHour | number:'2.0' }}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{ 'audit.permamonitor.configdpcecopy.tostarthour' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.toStartHour | number:'2.0' }}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{ 'audit.permamonitor.configdpcecopy.toendhour' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.toEndHour | number:'2.0' }}</span>
          </td>
        </tr>


      </table>
    </div>
  </ng-template>
</div>
