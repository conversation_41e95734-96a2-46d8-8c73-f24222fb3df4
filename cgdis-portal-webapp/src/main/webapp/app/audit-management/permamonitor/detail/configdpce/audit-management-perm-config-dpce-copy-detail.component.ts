import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { AuditPermConfigDpceCopyModel } from '@app/model/audit/audit.model';
import { DecimalPipe, NgIf } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from '@app/common/shared/shared.module';

@Component({
  selector: 'cgdis-portal-audit-management-perm-config-dpce-copy-detail',
  templateUrl: './audit-management-perm-config-dpce-copy-detail.component.html',
  styleUrl: './audit-management-perm-config-dpce-copy-detail.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, TranslateModule, DecimalPipe, SharedModule],
})
export class AuditManagementPermConfigDpceCopyDetailComponent {
  @Input() audit: AuditPermConfigDpceCopyModel;

  constructor() {}
}
