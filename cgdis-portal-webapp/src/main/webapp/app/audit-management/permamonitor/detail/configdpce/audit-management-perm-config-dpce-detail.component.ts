import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { AuditPermConfigDpceModel } from '@app/model/audit/audit.model';
import { Subscription } from 'rxjs';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { NgIf } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from '@app/common/shared/shared.module';

@Component({
  selector: 'cgdis-portal-audit-management-perm-config-dpce-detail-component',
  templateUrl: './audit-management-perm-config-dpce-detail.component.html',
  styleUrl: './audit-management-perm-config-dpce-detail.component.scss',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, TranslateModule, SharedModule],
})
export class AuditManagementPermConfigDpceDetailComponent
  implements OnInit, OnDestroy
{
  @Input() audit: AuditPermConfigDpceModel;

  isMobile: boolean = false;

  subscriptions: Subscription[] = [];

  constructor(
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  ngOnInit(): void {}

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }
}
