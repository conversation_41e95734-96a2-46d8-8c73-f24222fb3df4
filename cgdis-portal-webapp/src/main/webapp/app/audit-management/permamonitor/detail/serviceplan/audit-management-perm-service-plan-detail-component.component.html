<div class="row ">

  <div *ngIf="!audit; else auditPrestationInformation">

  </div>
  <ng-template #auditPrestationInformation>
    <div class="col-sm-12 ">
      <table>
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.actionDateTime' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.actionDatetime | dateTimeFormat }}</span>
          </td>
        </tr>
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.person' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.personTecid.lastName }} {{ audit.personTecid.firstName }}</span>
          </td>
        </tr>
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.type.title' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ 'audit.type.' + audit.type | translate }}</span>
          </td>
        </tr>
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.actionType.title' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ 'audit.actionType.' + audit.actionType | translate }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.permamonitor.serviceplan.name' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.servicePlanName | defaultValue }}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{ 'audit.permamonitor.serviceplan.categoryname' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.categoryName | defaultValue }}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{ 'audit.permamonitor.serviceplan.subcategoryname' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.subcategoryName| defaultValue }}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{ 'audit.permamonitor.serviceplan.bookmarked' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.bookmarked | yesNo }}</span>
          </td>
        </tr>

      </table>
    </div>
  </ng-template>
</div>
