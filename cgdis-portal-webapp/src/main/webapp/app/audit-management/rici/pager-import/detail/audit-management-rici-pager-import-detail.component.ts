import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { SharedModule } from '@app/common/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { NgIf } from '@angular/common';
import { AuditRiciPagerImportModel } from '@app/model/audit/audit-rici-pager-import.model';

@Component({
    selector: 'cgdis-portal-audit-management-rici-pager-import-detail',
    standalone: true,
    imports: [SharedModule, TranslateModule, NgIf],
    templateUrl: './audit-management-rici-pager-import-detail.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AuditManagementRiciPagerImportDetailComponent {
    @Input() audit: AuditRiciPagerImportModel;
}
