import { Injectable } from '@angular/core';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { RestService } from '@eportal/core';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { AuditRiciPagerImportModel } from '@app/model/audit/audit-rici-pager-import.model';

/**
 * Service for managing datatable operations for RICI Pager Import audit records.
 */
@Injectable()
export class AuditManagementListRiciPagerImportService extends CgdisDatatableService<AuditRiciPagerImportModel> {
    constructor(
        restService: RestService,
        fb: UntypedFormBuilder,
        location: Location,
        router: Router,
        route: ActivatedRoute,
        popupService: SimplePopupService
    ) {
        super(fb, location, router, route, popupService, true);
        // Initialize the data resource list to the new dedicated endpoint for Pager import audits
        super.initDataResourceList(restService.all('audits', 'rici', 'pager-imports'));
    }
}
