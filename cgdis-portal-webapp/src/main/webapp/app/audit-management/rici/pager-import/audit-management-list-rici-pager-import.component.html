<div class="accordion__panel">
  <cgdis-portal-button-link (click)="showFilter = !showFilter; updateFilterNumber()" *ngIf="isMobile">
    <span *ngIf="!showFilter" before-icon>{{ 'service_plan.filter-link' | translate }} ({{ numberOfFilters }})</span>
    <span *ngIf="showFilter" before-icon>{{ 'service_plan.filter-link-toclose' | translate }} ({{ numberOfFilters }}
      )</span>
  </cgdis-portal-button-link>

  <ng-container *ngIf="isMobile">
    <div [hidden]="!showFilter" class="row search-filter">
      <div class="col-md-2">
        <label [translate]="'audit.actionDateTime'" class="form-label"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()"
                                                  [customFormControl]="dateFormControl"
                                                  [datatableService]="auditPagerImportService"
                                                  [filterName]="'actionDate'" [initialValue]="dateFormControl.value"
                                                  class="informations-datepicker-filter"></cgdis-portal-datatable-datepicker-filter>
      </div>
      <div class="col-md-2">
        <label [translate]="'audit.rici.pager.import.fileName'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()"
                                                      [allowClear]="true"
                                                      [customFormControl]="fileNameFormControl"
                                                      [datatableService]="auditPagerImportService"
                                                      [filterConfig]="fileNameFilterConfig"
                                                      [filterName]="'riciPagerImportFileName'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <div class="col-md-2">
        <label [translate]="'audit.rici.pager.import.importStatus'" class="form-label"></label>
        <cgdis-portal-datatable-select-filter [allowClear]="true"
                                              [datatableService]="auditPagerImportService"
                                              [filterName]="'riciPagerImportStatus'"
                                              [possibleValues]="importStatusValues"
        ></cgdis-portal-datatable-select-filter>
      </div>
      <div class="col-md-2">
        <label [translate]="'audit.rici.pager.import.totalRecords'" class="form-label"></label>
        <cgdis-portal-datatable-number-filter (onValueChanged)="updateFilterNumber()"
                                              [allowClear]="true"
                                              [customFormControl]="totalRecordsFormControl"
                                              [datatableService]="auditPagerImportService"
                                              [filterConfig]="totalRecordsFilterConfig"
                                              [filterName]="'riciPagerImportTotalRecords'"></cgdis-portal-datatable-number-filter>
      </div>
      <div class="col-md-2">
        <label [translate]="'audit.rici.pager.import.successfulRecords'" class="form-label"></label>
        <cgdis-portal-datatable-number-filter (onValueChanged)="updateFilterNumber()"
                                              [allowClear]="true"
                                              [customFormControl]="successfulRecordsFormControl"
                                              [datatableService]="auditPagerImportService"
                                              [filterConfig]="successfulRecordsFilterConfig"
                                              [filterName]="'riciPagerImportSuccessfulRecords'"></cgdis-portal-datatable-number-filter>
      </div>
      <div class="col-md-2">
        <label [translate]="'audit.rici.pager.import.validationErrors'" class="form-label"></label>
        <cgdis-portal-datatable-number-filter (onValueChanged)="updateFilterNumber()"
                                              [allowClear]="true"
                                              [customFormControl]="validationErrorsFormControl"
                                              [datatableService]="auditPagerImportService"
                                              [filterConfig]="validationErrorsFilterConfig"
                                              [filterName]="'riciPagerImportValidationErrors'"></cgdis-portal-datatable-number-filter>
      </div>
      <div class="col-md-2">
        <label [translate]="'audit.rici.pager.import.importErrors'" class="form-label"></label>
        <cgdis-portal-datatable-number-filter (onValueChanged)="updateFilterNumber()"
                                              [allowClear]="true"
                                              [customFormControl]="importErrorsFormControl"
                                              [datatableService]="auditPagerImportService"
                                              [filterConfig]="importErrorsFilterConfig"
                                              [filterName]="'riciPagerImportImportErrors'"></cgdis-portal-datatable-number-filter>
      </div>
    </div>
  </ng-container>

  <cgdis-portal-cgdisdatatable [class]="'entity__table'" [datatableService]="auditPagerImportService"
                               [id]="'audit-rici-pager-import-list'" [showDetails]="isMobile"
                               [sorts]="[{dir:'desc',prop:'tecid'}]">
    <ep-datatable-column [columnName]="'actionDatetime'" [flexGrow]="1">
      <ng-template epDatatableHeader><span [translate]="'audit.actionDateTime'"></span></ng-template>
      <ng-template epDatatableCell let-context>{{ cast(context.row).actionDatetime | dateTimeFormat }}</ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter [customFormControl]="dateFormControl"
                                                  [datatableService]="auditPagerImportService"
                                                  [filterName]="'actionDate'"
                                                  [initialValue]="dateFormControl.value"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
    </ep-datatable-column>
    <!-- User -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'personTecid.lastName'" [flexGrow]="1">
      <ng-template epDatatableHeader><span [translate]="'audit.person'"></span></ng-template>
      <ng-template epDatatableCell let-context>
        <span
          class="text-wrap">{{ cast(context.row).personTecid?.lastName }} {{ cast(context.row).personTecid?.firstName }}</span>
      </ng-template>
    </ep-datatable-column>
    <!-- CGDIS Registration Number -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'personTecid.cgdisRegistrationNumber'" [flexGrow]="1">
      <ng-template epDatatableHeader><span [translate]="'audit.cgdisregistrationnumber'"></span></ng-template>
      <ng-template epDatatableCell let-context>
        {{ cast(context.row).personTecid?.cgdisRegistrationNumber }}
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-text-with-null-filter
          [allowClear]="true"
          [datatableService]="auditPagerImportService"
          [filterName]="'personTecid.cgdisRegistrationNumber'"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'fileName'" [flexGrow]="2">
      <ng-template epDatatableHeader><span [translate]="'audit.rici.pager.import.fileName'"></span></ng-template>
      <ng-template epDatatableCell let-context>
        <div class="text-wrap">{{ cast(context.row).fileName | defaultValue }}</div>
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-text-with-null-filter
          [allowClear]="true"
          [customFormControl]="fileNameFormControl"
          [datatableService]="auditPagerImportService"
          [filterConfig]="fileNameFilterConfig"
          [filterName]="'riciPagerImportFileName'">
        </cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'importStatus'" [flexGrow]="2">
      <ng-template epDatatableHeader><span [translate]="'audit.rici.pager.import.importStatus'"></span></ng-template>
      <ng-template epDatatableCell let-context>
        <div class="text-wrap">
          {{ 'audit.rici.pager.import.statuses.' + cast(context.row).importStatus | translate }}
        </div>
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-select-filter
          [allowClear]="true"
          [datatableService]="auditPagerImportService"
          [filterName]="'riciPagerImportStatus'"
          [filterConfig]="importStatusFilterConfig"
          [possibleValues]="importStatusValues">
        </cgdis-portal-datatable-select-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'totalRecords'" [flexGrow]="1">
      <ng-template epDatatableHeader><span [translate]="'audit.rici.pager.import.totalRecords'"></span></ng-template>
      <ng-template epDatatableCell let-context>{{ cast(context.row).totalRecords }}</ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-number-filter
          [allowClear]="true"
          [customFormControl]="totalRecordsFormControl"
          [datatableService]="auditPagerImportService"
          [filterConfig]="totalRecordsFilterConfig"
          [filterName]="'riciPagerImportTotalRecords'">
        </cgdis-portal-datatable-number-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'successfulRecords'" [flexGrow]="1">
      <ng-template epDatatableHeader><span [translate]="'audit.rici.pager.import.successfulRecords'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>{{ cast(context.row).successfulRecords }}</ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-number-filter
          [allowClear]="true"
          [customFormControl]="successfulRecordsFormControl"
          [datatableService]="auditPagerImportService"
          [filterConfig]="successfulRecordsFilterConfig"
          [filterName]="'riciPagerImportSuccessfulRecords'">
        </cgdis-portal-datatable-number-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'validationErrors'" [flexGrow]="1">
      <ng-template epDatatableHeader><span [translate]="'audit.rici.pager.import.validationErrors'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>{{ cast(context.row).validationErrors }}</ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-number-filter
          [allowClear]="true"
          [customFormControl]="validationErrorsFormControl"
          [datatableService]="auditPagerImportService"
          [filterConfig]="validationErrorsFilterConfig"
          [filterName]="'riciPagerImportValidationErrors'">
        </cgdis-portal-datatable-number-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'importErrors'" [flexGrow]="1">
      <ng-template epDatatableHeader><span [translate]="'audit.rici.pager.import.importErrors'"></span></ng-template>
      <ng-template epDatatableCell let-context>{{ cast(context.row).importErrors }}</ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-number-filter
          [allowClear]="true"
          [customFormControl]="importErrorsFormControl"
          [datatableService]="auditPagerImportService"
          [filterConfig]="importErrorsFilterConfig"
          [filterName]="'riciPagerImportImportErrors'">
        </cgdis-portal-datatable-number-filter>
      </ng-template>
    </ep-datatable-column>

    <ng-template #template let-row="row">
      <div *ngIf="row.tecid">
        <cgdis-portal-audit-management-rici-pager-import-detail
          [audit]="row"></cgdis-portal-audit-management-rici-pager-import-detail>
      </div>
    </ng-template>
  </cgdis-portal-cgdisdatatable>
</div>
