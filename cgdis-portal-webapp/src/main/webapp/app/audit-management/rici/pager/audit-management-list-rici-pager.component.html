<div class="accordion__panel">

  <cgdis-portal-button-link (click)="showFilter = !showFilter; updateFilterNumber()" *ngIf="isMobile">
    <span *ngIf="!showFilter" before-icon>{{ 'service_plan.filter-link' | translate }} ({{ numberOfFilters }})</span>
    <span *ngIf="showFilter" before-icon>{{ 'service_plan.filter-link-toclose' | translate }} ({{ numberOfFilters }}
      )</span>
  </cgdis-portal-button-link>

  <!-- Mobile Filters -->
  <ng-container *ngIf="isMobile">
    <div [hidden]="!showFilter" class="row search-filter">
      <!-- Date Filter -->
      <div class="col-md-2">
        <label [translate]="'audit.actionDateTime'" class="form-label"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()"
                                                  [customFormControl]="dateFormControl"
                                                  [datatableService]="auditPagerService"
                                                  [filterName]="'actionDate'"
                                                  [initialValue]="dateFormControl.value"
                                                  class="informations-datepicker-filter"></cgdis-portal-datatable-datepicker-filter>
      </div>
      <!-- Action Type Filter -->
      <div class="col-md-2">
        <label [translate]="'audit.actionType.title'" class="form-label"></label>
        <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true"
                                              [datatableService]="auditPagerService"
                                              [filterName]="'actionType'"
                                              [possibleValues]="actionTypes"></cgdis-portal-datatable-select-filter>
      </div>
      <!-- Pager ID Filter -->
      <div class="col-md-2">
        <label [translate]="'audit.rici.pager.pagerId'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()"
                                                      [customFormControl]="pagerIdFormControl"
                                                      [datatableService]="auditPagerService"
                                                      [filterConfig]="pagerIdFilterConfig"
                                                      [filterName]="'pagerId'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <!-- Serial Number Filter -->
      <div class="col-md-2">
        <label [translate]="'audit.rici.pager.serialNumber'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()"
                                                      [customFormControl]="serialNumberFormControl"
                                                      [datatableService]="auditPagerService"
                                                      [filterConfig]="serialNumberFilterConfig"
                                                      [filterName]="'serialNumber'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <!-- Manufacturer Filter -->
      <div class="col-md-2">
        <label [translate]="'audit.rici.pager.manufacturer'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()"
                                                      [customFormControl]="manufacturerFormControl"
                                                      [datatableService]="auditPagerService"
                                                      [filterConfig]="manufacturerFilterConfig"
                                                      [filterName]="'riciPagerManufacturer'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <!-- Model Filter -->
      <div class="col-md-2">
        <label [translate]="'audit.rici.pager.model'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()"
                                                      [customFormControl]="modelFormControl"
                                                      [datatableService]="auditPagerService"
                                                      [filterConfig]="modelFilterConfig"
                                                      [filterName]="'riciPagerModel'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <!-- Status Filter -->
      <div class="col-md-2">
        <label [translate]="'audit.rici.pager.status'" class="form-label"></label>
        <cgdis-portal-datatable-select-filter [allowClear]="true"
                                              [datatableService]="auditPagerService"
                                              [filterName]="'status'"
                                              [possibleValues]="pagerStatusValues"
        ></cgdis-portal-datatable-select-filter>
      </div>
      <!-- Associated SIM ICCID Filter -->
      <div class="col-md-2">
        <label [translate]="'audit.rici.pager.associatedSimCardIccid'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()"
                                                      [customFormControl]="simIccidFormControl"
                                                      [datatableService]="auditPagerService"
                                                      [filterConfig]="simIccidFilterConfig"
                                                      [filterName]="'associatedSimCardIccid'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <!-- Assignment Type Filter -->
      <div class="col-md-2">
        <label [translate]="'audit.rici.pager.assignmentType'" class="form-label"></label>
        <cgdis-portal-datatable-select-filter [allowClear]="true"
                                              [datatableService]="auditPagerService"
                                              [filterName]="'assignmentType'"
                                              [possibleValues]="assignmentTypeValues"
        ></cgdis-portal-datatable-select-filter>
      </div>
      <!-- Assigned Person Filter -->
      <div class="col-md-2">
        <label [translate]="'audit.rici.pager.assignedPersonName'" class="form-label"></label>
        <cgdis-portal-datatable-person-filter
          [datatableService]="auditPagerService"
          [filterName]="'assignedPersonTecid'"></cgdis-portal-datatable-person-filter>

      </div>
      <!-- Assigned Entity Filter -->
      <div class="col-md-2">
        <label [translate]="'audit.rici.pager.assignedEntityName'" class="form-label"></label>
        <cgdis-portal-datatable-entity-filter
          [allowClear]="true"
          [datatableService]="auditPagerService"
          [entityPermission]="['ROLE_PERMISSION_RICI_PAGER_VIEW']"
          [filterConfig]="entityFilterConfig"
          [filterName]="'assignedEntityTecid'"
          [placeholder]="'rici.pagers.list.headers.pager-assignment-entity-filter' | translate"
          [underDisable]="true"
        ></cgdis-portal-datatable-entity-filter>
      </div>
      <!-- Individual RIC Filter -->
      <div class="col-md-2">
        <label [translate]="'audit.rici.pager.individualRic'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()"
                                                      [customFormControl]="individualRicFormControl"
                                                      [datatableService]="auditPagerService"
                                                      [filterConfig]="individualRicFilterConfig"
                                                      [filterName]="'individualRic'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
    </div>
  </ng-container>

  <!-- Datatable -->
  <cgdis-portal-cgdisdatatable
    [class]="'entity__table'"
    [datatableService]="auditPagerService"
    [id]="'audit-rici-pager-list'"
    [showDetails]="isMobile"
    [sorts]="[{dir:'desc',prop:'tecid'}]">

    <!-- Action date time -->
    <ep-datatable-column [columnName]="'actionDatetime'" [flexGrow]="2">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.actionDateTime'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{ cast(context.row).actionDatetime | dateTimeFormat }}
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter
          [customFormControl]="dateFormControl"
          [datatableService]="auditPagerService"
          [filterName]="'actionDate'"
          [initialValue]="dateFormControl.value"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- User -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'personTecid.lastName'" [flexGrow]="2">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.person'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <span
          class="text-wrap">{{ cast(context.row).personTecid.lastName }} {{ cast(context.row).personTecid.firstName }}</span>
      </ng-template>
    </ep-datatable-column>

    <!-- CGDIS Registration Number -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'personTecid.cgdisRegistrationNumber'" [flexGrow]="1">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.cgdisregistrationnumber'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{ cast(context.row).personTecid.cgdisRegistrationNumber }}
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-text-with-null-filter
          [allowClear]="true"
          [datatableService]="auditPagerService"
          [filterName]="'personTecid.cgdisRegistrationNumber'"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Pager ID -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'pagerId'" [flexGrow]="1" [sortable]="true">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.rici.pager.pagerId'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <div class="text-wrap">{{ cast(context.row).pagerId | defaultValue }}</div>
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-text-with-null-filter
          [allowClear]="true"
          [customFormControl]="pagerIdFormControl"
          [datatableService]="auditPagerService"
          [filterConfig]="pagerIdFilterConfig"
          [filterName]="'pagerId'"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Serial Number -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'serialNumber'" [flexGrow]="2" [sortable]="true">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.rici.pager.serialNumber'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <div class="text-wrap">{{ cast(context.row).serialNumber | defaultValue }}</div>
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-text-with-null-filter
          [allowClear]="true"
          [customFormControl]="serialNumberFormControl"
          [datatableService]="auditPagerService"
          [filterConfig]="serialNumberFilterConfig"
          [filterName]="'serialNumber'"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Individual RIC -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'individualRic'" [flexGrow]="1" [sortable]="true">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.rici.pager.individualRic'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <div class="text-wrap">{{ cast(context.row).individualRic | defaultValue }}</div>
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-text-with-null-filter
          [allowClear]="true"
          [customFormControl]="individualRicFormControl"
          [datatableService]="auditPagerService"
          [filterConfig]="individualRicFilterConfig"
          [filterName]="'individualRic'"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Status -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'status'" [flexGrow]="1" [sortable]="true">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.rici.pager.status'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <div class="text-wrap">{{ cast(context.row).status | defaultValue }}</div>
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-select-filter [allowClear]="true"
                                              [datatableService]="auditPagerService"
                                              [filterName]="'status'"
                                              [possibleValues]="pagerStatusValues"
        ></cgdis-portal-datatable-select-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Associated SIM ICCID -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'associatedSimCardIccid'" [flexGrow]="2"
                         [sortable]="true">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.rici.pager.associatedSimCardIccid'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <div class="text-wrap">{{ cast(context.row).associatedSimCardIccid | defaultValue }}</div>
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-text-with-null-filter
          [allowClear]="true"
          [customFormControl]="simIccidFormControl"
          [datatableService]="auditPagerService"
          [filterConfig]="simIccidFilterConfig"
          [filterName]="'associatedSimCardIccid'"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Assignment Type -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'assignmentType'" [flexGrow]="1" [sortable]="true">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.rici.pager.assignmentType'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <div class="text-wrap">{{ cast(context.row).assignmentType | defaultValue }}</div>
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-select-filter [allowClear]="true"
                                              [datatableService]="auditPagerService"
                                              [filterName]="'assignmentType'"
                                              [possibleValues]="assignmentTypeValues"
        ></cgdis-portal-datatable-select-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Assigned Person -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'assignedPersonTecid'" [flexGrow]="2"
                         [sortable]="true">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.rici.pager.assignedPersonName'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <div class="text-wrap">{{ cast(context.row).assignedPersonName | defaultValue }}</div>
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-person-filter
          [datatableService]="auditPagerService"
          [filterConfig]="personFilterConfig"
          [filterName]="'assignedPersonTecid'"></cgdis-portal-datatable-person-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Assigned Entity -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'assignedEntityTecid'" [flexGrow]="2"
                         [sortable]="true">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.rici.pager.assignedEntityName'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <div class="text-wrap">{{ cast(context.row).assignedEntityName | defaultValue }}</div>
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-entity-filter
          [allowClear]="true"
          [datatableService]="auditPagerService"
          [entityPermission]="['ROLE_PERMISSION_RICI_PAGER_VIEW']"
          [filterConfig]="entityFilterConfig"
          [filterName]="'assignedEntityTecid'"
          [placeholder]="'rici.pagers.list.headers.pager-assignment-entity-filter' | translate"
          [underDisable]="true"
        ></cgdis-portal-datatable-entity-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Action type -->
    <ep-datatable-column [columnName]="'actionType'" [flexGrow]="1">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.actionType.title'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <span class="text-wrap">{{ 'audit.actionType.' + cast(context.row).actionType | translate }}</span>
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-select-filter [allowClear]="true" [datatableService]="auditPagerService"
                                              [filterName]="'actionType'"
                                              [possibleValues]="actionTypes"></cgdis-portal-datatable-select-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Detail Template for Mobile -->
    <ng-template #template let-row="row">
      <div *ngIf="row.tecid">
        <cgdis-portal-audit-management-rici-pager-detail
          [audit]="row"></cgdis-portal-audit-management-rici-pager-detail>
      </div>
    </ng-template>

  </cgdis-portal-cgdisdatatable>

</div>
