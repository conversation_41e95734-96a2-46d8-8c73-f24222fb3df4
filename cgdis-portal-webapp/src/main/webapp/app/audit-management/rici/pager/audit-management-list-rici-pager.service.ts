import { Injectable } from '@angular/core';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { AuditRiciPagerModel } from '@app/model/audit/audit.model'; // Assuming this model exists
import { RestService } from '@eportal/core';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';

@Injectable()
export class AuditManagementListRiciPagerService extends CgdisDatatableService<AuditRiciPagerModel> {
  constructor(
    restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService, true);
    // Update the API endpoint to fetch pager audits
    super.initDataResourceList(
      restService.all('audits', 'rici', 'pagers'),
    );
  }
}
