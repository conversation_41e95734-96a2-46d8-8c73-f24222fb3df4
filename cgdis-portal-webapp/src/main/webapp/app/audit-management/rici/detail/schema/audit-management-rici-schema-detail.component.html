<div class="row">
    <div class="col-md-6">
        <div class="row">
            <div class="col-md-6">
                <label class="form-label" [translate]="'audit.actionDateTime'"></label>
                <p>{{ audit.actionDatetime | dateTimeFormat }}</p>
            </div>
            <div class="col-md-6">
                <label class="form-label" [translate]="'audit.person'"></label>
                <!-- Access nested person details via personTecid -->
                <p>{{ audit.personTecid?.firstName }} {{ audit.personTecid?.lastName }}</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <label class="form-label" [translate]="'audit.actionType.title'"></label>
                <p>{{ 'audit.actionType.' + audit.actionType | translate }}</p>
            </div>
            <div class="col-md-6">
                <label class="form-label" [translate]="'audit.cgdisRegistrationNumber'"></label>
                <!-- Access nested person details via personTecid -->
                <p>{{ audit.personTecid?.cgdisRegistrationNumber }}</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <label class="form-label" [translate]="'audit.rici.schema.schemaName'"></label>
                <p>{{ audit.schemaName }}</p>
            </div>
            <div class="col-md-6">
                <label class="form-label" [translate]="'audit.rici.schema.schemaAlias'"></label>
                <p>{{ audit.schemaAlias }}</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <label class="form-label" [translate]="'audit.rici.schema.description'"></label>
                <p>{{ audit.description }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="row">
            <div class="col-md-6">
                <label class="form-label" [translate]="'audit.rici.schema.functionCodeA'"></label>
                <p>{{ audit.functionCodeAName }} ({{ audit.functionCodeATecid }})</p>
            </div>
            <div class="col-md-6">
                <label class="form-label" [translate]="'audit.rici.schema.functionCodeB'"></label>
                <p>{{ audit.functionCodeBName }} ({{ audit.functionCodeBTecid }})</p>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6">
                <label class="form-label" [translate]="'audit.rici.schema.functionCodeC'"></label>
                <p>{{ audit.functionCodeCName }} ({{ audit.functionCodeCTecid }})</p>
            </div>
            <div class="col-md-6">
                <label class="form-label" [translate]="'audit.rici.schema.functionCodeD'"></label>
                <p>{{ audit.functionCodeDName }} ({{ audit.functionCodeDTecid }})</p>
            </div>
        </div>
    </div>
</div>
