<div class="row ">
  <div *ngIf="!audit; else auditRicRangeInformation">
    <!-- Placeholder or loading indicator -->
  </div>
  <ng-template #auditRicRangeInformation>
    <div class="col-sm-12 ">
      <table>
        <!-- Common Audit Fields (Mobile Only) -->
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.actionDateTime' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.actionDatetime | dateTimeFormat }}</span>
          </td>
        </tr>
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.person' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.personTecid.lastName }} {{ audit.personTecid.firstName }}</span>
          </td>
        </tr>
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.type.title' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ 'audit.type.' + audit.type | translate }}</span>
          </td>
        </tr>
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.actionType.title' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ 'audit.actionType.' + audit.actionType | translate }}</span>
          </td>
        </tr>

        <!-- RIC Range Specific Fields -->
        <tr>
          <td class="list-label">
            <span>{{ 'audit.rici.ricrange.name' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.name | defaultValue }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.rici.ricrange.type' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ 'rici.ranges.types.' + audit.ricRangeType | lowercase | translate | defaultValue }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.rici.ricrange.rangeStart' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.rangeStart | defaultValue }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.rici.ricrange.rangeEnd' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.rangeEnd | defaultValue }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.rici.ricrange.entity' | translate }}</span>
          </td>
          <td class="list-value">
            <!-- TODO: Display entity name if available/needed -->
            <span>{{ audit.entityTecid | defaultValue }}</span>
          </td>
        </tr>
      </table>
    </div>
  </ng-template>
</div>
