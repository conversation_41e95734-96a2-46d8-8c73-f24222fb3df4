import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { AuditRiciSchemaModel } from '@app/model/audit/audit-rici-schema.model'; // Use the correct model
import { SharedModule } from '@app/common/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { NgIf } from '@angular/common';

@Component({
    selector: 'cgdis-portal-audit-management-rici-schema-detail',
    standalone: true,
    imports: [SharedModule, TranslateModule, NgIf],
    templateUrl: './audit-management-rici-schema-detail.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AuditManagementRiciSchemaDetailComponent {
    @Input() audit: AuditRiciSchemaModel;
}
