<div class="row ">

  <div *ngIf="!audit; else auditSimCardInformation">

  </div>
  <ng-template #auditSimCardInformation>
    <div class="col-sm-12 ">
      <table>
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.actionDateTime' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.actionDatetime | dateTimeFormat }}</span>
          </td>
        </tr>
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.person' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.personTecid.lastName }} {{ audit.personTecid.firstName }}</span>
          </td>
        </tr>
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.type.title' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ 'audit.type.' + audit.type | translate }}</span>
          </td>
        </tr>
        <tr *ngIf="isMobile">
          <td class="list-label">
            <span>{{ 'audit.actionType.title' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ 'audit.actionType.' + audit.actionType | translate }}</span>
          </td>
        </tr>
        <tr>
          <td class="list-label">
            <span>{{ 'audit.rici.simcard.iccid' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.iccid | defaultValue }}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{ 'audit.rici.simcard.msisdn' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.msisdn | defaultValue }}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{ 'audit.rici.simcard.pin' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.pin | defaultValue }}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{ 'audit.rici.simcard.status' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.status | defaultValue }}</span>
          </td>
        </tr>

        <tr>
          <td class="list-label">
            <span>{{ 'audit.rici.simcard.associatedPager' | translate }}</span>
          </td>
          <td class="list-value">
            <span>{{ audit.associatedPagerTecid | defaultValue }}</span>
          </td>
        </tr>
      </table>
    </div>
  </ng-template>
</div>
