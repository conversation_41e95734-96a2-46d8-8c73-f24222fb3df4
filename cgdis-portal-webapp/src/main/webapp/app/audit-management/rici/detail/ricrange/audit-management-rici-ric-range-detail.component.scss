// Using similar styles as simcard detail for consistency
.list-label {
  padding-left: 1rem;
  font-weight: 500;
  @media (min-width: 768px) {
    padding-right: 3rem;
    padding-left: 6rem; // Adjust as needed for alignment
  }
  font-size: 1.2em;

  &::after {
    content: ':';
  }
}

.list-value {
  font-weight: 100;
  font-size: 1.2em;
  @media (max-width: 768px) {
    padding-left: 1rem;
  }
}

table {
  margin-bottom: 10px;
  width: 100%; // Ensure table takes full width
}

td {
  vertical-align: top; // Align top for potentially multi-line values
  padding-top: 5px; // Add some padding
  padding-bottom: 5px;
  height: auto !important; // Allow height to adjust
}

ul {
  padding: 0;
  list-style-type: none;
}

tr {
  background-color: transparent !important;
  @media (max-width: 768px) {
    display: grid;
    margin-bottom: 1rem;
    border-bottom: 1px solid #eee; // Add separator in mobile view
    padding-bottom: 0.5rem;
  }
}
