import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { AuditRiciPagerModel } from '@app/model/audit/audit.model'; // Assuming this model exists
import { SharedModule } from '@app/common/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { NgIf } from '@angular/common';

@Component({
  selector: 'cgdis-portal-audit-management-rici-pager-detail',
  standalone: true,
  imports: [SharedModule, TranslateModule, NgIf],
  templateUrl: './audit-management-rici-pager-detail.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AuditManagementRiciPagerDetailComponent {
  @Input() audit: AuditRiciPagerModel;
}
