import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { AuditManagementListRiciSchemaService } from './audit-management-list-rici-schema.service'; // Use the correct service
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { FormControl, UntypedFormControl } from '@angular/forms';
import { Subscription } from 'rxjs';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { AuditRiciSchemaModel } from '@app/model/audit/audit-rici-schema.model'; // Use the correct model
import { NgIf } from '@angular/common';
import { SharedModule } from '@app/common/shared/shared.module';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { TranslateModule } from '@ngx-translate/core';
import { AuditManagementRiciSchemaDetailComponent } from '../detail/schema/audit-management-rici-schema-detail.component'; // Use the correct detail component
import {
  EpDatatableModule,
  FilterConfig,
  SearchOperator,
} from '@eportal/components';

@Component({
  selector: 'cgdis-portal-audit-management-list-rici-schema',
  templateUrl: './audit-management-list-rici-schema.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  providers: [AuditManagementListRiciSchemaService], // Provide the correct service
  imports: [
    NgIf,
    SharedModule,
    DatatableModule,
    TranslateModule,
    EpDatatableModule,
    AuditManagementRiciSchemaDetailComponent, // Import the correct detail component
  ],
})
export class AuditManagementListRiciSchemaComponent
  implements OnInit, OnDestroy
{
  @Input() actionTypes: FieldOption<string>[];
  @Input() dateFormControl: UntypedFormControl;
  showFilter = false;
  isMobile: boolean = false;
  numberOfFilters: number;

  // Form controls for RICI Schema filters
  schemaNameFormControl = new FormControl<string>(undefined);
  schemaNameFilterConfig = new FilterConfig({
    operator: SearchOperator.like,
    inUrl: true,
  });
  schemaAliasFormControl = new FormControl<string>(undefined);
  schemaAliasFilterConfig = new FilterConfig({ operator: SearchOperator.like });
  functionCodeANameFormControl = new FormControl<string>(undefined);
  functionCodeANameFilterConfig = new FilterConfig({
    operator: SearchOperator.like,
  });
  functionCodeBNameFormControl = new FormControl<string>(undefined);
  functionCodeBNameFilterConfig = new FilterConfig({
    operator: SearchOperator.like,
  });
  functionCodeCNameFormControl = new FormControl<string>(undefined);
  functionCodeCNameFilterConfig = new FilterConfig({
    operator: SearchOperator.like,
  });
  functionCodeDNameFormControl = new FormControl<string>(undefined);
  functionCodeDNameFilterConfig = new FilterConfig({
    operator: SearchOperator.like,
  });

  private subscriptions: Subscription[] = [];

  constructor(
    public auditSchemaService: AuditManagementListRiciSchemaService, // Inject the correct service
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  cast(row: any): AuditRiciSchemaModel {
    return row as AuditRiciSchemaModel;
  }

  ngOnInit() {
    this.numberOfFilters = 0;
    this.subscriptions.push(
      this.auditSchemaService.canExecuteFirstSearch().subscribe(() => {
        this.updateFilterNumber();
        const urlParams = new URLSearchParams(window.location.search);
        const hashParams = new URLSearchParams(
          window.location.hash.split('?')[1] || '',
        );
        // Check for 'schemaName' in URL params instead of 'riciSchemaName'
        const schemaName =
          urlParams.get('schemaName') || hashParams.get('schemaName');
        if (schemaName) {
          this.schemaNameFormControl.setValue(schemaName);
          this.auditSchemaService.addFilterWithFormControl(
            'riciSchemaName', // Use correct filter name
            this.schemaNameFormControl,
            this.schemaNameFilterConfig,
          );
          this.auditSchemaService.search();
        }
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }

  updateFilterNumber() {
    this.numberOfFilters = this.auditSchemaService.getNumberOfFilters();
  }
}
