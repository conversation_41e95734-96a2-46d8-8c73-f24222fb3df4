<div class="accordion__panel">
  <cgdis-portal-button-link (click)="showFilter = !showFilter; updateFilterNumber()" *ngIf="isMobile">
    <span *ngIf="!showFilter" before-icon>{{ 'service_plan.filter-link' | translate }} ({{ numberOfFilters }})</span>
    <span *ngIf="showFilter" before-icon>{{ 'service_plan.filter-link-toclose' | translate }} ({{ numberOfFilters }}
      )</span>
  </cgdis-portal-button-link>

  <ng-container *ngIf="isMobile">
    <div [hidden]="!showFilter" class="row search-filter">
      <!-- Common Filters -->
      <div class="col-md-2">
        <label [translate]="'audit.actionDateTime'" class="form-label"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()"
                                                  [customFormControl]="dateFormControl"
                                                  [datatableService]="auditSchemaService"
                                                  [filterName]="'actionDate'" [initialValue]="dateFormControl.value"
                                                  class="informations-datepicker-filter"></cgdis-portal-datatable-datepicker-filter>
      </div>
      <div class="col-md-2">
        <label [translate]="'audit.actionType.title'" class="form-label"></label>
        <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true"
                                              [datatableService]="auditSchemaService" [filterName]="'actionType'"
                                              [possibleValues]="actionTypes"></cgdis-portal-datatable-select-filter>
      </div>
      <!-- RICI Schema Specific Filters -->
      <div class="col-md-2">
        <label [translate]="'audit.rici.schema.schemaName'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true"
                                                      [customFormControl]="schemaNameFormControl"
                                                      [datatableService]="auditSchemaService"
                                                      [filterConfig]="schemaNameFilterConfig"
                                                      [filterName]="'riciSchemaName'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <div class="col-md-2">
        <label [translate]="'audit.rici.schema.schemaAlias'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true"
                                                      [customFormControl]="schemaAliasFormControl"
                                                      [datatableService]="auditSchemaService"
                                                      [filterConfig]="schemaAliasFilterConfig"
                                                      [filterName]="'riciSchemaAlias'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <div class="col-md-2">
        <label [translate]="'audit.rici.schema.functionCodeA'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true"
                                                      [customFormControl]="functionCodeANameFormControl"
                                                      [datatableService]="auditSchemaService"
                                                      [filterConfig]="functionCodeANameFilterConfig"
                                                      [filterName]="'riciSchemaFunctionCodeAName'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <div class="col-md-2">
        <label [translate]="'audit.rici.schema.functionCodeB'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true"
                                                      [customFormControl]="functionCodeBNameFormControl"
                                                      [datatableService]="auditSchemaService"
                                                      [filterConfig]="functionCodeBNameFilterConfig"
                                                      [filterName]="'riciSchemaFunctionCodeBName'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <div class="col-md-2">
        <label [translate]="'audit.rici.schema.functionCodeC'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true"
                                                      [customFormControl]="functionCodeCNameFormControl"
                                                      [datatableService]="auditSchemaService"
                                                      [filterConfig]="functionCodeCNameFilterConfig"
                                                      [filterName]="'riciSchemaFunctionCodeCName'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <div class="col-md-2">
        <label [translate]="'audit.rici.schema.functionCodeD'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true"
                                                      [customFormControl]="functionCodeDNameFormControl"
                                                      [datatableService]="auditSchemaService"
                                                      [filterConfig]="functionCodeDNameFilterConfig"
                                                      [filterName]="'riciSchemaFunctionCodeDName'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
    </div>
  </ng-container>

  <cgdis-portal-cgdisdatatable [class]="'entity__table'" [datatableService]="auditSchemaService"
                               [id]="'audit-rici-schema-list'" [showDetails]="isMobile"
                               [sorts]="[{dir:'desc',prop:'tecid'}]">
    <!-- Common Columns -->
    <ep-datatable-column [columnName]="'actionDatetime'" [flexGrow]="2">
      <ng-template epDatatableHeader><span [translate]="'audit.actionDateTime'"></span></ng-template>
      <ng-template epDatatableCell let-context>{{ cast(context.row).actionDatetime | dateTimeFormat }}</ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter [customFormControl]="dateFormControl"
                                                  [datatableService]="auditSchemaService" [filterName]="'actionDate'"
                                                  [initialValue]="dateFormControl.value"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'person'" [flexGrow]="2">
      <ng-template epDatatableHeader><span [translate]="'audit.person'"></span></ng-template>
      <!-- Access nested person details via personTecid -->
      <ng-template
        epDatatableCell let-context>
        {{ cast(context.row).personTecid?.firstName }} {{ cast(context.row).personTecid?.lastName }}
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-text-with-null-filter [allowClear]="true" [datatableService]="auditSchemaService"
                                                      [filterName]="'personTecid.cgdisRegistrationNumber'"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'actionType'" [flexGrow]="1">
      <ng-template epDatatableHeader><span [translate]="'audit.actionType.title'"></span></ng-template>
      <ng-template epDatatableCell let-context>{{ 'audit.actionType.' + cast(context.row).actionType | translate }}
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-select-filter [allowClear]="true" [datatableService]="auditSchemaService"
                                              [filterName]="'actionType'"
                                              [possibleValues]="actionTypes"></cgdis-portal-datatable-select-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'cgdisRegistrationNumber'" [flexGrow]="1">
      <ng-template epDatatableHeader><span [translate]="'audit.cgdisRegistrationNumber'"></span></ng-template>
      <!-- Access nested person details via personTecid -->
      <ng-template epDatatableCell let-context>{{ cast(context.row).personTecid?.cgdisRegistrationNumber }}
      </ng-template>
      <!-- Filter is handled by the 'person' column filter -->
    </ep-datatable-column>

    <!-- RICI Schema Specific Columns -->
    <ep-datatable-column [columnName]="'schemaName'" [flexGrow]="2">
      <ng-template epDatatableHeader><span [translate]="'audit.rici.schema.schemaName'"></span></ng-template>
      <ng-template epDatatableCell let-context>{{ cast(context.row).schemaName }}</ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-text-with-null-filter [allowClear]="true" [customFormControl]="schemaNameFormControl"
                                                      [datatableService]="auditSchemaService"
                                                      [filterConfig]="schemaNameFilterConfig"
                                                      [filterName]="'riciSchemaName'"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'schemaAlias'" [flexGrow]="2">
      <ng-template epDatatableHeader><span [translate]="'audit.rici.schema.schemaAlias'"></span></ng-template>
      <ng-template epDatatableCell let-context>{{ cast(context.row).schemaAlias }}</ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-text-with-null-filter [allowClear]="true" [customFormControl]="schemaAliasFormControl"
                                                      [datatableService]="auditSchemaService"
                                                      [filterConfig]="schemaAliasFilterConfig"
                                                      [filterName]="'riciSchemaAlias'"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'functionCodeA'" [flexGrow]="2">
      <ng-template epDatatableHeader><span [translate]="'audit.rici.schema.functionCodeA'"></span></ng-template>
      <ng-template epDatatableCell let-context>{{ cast(context.row).functionCodeAName }}</ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-text-with-null-filter [allowClear]="true"
                                                      [customFormControl]="functionCodeANameFormControl"
                                                      [datatableService]="auditSchemaService"
                                                      [filterConfig]="functionCodeANameFilterConfig"
                                                      [filterName]="'riciSchemaFunctionCodeAName'"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'functionCodeB'" [flexGrow]="2">
      <ng-template epDatatableHeader><span [translate]="'audit.rici.schema.functionCodeB'"></span></ng-template>
      <ng-template epDatatableCell let-context>{{ cast(context.row).functionCodeBName }}</ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-text-with-null-filter [allowClear]="true"
                                                      [customFormControl]="functionCodeBNameFormControl"
                                                      [datatableService]="auditSchemaService"
                                                      [filterConfig]="functionCodeBNameFilterConfig"
                                                      [filterName]="'riciSchemaFunctionCodeBName'"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'functionCodeC'" [flexGrow]="2">
      <ng-template epDatatableHeader><span [translate]="'audit.rici.schema.functionCodeC'"></span></ng-template>
      <ng-template epDatatableCell let-context>{{ cast(context.row).functionCodeCName }}</ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-text-with-null-filter [allowClear]="true"
                                                      [customFormControl]="functionCodeCNameFormControl"
                                                      [datatableService]="auditSchemaService"
                                                      [filterConfig]="functionCodeCNameFilterConfig"
                                                      [filterName]="'riciSchemaFunctionCodeCName'"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'functionCodeD'" [flexGrow]="2">
      <ng-template epDatatableHeader><span [translate]="'audit.rici.schema.functionCodeD'"></span></ng-template>
      <ng-template epDatatableCell let-context>{{ cast(context.row).functionCodeDName }}</ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-text-with-null-filter [allowClear]="true"
                                                      [customFormControl]="functionCodeDNameFormControl"
                                                      [datatableService]="auditSchemaService"
                                                      [filterConfig]="functionCodeDNameFilterConfig"
                                                      [filterName]="'riciSchemaFunctionCodeDName'"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Mobile Detail Template -->
    <ng-template #template let-row="row">
      <div *ngIf="row.tecid">
        <cgdis-portal-audit-management-rici-schema-detail
          [audit]="row"></cgdis-portal-audit-management-rici-schema-detail>
      </div>
    </ng-template>
  </cgdis-portal-cgdisdatatable>
</div>
