import { Injectable } from '@angular/core';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { AuditRiciSchemaModel } from '@app/model/audit/audit-rici-schema.model'; // Use the correct model
import { RestService } from '@eportal/core';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';

@Injectable()
export class AuditManagementListRiciSchemaService extends CgdisDatatableService<AuditRiciSchemaModel> {
    constructor(
        restService: RestService,
        fb: UntypedFormBuilder,
        location: Location,
        router: Router,
        route: ActivatedRoute,
        popupService: SimplePopupService
    ) {
        super(fb, location, router, route, popupService, true);
        // Point to the correct endpoint for RICI Schema audits
        super.initDataResourceList(restService.all('audits', 'rici', 'schemas'));
    }
}
