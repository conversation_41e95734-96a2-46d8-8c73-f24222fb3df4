import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { SharedModule } from '@app/common/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { AuditManagementListRiciAlertGroupService } from '@app/audit-management/rici/alert-group/audit-management-list-rici-alert-group.service';
import { AuditRiciAlertGroupModel } from '@app/model/audit/audit-rici-alert-group.model';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { UntypedFormControl } from '@angular/forms';
import { AuditManagementModule } from '@app/audit-management/audit-management.module';
import { AuditTypeEnum } from '@app/model/audit/audit.enum';
import { AuditAlertGroupRangeNameColumnComponent } from '@app/audit-management/rici/alert-group/columns/audit-alert-group-range-name-column.component';
import { AuditAlertGroupSchemaAliasColumnComponent } from '@app/audit-management/rici/alert-group/columns/audit-alert-group-schema-alias-column.component';

@Component({
  selector: 'cgdis-portal-audit-management-list-rici-alert-group',
  standalone: true,
  imports: [
    DatatableModule,
    EpDatatableModule,
    SharedModule,
    TranslateModule,
    AuditManagementModule,
    AuditAlertGroupRangeNameColumnComponent,
    AuditAlertGroupSchemaAliasColumnComponent,
  ],
  templateUrl: './audit-management-list-rici-alert-group.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [AuditManagementListRiciAlertGroupService],
})
export class AuditManagementListRiciAlertGroupComponent {
  @Input() actionTypes: FieldOption<string>[];
  @Input() dateFormControl: UntypedFormControl;
  protected isMobile: boolean = false;
  protected readonly AuditTypeEnum = AuditTypeEnum;

  constructor(
    protected auditManagementListRiciAlertGroupService: AuditManagementListRiciAlertGroupService,
  ) {}

  cast(row: any): AuditRiciAlertGroupModel {
    return row as AuditRiciAlertGroupModel;
  }
}
