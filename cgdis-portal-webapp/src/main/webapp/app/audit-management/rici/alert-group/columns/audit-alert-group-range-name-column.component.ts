import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
} from '@angular/core';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { EpDatatableModule } from '@eportal/components';
import { SharedModule } from '@app/common/shared/shared.module';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { AuditRiciAlertGroupModel } from '@app/model/audit/audit-rici-alert-group.model';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';

@Component({
  selector: 'cgdis-portal-audit-alert-group-range-name-column',
  standalone: true,
  imports: [DatatableModule, EpDatatableModule, SharedModule],
  templateUrl: './audit-alert-group-range-name-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(() => AuditAlertGroupRangeNameColumnComponent),
    },
  ],
})
export class AuditAlertGroupRangeNameColumnComponent extends CgdisDatatableColumnComponent<AuditRiciAlertGroupModel> {
  @Input() auditService: CgdisDatatableService<AuditRiciAlertGroupModel>;

  constructor() {
    super();
  }
}
