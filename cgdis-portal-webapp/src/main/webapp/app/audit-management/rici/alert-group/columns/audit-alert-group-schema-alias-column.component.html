<ep-datatable-column [columnName]="'riciRicSchemaAlias'" [flexGrow]="3">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.rici.alert-group.headers.schema-alias'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <span class="text-wrap">{{ context.row.riciRicSchemaAlias | defaultValue:'-' }}</span>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-text-filter
      [datatableService]="auditService"
      [filterName]="'riciRicSchemaAlias'"
    ></cgdis-portal-datatable-text-filter>
  </ng-template>

</ep-datatable-column>

