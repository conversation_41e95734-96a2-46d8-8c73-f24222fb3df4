<ep-datatable-column [columnName]="'riciRicRangeName'" [flexGrow]="3">
  <ng-template epDatatableHeader>
    <span [translate]="'audit.rici.alert-group.headers.range-name'"></span>
  </ng-template>

  <ng-template epDatatableCell let-context>
    <span class="text-wrap">{{ context.row.riciRicRangeName | defaultValue:'-' }}</span>
  </ng-template>

  <ng-template epDatatableFilter>
    <cgdis-portal-datatable-text-filter
      [datatableService]="auditService"
      [filterName]="'riciRicRangeName'"
    ></cgdis-portal-datatable-text-filter>
  </ng-template>

</ep-datatable-column>

