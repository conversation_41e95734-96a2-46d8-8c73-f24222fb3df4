import {
  ChangeDetectionStrategy,
  Component,
  forwardRef,
  Input,
} from '@angular/core';
import { EpDatatableModule } from '@eportal/components';
import { SharedModule } from '@app/common/shared/shared.module';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { CgdisDatatableColumnComponent } from '@app/common/modules/datatable/cgdisdatatable-column.component';
import { AuditRiciAlertGroupModel } from '@app/model/audit/audit-rici-alert-group.model';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';

@Component({
  selector: 'cgdis-portal-audit-alert-group-schema-alias-column',
  standalone: true,
  imports: [EpDatatableModule, SharedModule, DatatableModule],
  templateUrl: './audit-alert-group-schema-alias-column.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: CgdisDatatableColumnComponent,
      useExisting: forwardRef(() => AuditAlertGroupSchemaAliasColumnComponent),
    },
  ],
})
export class AuditAlertGroupSchemaAliasColumnComponent extends CgdisDatatableColumnComponent<AuditRiciAlertGroupModel> {
  @Input() auditService: CgdisDatatableService<AuditRiciAlertGroupModel>;

  constructor() {
    super();
  }
}
