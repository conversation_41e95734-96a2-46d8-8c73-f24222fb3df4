<div class="accordion__panel">


  <cgdis-portal-cgdisdatatable
    [autoMobileFilters]="true"
    [class]="'entity__table'"
    [datatableService]="auditManagementListRiciAlertGroupService"
    [id]="'alert-group-list'"
    [showDetails]="'MOBILE'"
    [sorts]="[{dir:'desc',prop:'tecid'}]">


    <cgdis-portal-audit-action-date-column
      [auditService]="auditManagementListRiciAlertGroupService"
      [dateFormControl]="dateFormControl"
      cgdisDatatableColumn>
    </cgdis-portal-audit-action-date-column>

    <cgdis-portal-audit-person-tecid-column
      [columnHiddenMobile]="true"
      cgdisDatatableColumn
    ></cgdis-portal-audit-person-tecid-column>

    <cgdis-portal-audit-person-cgdis-registration-number-column
      [auditService]="auditManagementListRiciAlertGroupService"
      [columnHiddenMobile]="true"
      cgdisDatatableColumn
    ></cgdis-portal-audit-person-cgdis-registration-number-column>

    <cgdis-portal-audit-alert-group-range-name-column
      [auditService]="auditManagementListRiciAlertGroupService"
      cgdisDatatableColumn
    ></cgdis-portal-audit-alert-group-range-name-column>

    <cgdis-portal-audit-alert-group-schema-alias-column
      [auditService]="auditManagementListRiciAlertGroupService"
      cgdisDatatableColumn></cgdis-portal-audit-alert-group-schema-alias-column>

    <cgdis-portal-audit-action-type-column
      [auditService]="auditManagementListRiciAlertGroupService"
      [auditTypes]="[AuditTypeEnum.RICI_ALERT_GROUP]"
      cgdisDatatableColumn
    ></cgdis-portal-audit-action-type-column>
  </cgdis-portal-cgdisdatatable>
</div>
