import { Injectable } from '@angular/core';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { AuditRiciRicRangeModel } from '@app/model/audit/audit-rici-ric-range.model';
import { RestService } from '@eportal/core';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';

@Injectable()
export class AuditManagementListRiciRicRangeService extends CgdisDatatableService<AuditRiciRicRangeModel> {
  constructor(
    restService: RestService,
    fb: UntypedFormBuilder,
    location: Location,
    router: Router,
    route: ActivatedRoute,
    popupService: SimplePopupService,
  ) {
    super(fb, location, router, route, popupService, true);
    // Adjust the endpoint according to your API structure
    super.initDataResourceList(
      restService.all('audits', 'rici', 'ricranges'),
    );
  }
}
