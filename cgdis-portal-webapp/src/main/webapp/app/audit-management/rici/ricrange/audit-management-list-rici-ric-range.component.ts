import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { AuditManagementListRiciRicRangeService } from './audit-management-list-rici-ric-range.service';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { FormControl, UntypedFormControl } from '@angular/forms';
import { Subscription } from 'rxjs';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { AuditRiciRicRangeModel } from '@app/model/audit/audit-rici-ric-range.model';
import { NgIf } from '@angular/common';
import { SharedModule } from '@app/common/shared/shared.module';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { TranslateModule } from '@ngx-translate/core';
import { AuditManagementRiciRicRangeDetailComponent } from '@app/audit-management/rici/detail/ricrange/audit-management-rici-ric-range-detail.component';
import {
  EpDatatableModule,
  FilterConfig,
  SearchOperator,
} from '@eportal/components';
import { RiciRicRangeType } from '@app/model/rici/rici.model';
import _ from 'lodash';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'cgdis-portal-audit-management-list-rici-ric-range',
  templateUrl: './audit-management-list-rici-ric-range.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  providers: [AuditManagementListRiciRicRangeService],
  imports: [
    NgIf,
    SharedModule,
    DatatableModule,
    TranslateModule,
    EpDatatableModule,
    AuditManagementRiciRicRangeDetailComponent,
  ],
})
export class AuditManagementListRiciRicRangeComponent
  implements OnInit, OnDestroy
{
  @Input() actionTypes: FieldOption<string>[];
  @Input() dateFormControl: UntypedFormControl;
  showFilter = false;
  isMobile: boolean = false;

  numberOfFilters: number;

  nameFormControl = new FormControl<string>(undefined);
  nameFilterConfig: FilterConfig = { operator: SearchOperator.like };

  ricRangeTypeFormControl = new FormControl<string>(undefined); // Renamed from typeFormControl
  ricRangeTypeFilterConfig: FilterConfig = { operator: SearchOperator.eq }; // Renamed from typeFilterConfig
  ricRangeTypes: FieldOption<string>[];

  entityFormControl = new FormControl<string>(undefined); // Assuming entityTecid is searched as string/number
  entityFilterConfig: FilterConfig = { operator: SearchOperator.eq };

  private subscriptions: Subscription[] = [];

  constructor(
    public auditService: AuditManagementListRiciRicRangeService,
    private breakpointObserver: BreakpointObserver,
    private route: ActivatedRoute,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  cast(row: any): AuditRiciRicRangeModel {
    return row as AuditRiciRicRangeModel;
  }

  ngOnInit() {
    this.numberOfFilters = 0;
    this.loadTypes();

    this.subscriptions.push(
      this.auditService
        .canExecuteFirstSearch()
        .subscribe(() => this.updateFilterNumber()),
    );

    this.loadRicRangesURLParams();
  }

  loadRicRangesURLParams() {
    this.subscriptions.push(
      this.route.queryParams.subscribe((params) => {
        const name = params['rangeName'];
        this.nameFormControl.setValue(name);
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => {
      subscription.unsubscribe();
    });
  }

  updateFilterNumber() {
    this.numberOfFilters = this.auditService.getNumberOfFilters();
  }

  private loadTypes(): void {
    // Assuming RiciRicRangeType enum is available or fetched
    const allTypes = Object.values(RiciRicRangeType);
    this.ricRangeTypes = _.map(allTypes, (oneType) => {
      return new FieldGroupOption({
        // Assuming translation keys follow a pattern like 'rici.ranges.types.TYPE_VALUE'
        I18NLabel: 'rici.ranges.types.' + oneType.toLowerCase(),
        value: oneType,
      });
    });
  }
}
