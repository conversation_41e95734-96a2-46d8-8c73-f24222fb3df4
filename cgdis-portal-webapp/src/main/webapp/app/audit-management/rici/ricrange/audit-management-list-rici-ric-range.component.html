<div class="accordion__panel">

  <cgdis-portal-button-link (click)="showFilter = !showFilter; updateFilterNumber()" *ngIf="isMobile">
    <span *ngIf="!showFilter" before-icon>{{ 'service_plan.filter-link' | translate }} ({{ numberOfFilters }})</span>
    <span *ngIf="showFilter" before-icon>{{ 'service_plan.filter-link-toclose' | translate }} ({{ numberOfFilters }}
      )</span>
  </cgdis-portal-button-link>
  <ng-container *ngIf="isMobile">
    <div [hidden]="!showFilter" class="row search-filter">
      <!-- Common Filters -->
      <div class="col-md-2">
        <label [translate]="'audit.actionDateTime'" class="form-label"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()"
                                                  [customFormControl]="dateFormControl"
                                                  [datatableService]="auditService"
                                                  [filterName]="'actionDate'"
                                                  [initialValue]="dateFormControl.value"
                                                  class="informations-datepicker-filter"></cgdis-portal-datatable-datepicker-filter>
      </div>
      <div class="col-md-2">
        <label [translate]="'audit.actionType.title'" class="form-label"></label>
        <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true"
                                              [datatableService]="auditService"
                                              [filterName]="'actionType'"
                                              [possibleValues]="actionTypes"></cgdis-portal-datatable-select-filter>
      </div>

      <!-- RIC Range Specific Filters -->
      <div class="col-md-2">
        <label [translate]="'audit.rici.ricrange.name'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()"
                                                      [allowClear]="true"
                                                      [customFormControl]="nameFormControl"
                                                      [datatableService]="auditService"
                                                      [filterConfig]="nameFilterConfig"
                                                      [filterName]="'riciRicRangeName'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <div class="col-md-2">
        <label [translate]="'audit.rici.ricrange.type'" class="form-label"></label>
        <cgdis-portal-datatable-select-filter (onValueChanged)="updateFilterNumber()" [allowClear]="true"
                                              [customFormControl]="ricRangeTypeFormControl"
                                              [datatableService]="auditService"
                                              [filterConfig]="ricRangeTypeFilterConfig"
                                              [filterName]="'riciRicRangeType'"
                                              [possibleValues]="ricRangeTypes"></cgdis-portal-datatable-select-filter>
      </div>
      <div class="col-md-2">
        <label [translate]="'audit.rici.ricrange.entity'" class="form-label"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()"
                                                      [allowClear]="true"
                                                      [customFormControl]="entityFormControl"
                                                      [datatableService]="auditService"
                                                      [filterConfig]="entityFilterConfig"
                                                      [filterName]="'riciRicRangeEntityTecid'"></cgdis-portal-datatable-text-with-null-filter>
      </div>
    </div>
  </ng-container>

  <cgdis-portal-cgdisdatatable
    [class]="'entity__table'"
    [datatableService]="auditService"
    [id]="'audit-rici-ric-range-list'"
    [showDetails]="isMobile"
    [sorts]="[{dir:'desc',prop:'tecid'}]">

    <!-- Action date time -->
    <ep-datatable-column [columnName]="'actionDatetime'" [flexGrow]="3">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.actionDateTime'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{ cast(context.row).actionDatetime | dateTimeFormat }}
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-datepicker-filter
          [customFormControl]="dateFormControl"
          [datatableService]="auditService"
          [filterName]="'actionDate'"
          [initialValue]="dateFormControl.value"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- User -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'personTecid.lastName'" [flexGrow]="3">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.person'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <span
          class="text-wrap">{{ cast(context.row).personTecid.lastName }} {{ cast(context.row).personTecid.firstName }}</span>
      </ng-template>
    </ep-datatable-column>

    <ep-datatable-column *ngIf="!isMobile" [columnName]="'personTecid.cgdisRegistrationNumber'" [flexGrow]="3">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.cgdisregistrationnumber'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        {{ cast(context.row).personTecid.cgdisRegistrationNumber }}
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-text-with-null-filter
          [allowClear]="true"
          [datatableService]="auditService"
          [filterName]="'personTecid.cgdisRegistrationNumber'"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Name -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'riciRicRangeName'" [flexGrow]="3" [sortable]="false">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.rici.ricrange.name'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <div class="text-wrap">{{ cast(context.row).name | defaultValue }}</div>
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-text-with-null-filter
          [allowClear]="true"
          [customFormControl]="nameFormControl"
          [datatableService]="auditService"
          [filterConfig]="nameFilterConfig"
          [filterName]="'riciRicRangeName'"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Type -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'riciRicRangeType'" [flexGrow]="2" [sortable]="false">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.rici.ricrange.type'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <div
          class="text-wrap">{{ 'rici.ranges.types.' + cast(context.row).ricRangeType | lowercase | translate | defaultValue }}
        </div>
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-select-filter [allowClear]="true"
                                              [customFormControl]="ricRangeTypeFormControl"
                                              [datatableService]="auditService"
                                              [filterConfig]="ricRangeTypeFilterConfig"
                                              [filterName]="'riciRicRangeType'"
                                              [possibleValues]="ricRangeTypes"></cgdis-portal-datatable-select-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Range Start -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'rangeStart'" [flexGrow]="2" [sortable]="false">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.rici.ricrange.rangeStart'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <div class="text-wrap">{{ cast(context.row).rangeStart | defaultValue }}</div>
      </ng-template>
      <!-- No filter for range start/end for now -->
    </ep-datatable-column>

    <!-- Range End -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'rangeEnd'" [flexGrow]="2" [sortable]="false">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.rici.ricrange.rangeEnd'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <div class="text-wrap">{{ cast(context.row).rangeEnd | defaultValue }}</div>
      </ng-template>
      <!-- No filter for range start/end for now -->
    </ep-datatable-column>

    <!-- Entity -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'riciRicRangeEntityTecid'" [flexGrow]="3" [sortable]="false">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.rici.ricrange.entity'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <!-- TODO: Fetch and display entity name if needed -->
        <div class="text-wrap">{{ cast(context.row).entityTecid | defaultValue }}</div>
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-text-with-null-filter
          [allowClear]="true"
          [customFormControl]="entityFormControl"
          [datatableService]="auditService"
          [filterConfig]="entityFilterConfig"
          [filterName]="'riciRicRangeEntityTecid'"></cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Action type -->
    <ep-datatable-column [columnName]="'actionType'" [flexGrow]="3">
      <ng-template epDatatableHeader>
        <span [translate]="'audit.actionType.title'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>
        <span class="text-wrap">{{ 'audit.actionType.' + cast(context.row).actionType | translate }}</span>
      </ng-template>
      <ng-template *ngIf="!isMobile" epDatatableFilter>
        <cgdis-portal-datatable-select-filter [allowClear]="true" [datatableService]="auditService"
                                              [filterName]="'actionType'"
                                              [possibleValues]="actionTypes"></cgdis-portal-datatable-select-filter>
      </ng-template>
    </ep-datatable-column>

    <!-- Detail Template for Mobile View -->
    <ng-template #template let-row="row">
      <div *ngIf="row.tecid">
        <cgdis-portal-audit-management-rici-ric-range-detail
          [audit]="row"></cgdis-portal-audit-management-rici-ric-range-detail>
      </div>
    </ng-template>

  </cgdis-portal-cgdisdatatable>

</div>
