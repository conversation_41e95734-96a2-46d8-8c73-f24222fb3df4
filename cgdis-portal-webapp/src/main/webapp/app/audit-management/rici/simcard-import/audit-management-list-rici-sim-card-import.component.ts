import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { FormControl, UntypedFormControl } from '@angular/forms';
import { Subscription } from 'rxjs';
import { BreakpointObserver, BreakpointState } from '@angular/cdk/layout';
import { NgIf } from '@angular/common';
import { SharedModule } from '@app/common/shared/shared.module';
import { DatatableModule } from '@app/common/modules/datatable/datatable.module';
import { TranslateModule } from '@ngx-translate/core';
import {
  EpDatatableModule,
  FilterConfig,
  SearchOperator,
} from '@eportal/components';
import { FieldOption } from '@app/common/modules/form-module/model/field-option.model';
import { AuditRiciSimCardImportModel } from '@app/model/audit/audit-rici-sim-card-import.model';
import { AuditManagementListRiciSimCardImportService } from '@app/audit-management/rici/simcard-import/audit-management-list-rici-sim-card-import.service';
import { AuditManagementRiciSimCardImportDetailComponent } from '@app/audit-management/rici/simcard-import/detail/audit-management-rici-sim-card-import-detail.component';
import { RiciSimCardStatus } from '@rici/common/enums/sim-card-status.enum';
import { FieldGroupOption } from '@app/common/modules/form-module/model/field-group-option.model';
import * as _ from 'lodash';
import { PagerImportStatusEnum } from '@rici/common/enums/pager-import-status.enum';

@Component({
  selector: 'cgdis-portal-audit-management-list-rici-sim-card-import',
  templateUrl: './audit-management-list-rici-sim-card-import.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  providers: [AuditManagementListRiciSimCardImportService],
  imports: [
    NgIf,
    SharedModule,
    DatatableModule,
    TranslateModule,
    EpDatatableModule,
    AuditManagementRiciSimCardImportDetailComponent,
  ],
})
export class AuditManagementListRiciSimCardImportComponent
  implements OnInit, OnDestroy
{
  @Input() actionTypes: FieldOption<string>[];
  @Input() dateFormControl: UntypedFormControl;
  showFilter = false;
  isMobile = false;
  numberOfFilters: number;

  fileNameFormControl = new FormControl<string>(undefined);
  fileNameFilterConfig = new FilterConfig({ operator: SearchOperator.like });
  importStatusFormControl = new FormControl<string>(undefined);
  importStatusFilterConfig = new FilterConfig({
    operator: SearchOperator.like,
  });
  totalRecordsFormControl = new FormControl<number>(undefined);
  totalRecordsFilterConfig = new FilterConfig({ operator: SearchOperator.eq });
  successfulRecordsFormControl = new FormControl<number>(undefined);
  successfulRecordsFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
  });
  validationErrorsFormControl = new FormControl<number>(undefined);
  validationErrorsFilterConfig = new FilterConfig({
    operator: SearchOperator.eq,
  });
  importErrorsFormControl = new FormControl<number>(undefined);
  importErrorsFilterConfig = new FilterConfig({ operator: SearchOperator.eq });

  private subscriptions: Subscription[] = [];
  ricSimImportStatuses: FieldOption<PagerImportStatusEnum>[];

  constructor(
    public auditSimCardImportService: AuditManagementListRiciSimCardImportService,
    private breakpointObserver: BreakpointObserver,
    private cd: ChangeDetectorRef,
  ) {
    this.subscriptions.push(
      this.breakpointObserver
        .observe(['(max-width: 992px)'])
        .subscribe((result: BreakpointState) => {
          this.isMobile = result.matches;
          this.cd.markForCheck();
        }),
    );
  }

  cast(row: any): AuditRiciSimCardImportModel {
    return row as AuditRiciSimCardImportModel;
  }

  ngOnInit() {
    this.loadStatuses();
    this.numberOfFilters = 0;
    this.subscriptions.push(
      this.auditSimCardImportService.canExecuteFirstSearch().subscribe(() => {
        this.updateFilterNumber();
        // No specific URL params to pre-populate for import audits, but keeping the pattern
        // const urlParams = new URLSearchParams(window.location.search);
        // const hashParams = new URLSearchParams(window.location.hash.split('?')[1] || '');
        // const fileName = urlParams.get('fileName') || hashParams.get('fileName');
        // if (fileName) {
        //     this.fileNameFormControl.setValue(fileName);
        //     this.auditSimCardImportService.addFilterWithFormControl(
        //         'fileName',
        //         this.fileNameFormControl,
        //         this.fileNameFilterConfig
        //     );
        //     this.auditSimCardImportService.search();
        // }
      }),
    );
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((subscription) => subscription.unsubscribe());
  }

  updateFilterNumber() {
    this.numberOfFilters = this.auditSimCardImportService.getNumberOfFilters();
  }

  private loadStatuses(): void {
    // Assuming RiciSimCardStatus enum is available or fetched
    const allTypes = Object.values(PagerImportStatusEnum);
    this.ricSimImportStatuses = _.map(allTypes, (oneType) => {
      return new FieldGroupOption({
        // Assuming translation keys follow a pattern like 'rici.ranges.types.TYPE_VALUE'
        I18NLabel: 'audit.rici.simcard.import.statuses.' + oneType,
        value: oneType,
      });
    });
  }
}
