<div class="accordion__panel">
  <cgdis-portal-button-link *ngIf="isMobile" (click)="showFilter = !showFilter; updateFilterNumber()">
    <span *ngIf="!showFilter" before-icon>{{ 'service_plan.filter-link' | translate }} ({{ numberOfFilters }})</span>
    <span *ngIf="showFilter" before-icon>{{ 'service_plan.filter-link-toclose' | translate }} ({{ numberOfFilters }}
      )</span>
  </cgdis-portal-button-link>

  <ng-container *ngIf="isMobile">
    <div class="row search-filter" [hidden]="!showFilter">
      <div class="col-md-2">
        <label class="form-label" [translate]="'audit.actionDateTime'"></label>
        <cgdis-portal-datatable-datepicker-filter (onValueChanged)="updateFilterNumber()"
                                                  class="informations-datepicker-filter"
                                                  [customFormControl]="dateFormControl"
                                                  [initialValue]="dateFormControl.value" [filterName]="'actionDate'"
                                                  [datatableService]="auditSimCardImportService"></cgdis-portal-datatable-datepicker-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'audit.rici.simcard.import.fileName'"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()"
                                                      [allowClear]="true"
                                                      [customFormControl]="fileNameFormControl"
                                                      [filterName]="'riciSimCardImportFileName'"
                                                      [filterConfig]="fileNameFilterConfig"
                                                      [datatableService]="auditSimCardImportService"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'audit.rici.simcard.import.importStatus'"></label>
        <cgdis-portal-datatable-text-with-null-filter (onValueChanged)="updateFilterNumber()"
                                                      [allowClear]="true"
                                                      [customFormControl]="importStatusFormControl"
                                                      [filterName]="'riciSimCardImportStatus'"
                                                      [filterConfig]="importStatusFilterConfig"
                                                      [datatableService]="auditSimCardImportService"></cgdis-portal-datatable-text-with-null-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'audit.rici.simcard.import.totalRecords'"></label>
        <cgdis-portal-datatable-number-filter (onValueChanged)="updateFilterNumber()"
                                              [allowClear]="true"
                                              [customFormControl]="totalRecordsFormControl"
                                              [filterName]="'riciSimCardImportTotalRecords'"
                                              [filterConfig]="totalRecordsFilterConfig"
                                              [datatableService]="auditSimCardImportService"></cgdis-portal-datatable-number-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'audit.rici.simcard.import.successfulRecords'"></label>
        <cgdis-portal-datatable-number-filter (onValueChanged)="updateFilterNumber()"
                                              [allowClear]="true"
                                              [customFormControl]="successfulRecordsFormControl"
                                              [filterName]="'riciSimCardImportSuccessfulRecords'"
                                              [filterConfig]="successfulRecordsFilterConfig"
                                              [datatableService]="auditSimCardImportService"></cgdis-portal-datatable-number-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'audit.rici.simcard.import.validationErrors'"></label>
        <cgdis-portal-datatable-number-filter (onValueChanged)="updateFilterNumber()"
                                              [allowClear]="true"
                                              [customFormControl]="validationErrorsFormControl"
                                              [filterName]="'riciSimCardImportValidationErrors'"
                                              [filterConfig]="validationErrorsFilterConfig"
                                              [datatableService]="auditSimCardImportService"></cgdis-portal-datatable-number-filter>
      </div>
      <div class="col-md-2">
        <label class="form-label" [translate]="'audit.rici.simcard.import.importErrors'"></label>
        <cgdis-portal-datatable-number-filter (onValueChanged)="updateFilterNumber()"
                                              [allowClear]="true"
                                              [customFormControl]="importErrorsFormControl"
                                              [filterName]="'riciSimCardImportImportErrors'"
                                              [filterConfig]="importErrorsFilterConfig"
                                              [datatableService]="auditSimCardImportService"></cgdis-portal-datatable-number-filter>
      </div>
    </div>
  </ng-container>

  <cgdis-portal-cgdisdatatable [datatableService]="auditSimCardImportService" [id]="'audit-rici-sim-card-import-list'"
                               [sorts]="[{dir:'desc',prop:'tecid'}]" [showDetails]="isMobile" [class]="'entity__table'">
    <ep-datatable-column [columnName]="'actionDatetime'" [flexGrow]="1">
      <ng-template epDatatableHeader><span [translate]="'audit.actionDateTime'"></span></ng-template>
      <ng-template epDatatableCell let-context>{{ cast(context.row).actionDatetime | dateTimeFormat }}</ng-template>
      <ng-template epDatatableFilter *ngIf="!isMobile">
        <cgdis-portal-datatable-datepicker-filter [customFormControl]="dateFormControl"
                                                  [initialValue]="dateFormControl.value" [filterName]="'actionDate'"
                                                  [datatableService]="auditSimCardImportService"></cgdis-portal-datatable-datepicker-filter>
      </ng-template>
    </ep-datatable-column>
    <!-- User -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'personTecid.lastName'" [flexGrow]="1">
      <ng-template epDatatableHeader><span [translate]="'audit.person'"></span></ng-template>
      <ng-template epDatatableCell let-context>
        <span
          class="text-wrap">{{ cast(context.row).personTecid?.lastName }} {{ cast(context.row).personTecid?.firstName }}</span>
      </ng-template>
    </ep-datatable-column>
    <!-- CGDIS Registration Number -->
    <ep-datatable-column *ngIf="!isMobile" [columnName]="'personTecid.cgdisRegistrationNumber'" [flexGrow]="1">
      <ng-template epDatatableHeader><span [translate]="'audit.cgdisregistrationnumber'"></span></ng-template>
      <ng-template epDatatableCell let-context>
        {{ cast(context.row).personTecid?.cgdisRegistrationNumber }}
      </ng-template>
      <ng-template epDatatableFilter *ngIf="!isMobile">
        <cgdis-portal-datatable-text-with-null-filter
          [allowClear]="true"
          [filterName]="'personTecid.cgdisRegistrationNumber'"
          [datatableService]="auditSimCardImportService">
        </cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'fileName'" [flexGrow]="2">
      <ng-template epDatatableHeader><span [translate]="'audit.rici.simcard.import.fileName'"></span></ng-template>
      <ng-template epDatatableCell let-context>
        <div class="text-wrap">{{ cast(context.row).fileName | defaultValue }}</div>
      </ng-template>
      <ng-template epDatatableFilter *ngIf="!isMobile">
        <cgdis-portal-datatable-text-with-null-filter
          [allowClear]="true"
          [customFormControl]="fileNameFormControl"
          [filterName]="'riciSimCardImportFileName'"
          [filterConfig]="fileNameFilterConfig"
          [datatableService]="auditSimCardImportService">
        </cgdis-portal-datatable-text-with-null-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'importStatus'" [flexGrow]="2">
      <ng-template epDatatableHeader><span [translate]="'audit.rici.simcard.import.importStatus'"></span></ng-template>
      <ng-template epDatatableCell let-context>
        <div class="text-wrap">
          {{ 'audit.rici.simcard.import.statuses.' + cast(context.row).importStatus | translate }}
        </div>
      </ng-template>
      <ng-template epDatatableFilter *ngIf="!isMobile">
        <cgdis-portal-datatable-select-filter
          [allowClear]="true"
          [customFormControl]="importStatusFormControl"
          [filterName]="'riciSimCardImportStatus'"
          [filterConfig]="importStatusFilterConfig"
          [possibleValues]="ricSimImportStatuses"
          [datatableService]="auditSimCardImportService" >
        </cgdis-portal-datatable-select-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'totalRecords'" [flexGrow]="1">
      <ng-template epDatatableHeader><span [translate]="'audit.rici.simcard.import.totalRecords'"></span></ng-template>
      <ng-template epDatatableCell let-context>{{ cast(context.row).totalRecords }}</ng-template>
      <ng-template epDatatableFilter *ngIf="!isMobile">
        <cgdis-portal-datatable-number-filter
          [allowClear]="true"
          [customFormControl]="totalRecordsFormControl"
          [filterName]="'riciSimCardImportTotalRecords'"
          [filterConfig]="totalRecordsFilterConfig"
          [datatableService]="auditSimCardImportService">
        </cgdis-portal-datatable-number-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'successfulRecords'" [flexGrow]="1">
      <ng-template epDatatableHeader><span [translate]="'audit.rici.simcard.import.successfulRecords'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>{{ cast(context.row).successfulRecords }}</ng-template>
      <ng-template epDatatableFilter *ngIf="!isMobile">
        <cgdis-portal-datatable-number-filter
          [allowClear]="true"
          [customFormControl]="successfulRecordsFormControl"
          [filterName]="'riciSimCardImportSuccessfulRecords'"
          [filterConfig]="successfulRecordsFilterConfig"
          [datatableService]="auditSimCardImportService">
        </cgdis-portal-datatable-number-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'validationErrors'" [flexGrow]="1">
      <ng-template epDatatableHeader><span [translate]="'audit.rici.simcard.import.validationErrors'"></span>
      </ng-template>
      <ng-template epDatatableCell let-context>{{ cast(context.row).validationErrors }}</ng-template>
      <ng-template epDatatableFilter *ngIf="!isMobile">
        <cgdis-portal-datatable-number-filter
          [allowClear]="true"
          [customFormControl]="validationErrorsFormControl"
          [filterName]="'riciSimCardImportValidationErrors'"
          [filterConfig]="validationErrorsFilterConfig"
          [datatableService]="auditSimCardImportService">
        </cgdis-portal-datatable-number-filter>
      </ng-template>
    </ep-datatable-column>
    <ep-datatable-column [columnName]="'importErrors'" [flexGrow]="1">
      <ng-template epDatatableHeader><span [translate]="'audit.rici.simcard.import.importErrors'"></span></ng-template>
      <ng-template epDatatableCell let-context>{{ cast(context.row).importErrors }}</ng-template>
      <ng-template epDatatableFilter *ngIf="!isMobile">
        <cgdis-portal-datatable-number-filter
          [allowClear]="true"
          [customFormControl]="importErrorsFormControl"
          [filterName]="'riciSimCardImportImportErrors'"
          [filterConfig]="importErrorsFilterConfig"
          [datatableService]="auditSimCardImportService">
        </cgdis-portal-datatable-number-filter>
      </ng-template>
    </ep-datatable-column>

    <ng-template #template let-row="row">
      <div *ngIf="row.tecid">
        <cgdis-portal-audit-management-rici-sim-card-import-detail
          [audit]="row"></cgdis-portal-audit-management-rici-sim-card-import-detail>
      </div>
    </ng-template>
  </cgdis-portal-cgdisdatatable>
</div>
