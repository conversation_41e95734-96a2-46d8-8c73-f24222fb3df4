import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { SharedModule } from '@app/common/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { NgIf } from '@angular/common';
import { AuditRiciSimCardImportModel } from '@app/model/audit/audit-rici-sim-card-import.model';

@Component({
    selector: 'cgdis-portal-audit-management-rici-sim-card-import-detail',
    standalone: true,
    imports: [SharedModule, TranslateModule, NgIf],
    templateUrl: './audit-management-rici-sim-card-import-detail.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush
})
export class AuditManagementRiciSimCardImportDetailComponent {
    @Input() audit: AuditRiciSimCardImportModel;
}
