import { Injectable } from '@angular/core';
import { CgdisDatatableService } from '@app/common/modules/datatable/cgdisdatatable-service';
import { RestService } from '@eportal/core';
import { UntypedFormBuilder } from '@angular/forms';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { SimplePopupService } from '@app/common/modules/popup/simple-popup.service';
import { AuditRiciSimCardImportModel } from '@app/model/audit/audit-rici-sim-card-import.model';

/**
 * Service for managing datatable operations for RICI SIM Card Import audit records.
 */
@Injectable()
export class AuditManagementListRiciSimCardImportService extends CgdisDatatableService<AuditRiciSimCardImportModel> {
    constructor(
        restService: RestService,
        fb: UntypedFormBuilder,
        location: Location,
        router: Router,
        route: ActivatedRoute,
        popupService: SimplePopupService
    ) {
        super(fb, location, router, route, popupService, true);
        // Initialize the data resource list to the new dedicated endpoint for SIM card import audits
        super.initDataResourceList(restService.all('audits', 'rici', 'sim-card-imports'));
    }
}
