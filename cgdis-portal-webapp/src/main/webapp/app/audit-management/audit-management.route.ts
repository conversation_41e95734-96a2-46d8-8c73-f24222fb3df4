import { Routes } from '@angular/router';
import { AuditManagementComponent } from '@app/audit-management/audit-management.component';
import { AuditManagementListComponent } from '@app/audit-management/list/audit-management-list.component';
import { RoleGuard } from '@app/security/guards/role.guard';

export const AUDIT_MANAGEMENT_ROUTES: Routes = [
  {
    path: '',
    component: AuditManagementComponent,
    children: [
      { path: '', redirectTo: 'list', pathMatch: 'full' },
      {
        path: 'list',
        component: AuditManagementListComponent,
        canActivate: [RoleGuard],
        data: {
          titleKey: 'auditmanagement.list.title',
          guardSameParentRedirect: 'permamonitor',
          expectedRoles: ['ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL'],
        },
      },
      {
        path: 'permamonitor',
        loadChildren: () =>
          import('./permamonitor/audit-permamonitor.routing').then(
            (m) => m.AUDIT_PERMAMONITOR_ROUTES,
          ),
        canActivate: [RoleGuard],
        data: {
          titleKey: 'auditmanagement.permamonitor.title',
          guardSameParentRedirect: 'list',
          expectedRoles: ['ROLE_PERMISSION_ADMIN_AUDIT_PERMAMONITOR'],
        },
      },
      {
        path: 'rici',
        loadChildren: () =>
          import('./rici/audit-rici.routing').then((m) => m.AUDIT_RICI_ROUTES),
        canActivate: [RoleGuard],
        data: {
          titleKey: 'auditmanagement.rici.title',
          guardSameParentRedirect: 'list',
          expectedRoles: ['ROLE_PERMISSION_ADMIN_AUDIT_PERMAMONITOR'],
        },
      },
      {
        path: 'fos',
        loadChildren: () =>
          import(
            './functionoperational/audit-function-operational.routing'
          ).then((m) => m.AUDIT_FUNCTION_OPERATIONAL_ROUTES),
        canActivate: [RoleGuard],
        data: {
          titleKey: 'auditmanagement.function-operational.title',
          guardSameParentRedirect: 'list',
          expectedRoles: ['ROLE_PERMISSION_ADMIN_AUDIT_FO'],
        },
      },
    ],
  },
];
