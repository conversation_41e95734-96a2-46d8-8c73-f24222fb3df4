import { Component, OnInit } from '@angular/core';
import { TabsHeadersItem } from '@app/common/modules/tabs-headers/tabs-headers-item';

@Component({
  selector: 'cgdis-portal-audit-management',
  templateUrl: './audit-management.component.html',
})
export class AuditManagementComponent implements OnInit {
  tabsHeaders: TabsHeadersItem[];
  constructor() {}

  ngOnInit(): void {
    this.tabsHeaders = [
      new TabsHeadersItem({
        routerLink: 'list',
        labelKey: 'audit.tabs.headers.portal',
        visibleRoles: ['ROLE_PERMISSION_ADMIN_AUDIT_PORTAIL'],
      }),
      new TabsHeadersItem({
        routerLink: 'fos',
        labelKey: 'audit.tabs.headers.function-operational',
        visibleRoles: ['ROLE_PERMISSION_ADMIN_AUDIT_FO'],
      }),
      new TabsHeadersItem({
        routerLink: 'permamonitor',
        labelKey: 'audit.tabs.headers.permamonitor',
        visibleRoles: ['ROLE_PERMISSION_ADMIN_AUDIT_PERMAMONITOR'],
      }),
      new TabsHeadersItem({
        routerLink: 'rici',
        labelKey: 'audit.rici.tabs.headers.rici',
        visibleRoles: ['ROLE_PERMISSION_ADMIN_AUDIT_RICI'],
      }),
    ];
  }
}
