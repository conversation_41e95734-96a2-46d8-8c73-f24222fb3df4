cgdis-portal-people-management-function-table-result,
cgdis-portal-person-function-operational-table {
  .scrollable-content {
    .ng-scrollbar-view {
      max-height: 50vh;
    }
  }

  .hide-scroll {
    .scrollable-content {
      .ng-scrollbar-view {
        max-height: 50vh;
      }
    }
  }
}

cgdis-portal-people-management-function-table-result {
  planner__people {
    flex-basis: auto !important;
  }

  cgdis-portal-checkbox-field {
    .form-item {
      margin: 0;
      text-align-last: center;

      > label {
        display: none !important;
      }

      input[type=checkbox]:not(.-slide) + label,
      label {
        display: inline-block;
      }
    }
  }
}

cgdis-portal-people-management-function-table-functions {

  .field {
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.5);
    background-position: right center;
    background-size: 1.5rem;
    background-repeat: no-repeat;
    width: 12rem;

    i {
      color: white;
    }

    input {
      padding-right: 3rem !important;
      padding-bottom: 0 !important;
      height: 2.3rem !important;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .close-icon {
      left: 11rem;
    }

    .close-icon,
    .search-icon {
      right: unset !important;
      @media (max-width: 768px) {
        top: 2.5rem;
      }
    }
  }

  .planner__header-jobs {
    display: flex;
    align-items: flex-end;
  }

  cgdis-portal-input-text {
    padding: 0 1rem;

    .form-item {
      display: flex;
      flex-direction: column;
      width: 13rem;

      label {
        color: #fff;
      }
    }
  }
}


cgdis-portal-admin-people-management-functions {
  .people-management-function-table-functions__filter {
    padding: 1rem;
    color: white;
  }

  .people-management-function-table-result__person,
  .people-management-function-table-functions__filter {
    width: 16rem;
  }

  .ngx-select__toggle.btn.form-control {
    background: none;
  }

  .ngx-select.dropdown {
    border: none;
  }

  ngx-select {
    border: none;
    background: transparent;

    .ngx-select__clear {
      margin-right: 0 !important;
      margin-left: 0.75em;
    }

    .ngx-select a.ngx-select__clear {
      margin-top: 0;
    }

    .ngx-select__clear-icon {
      width: 0.75em !important;
    }
  }

  .ngx-select__choices {
    width: unset !important;
    min-width: 100% !important;
  }

  span.ngx-select__placeholder.text-muted.ng-star-inserted {
    line-height: 2.4em !important;
  }

  .ngx-select__toggle.btn.form-control {
    @media (min-width: 800px) {
      padding: 0;
      height: 2.4em;
      max-height: 2.4em;
      line-height: 0.25em;
      span {
        line-height: normal;
      }
    }
  }

  input.ngx-select__search.form-control.ng-star-inserted {
    height: 2em;
  }

  .planner {
    &__people {
      align-items: flex-start;
      padding: 1.5rem;
    }

    &__header-jobs {
      height: 7rem;
      max-height: 7rem;

      .planner__people-list {
        display: flex;
        //flex-direction: column;
        //transform: rotate(-90deg);
        //max-width: 11rem;
        align-self: stretch;
        align-items: stretch;
        height: inherit;
        max-height: inherit;

        .functions_people {
          height: inherit;
          max-height: inherit;
          padding: 0;
          justify-content: center;
          align-items: center;
          width: 12rem;

          .functions_people-label {
            padding: 0.5rem;
            text-align: center;
          }
        }
      }
    }
  }

  .schedule {
    @include media-breakpoint-up(lg) {
      .tabs &__selector {
        .schedule,
        > [role='tab'],
        > [role\.tab='tab'] {
          &.-actions {
            height: 11rem;
            max-height: 11rem;
          }
        }
      }
    }
  }

  .position-scrollbar {
    max-height: 11rem;
  }
}


