cgdis-portal-permamonitor {
  cgdis-portal-permamonitor-filters {
    cgdis-portal-permamonitor-period-evaluation-filter {
      @include media-breakpoint-down(sm) {
        width: 100%;
      }
    }

    cgdis-portal-permamonitor-region-filter {
      @include media-breakpoint-down(sm) {
        max-width: 80%;
      }
    }

    .add-days-buttons {
      margin-left: 10px;
      display: flex;
      gap: 10px;

      .btn {
        width: 30px;
        height: 27px;
      }
    }

    .btn {
      &.-compact {
        padding: 0.2em 0.5em;
      }
    }

    cgdis-portal-button-group-selector {
      box-sizing: border-box;
    }

    cgdis-portal-panel {
      display: none;

      .panel-body {
        margin-bottom: 10px;
      }
    }

    .accordion__group {
      display: block;
      //padding: 10px !important;

      .mat-expansion-panel-header {
        &.mat-expanded {
          height: 20px !important;
          justify-content: end;

          .mat-content {
            display: none;
          }
        }

        height: 20px !important;
      }

      .mat-expansion-panel-content {
        .mat-expansion-panel-body {
          padding-bottom: 0 !important;
        }
      }
    }

    @include media-breakpoint-down(sm) {
      .accordion__group {
        display: block;
      }
    }

    .panel-body {
      margin-bottom: 20px;

      mat-divider {
        margin-bottom: 20px;
      }

      .filters {
        padding-left: 10px;
        padding-right: 10px;
        display: flex;
        column-gap: 50px;
        row-gap: 10px;
        flex-wrap: wrap;
        @include media-breakpoint-down(sm) {
          column-gap: 10px;
        }

        .filter-group {
          .filter {
            display: flex;
            justify-content: center;
            //min-height: 40px;
            @include media-breakpoint-down(sm) {
              //min-height: 20px;
            }

            label {
              font-size: 1.4rem;
              margin-bottom: 0;
            }
          }

          .filter-group-label {
            color: $c-primary;
            font-weight: bold;
            font-family: $ff-heading;
          }

          &.region {
            .filter {
              display: flex;
              flex-direction: column;
              row-gap: 8px;

              .group-selector {
                > div {
                  margin-top: 5px;
                }
              }
            }

            @include media-breakpoint-down(sm) {
              .region-filters {
                .button-group-selector {
                  flex-wrap: wrap;
                }

                .zone-selector,
                .group-selector {
                }
              }

              .btn {
                text-overflow: ellipsis;
                overflow: hidden;
              }
            }
          }

          &.period-evolution {
            width: 650px;
            max-width: 100%;

            .filter {
              display: flex;
              gap: 20px;
              @include media-breakpoint-down(sm) {
                flex-wrap: wrap;
                justify-content: left;
              }

              .date-picker {
                box-sizing: border-box;
                width: 140px;
              }

              .evolution {
                display: flex;
                gap: 10px;

                .btn {
                  width: 30px;
                  height: 27px;
                }
              }

              .slider {
                width: 100%;
                height: 70px;

                .ng2-nouislider {
                  margin-top: 0;
                }

                .current-time {
                  margin-right: auto;
                  margin-left: auto;
                  width: fit-content;
                  font-weight: 300;
                }
              }
            }
          }
        }
      }
    }
  }
}
