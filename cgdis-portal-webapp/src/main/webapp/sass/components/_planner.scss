@use 'sass:math';
.planner {
  &__people-list {
    @extend %unstyled-list;

    &.-placeholder {
      font-family: $ff-heading;

      .planner__people {
        max-height: 5rem;
        color: #fff;
        font-size: 1.4rem;
        line-height: 2rem;
        text-transform: uppercase;

        svg {
          display: inline-block;
          margin-right: 0.5rem;
          width: 1.5rem;
          height: 1.5rem;
        }

        &.-optional {
          color: rgba(#fff, 0.6);
        }

        &.additional {
          background-color: rgb(69, 96, 186) !important;
          padding: 1rem;
        }

        &.complete {
          background-color: rgb(55, 77, 149) !important;
          padding: 1rem;
        }

        &.degraded {
          background-color: $c-primary;
          padding: 1rem;
        }
      }
    }
  }

  &__people {
    display: flex;
    position: relative;
    align-items: center;
    padding: 1.5rem 0;
    min-height: 5rem;
    color: $c-secondary;

    svg:not(.icon-delete):not(.icon-chevron):not(.icon-barracked) {
      display: inline-block;
      vertical-align: middle;
      margin-right: 3rem;
      width: 3.5rem;
      height: 3.5rem;
    }

    svg.icon-barracked {
      width: 2rem;
      height: 2rem;
    }

    &:before {
      //content: '';
      //height: .1rem !important;
      //position: absolute !important;
      //left: 0 !important;
      //right: 0 !important;
      //top: 0 !important;
      //__<<ngThemingMigrationEscapedComment0>>__
      //background: -moz-linear-gradient(left, rgba(246,246,246,0) 0%, rgba(246,246,246,1) 10%, rgba(246,246,246,1) 90%, rgba(246,246,246,0) 100%); __<<ngThemingMigrationEscapedComment1>>__
      //background: -webkit-linear-gradient(left, rgba(246,246,246,0) 0%,rgba(246,246,246,1) 10%,rgba(246,246,246,1) 90%,rgba(246,246,246,0) 100%); __<<ngThemingMigrationEscapedComment2>>__
      //background: linear-gradient(to right, rgba(246,246,246,0) 0%,rgba(246,246,246,1) 10%,rgba(246,246,246,1) 90%,rgba(246,246,246,0) 100%); __<<ngThemingMigrationEscapedComment3>>__
      //filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#00f6f6f6', endColorstr='#00f6f6f6',GradientType=.1 ); __<<ngThemingMigrationEscapedComment4>>__
    }

    &.-optional {
      &:before {
        border-bottom: 0.1rem dashed #f6f6f6;
        height: 0;
      }
    }

    &-list {
      .planner__people {
        justify-content: space-between;

        .add_person {
          @media (max-width: 1400px) and (min-width: 1000px) {
            //max-width: 60px;
          }

          .add {
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }

      li {
        font-size: 1.3rem;
      }
    }
  }

  &__header-jobs {
    background-color: $c-primary;

    height: 5rem;
    max-height: 5rem;
    .planner__people-list {
      height: 5rem;
      max-height: 5rem;
    }

    &.-loading {
      overflow: hidden;
    }
  }

  /*@include media-breakpoint-down(md) {
      &__header-jobs {
          display: none;
      }

      &__people-list {
          &.-placeholder {
              display: none;
          }
      }
  }*/

  @include media-breakpoint-up(xl) {
    &__people {
      width: 23rem;
    }
  }

  @include media-breakpoint-down(lg) {
    &__people {
      width: 14rem;
    }
  }
  @include media-breakpoint-down(md) {
    &__people {
      width: 100%;
    }
  }

  @include media-breakpoint-up(lg) {
    &__people-list {
      display: flex;
      flex-wrap: nowrap;
      height: 6rem;

      .-incomplete &,
      .-empty &,
      .-degraded & {
        background-color: #fbfbfb;
      }
    }

    &__people {
      display: flex;

      flex-grow: 1;
      flex-shrink: 0;
      align-items: center;
      max-height: 6rem;
      overflow-y: hidden;
      font-size: 1.2rem;
      line-height: 2rem;

      &.-optional {
        border-left: 0.1rem dashed #f6f6f6;
        background-color: #f6f6f6;
        color: $c-gray;
      }

      &.six_serviceplan {
        flex-shrink: unset !important;
      }

      svg:not(.icon-delete):not(.icon-chevron):not(.icon-barracked) {
        display: none;
      }
    }
  }
}

.align-person {
  padding-left: 30%;
}

.people {
  display: inline-block;
  vertical-align: middle;

  &__firstname {
    display: block;
    font-weight: 300;
  }

  &__lastname {
    display: block;
    font-weight: 500;
    &--small-font {
      font-size: 1.1rem;
    }
  }

  @include media-breakpoint-up(lg) {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;

    &__firstname,
    &__lastname {
      width: 100%;
    }
  }
}

.week-selector {
  margin: 0 auto;
  padding: math.div($grid-gutter-width, 2);

  text-align: center;

  &__title {
    @extend .h3-like;
    vertical-align: middle;
    padding: 0 math.div($grid-gutter-width, 2);
  }

  &__button {
    @extend %unstyled-button;
    vertical-align: middle;
    width: 2.4rem;
    height: 2.4rem;
    color: $c-primary;
  }

  &__one {
    text-align: center;
    @media (max-width: 1401px) {
      margin-top: 15px;
      margin-bottom: 25px;
      padding-top: 15px;
    }

    @media (max-width: 768px) {
      margin-top: 45px !important;
      padding-top: 45px !important;
      padding-left: 5px !important;
    }

    @media (max-width: 500px) {
      margin-top: 0 !important;
      padding-top: 15px !important;
    }

    &__day {
      display: flex;
      flex-direction: row;

      &__name {
        @extend .h3-like;
        vertical-align: middle;
      }
    }
  }
}

.publicHoliday {
  background-color: #fff9e7;
}

.day-selector {
  display: flex;
  position: relative;
  align-items: center;
  text-transform: capitalize;

  :focus {
    outline: none;
  }

  &__title {
    flex-grow: 1;
    overflow-x: hidden;
    font-family: $ff-heading;
    text-align: center;
  }

  &__button {
    @extend %unstyled-button;
    display: inline-block;
    flex-grow: 0;
    flex-basis: 2.4rem;
    width: 2.4rem;
    height: 2.4rem;
    color: $c-primary;
  }

  &__month-name {
    display: none;
    width: 10rem;
    color: $c-secondary;
    font-size: 2rem;
    font-family: $ff-heading;
    text-align: center;
  }

  &__name {
    display: block;
    font-size: 1.5rem !important;
  }

  &__number {
    display: block;
    font-size: 1.8rem;
  }

  &__occupancy {
    font-size: 1.3rem;
    font-family: $ff-base;

    .progress-bar {
      margin: 0 auto;
      max-width: 3rem;
      height: 0.6rem;

      [aria-selected='false'] & {
        border-color: $c-gray-light;

        .progress-bar__progress {
          background-color: $c-gray-light;
        }
      }
    }
  }

  @include media-breakpoint-down(lg) {
    &__occupancy {
      display: none;
    }

    &__title {
      > [role='tab'][aria-selected='true'],
      > [role\.tab='tab'][aria-selected='true'] {
        color: $c-secondary-lighter;

        &:before {
          display: none;
        }
      }
    }
  }

  @include media-breakpoint-between(md, lg) {
    &__title {
      > [role='tab'],
      > [role\.tab='tab'] {
        flex-basis: 25%;
      }
    }
  }

  @include media-breakpoint-up(xl) {
    display: flex;
    justify-content: center;
    align-items: center;

    &__month-name {
      display: block;
    }

    &__title {
      flex-grow: 1;
      max-width: 100%;
      overflow-x: initial;
    }

    &__name {
      font-size: 1.3rem;
      font-family: $ff-base;
    }

    &__number {
      font-size: 2.6rem;
    }

    &__button {
      display: none;
    }

    .js-fillday {
      display: none;
    }
  }
}

.example-block__item {
  width: 85% !important;
}

.slot-selector {
  display: flex;
}

.mobile-split {
  align-self: center;
  margin-left: 1rem;
}

.schedule {
  display: flex;
  align-items: center;
  color: $c-gray-lighter;

  &__status {
    display: inline-block;
    vertical-align: middle;
    padding: 0;
    line-height: 3rem;
    text-align: center;
    @media (max-width: 768px) {
      padding-right: 5px;
      font-size: 1.5em !important;
    }
    @media (min-width: 768px) {
      margin-left: 0.4rem;
      font-size: 2em;
    }
  }

  &__start {
    vertical-align: middle;
    color: $c-gray;

    &::after {
      content: ' - ';
    }
  }

  &__end {
    vertical-align: middle;
    color: $c-gray-lighter;
  }

  @include media-breakpoint-down(md) {
    .tabs &__selector {
      display: none;
    }
    &__action {
      display: none;
    }

    &__selector {
      display: none;
    }
  }

  @include media-breakpoint-up(lg) {
    justify-content: space-between;
    font-size: 1.2rem;
    line-height: 1.5rem;

    &:not(.-complete) {
      background-color: #fbfbfb;
    }

    .tabs &__selector {
      .with__team {
        min-width: 15rem;
      }

      .without__team {
        min-width: 14rem;
      }

      //padding-bottom: 2.5rem;

      .schedule {
        .schedule-selector-icons {
          display: flex;
          flex-basis: 100%;
          flex-direction: unset;
          flex-wrap: wrap;
          align-content: space-around;
          justify-content: space-between;
          max-width: 4.3rem;
          height: 100%;
        }

        &.-actions {
          flex-direction: row-reverse;
          padding-right: 1rem;
        }
      }

      > [role='tab'],
      > [role\.tab='tab'] {
        &.-actions {
          [class*='icon-time'] {
            margin: auto 0.8rem auto 11.5rem;
          }
        }
      }

      .schedule,
      > [role='tab'],
      > [role\.tab='tab'] {
        flex-basis: auto;
        cursor: default;
        padding: 0 1rem !important;
        height: 6rem;
        max-height: 6rem;

        &[aria-selected='true'],
        &:hover {
          color: $c-gray-lighter;
        }

        &.-actions {
          background-color: $c-primary;
          height: 5rem;
          max-height: 5rem;
          color: #fff;
          //padding: 1rem 1.5rem;

          [class*='icon-time'] {
            width: 1.2rem;
            height: 1.2rem;
          }
        }

        + .schedule,
        + [role='tab'],
        + [role\.tab='tab'] {
          position: relative;

          &[aria-selected='true']:before,
          &:before {
            display: block;
            position: absolute;
            top: 0;
            right: 0;
            left: 0;
            border-top: 0.1rem solid $c-gray-lightest;
            background-color: transparent;
            content: '';
          }
        }

        &[aria-selected='true'] {
          box-shadow: none;

          &:before {
            display: none;
          }
        }
      }
    }

    &__end,
    &__start {
      display: block;
      font-family: $ff-base;

      &::after {
        content: '';
      }
    }

    &__status {
      float: left;
    }
  }
}

.service-planner {
  &__popup {
    position: relative;
    margin: 0 auto;
    padding: 2rem;
    min-height: 4.4rem; // close button

    .mfp-close-btn-in & .mfp-close {
      color: #fff;
    }

    &-header {
      padding: 4rem 4rem 0;
      color: #fff;
      line-height: 1;
    }

    &-day {
      font-size: 2.3rem;
    }

    &-schedule {
      margin-bottom: 3rem;
      color: rgba(#fff, 0.6);
      font-size: 1.8rem;
    }

    &-job {
      margin-bottom: 2rem;
      font-size: 1.4rem;
      font-family: $ff-heading;
      text-transform: uppercase;

      svg {
        margin-right: 0.5rem;
        width: 1.3rem;
        height: 1.3rem;
      }
    }

    &-switch-person {
      font-size: 14px;

      .ngx-select__toggle {
        padding: 0.5rem;
      }
    }
    .mat-mdc-tab:hover .mdc-tab__text-label {
      color: #fff;
    }
    [role='tablist'] {
      background-color: $c-secondary;
      padding: 0 4rem;

      [role='tab'],
      [role\.tab='tab'] {
        flex-grow: 0;
        flex-basis: auto;
        //padding: 1rem 2rem;
        outline: none;
        border-top-right-radius: 0.2rem;
        border-top-left-radius: 0.2rem;
        background-color: $c-secondary;
        color: rgba(#fff, 0.6);
        font-size: 1.6rem;

        &:hover {
          color: #fff;
        }

        &[aria-selected='true'] {
          box-shadow: none;
          background-color: #fff;
          color: $c-secondary;

          &:hover,
          &:focus {
            outline: none;
            color: $c-secondary;
          }

          &:before {
            display: none;
          }
        }
      }
    }

    [role='tabpanel'] {
      padding: 4rem;
    }

    &-search {
      margin-bottom: 2rem;
      outline: none;
      border: none;
      border-bottom: 0.1rem solid rgba(#757f8d, 0.2);
      background-size: 15px 15px;
      background: #fff
        url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgdmVyc2lvbj0iMS4xIgogICBpZD0ic3ZnNDI1MCIKICAgdmlld0JveD0iMCAwIDI0IDIzLjk5NDE2NyIKICAgaGVpZ2h0PSIyMy45OTQxNjciCiAgIHdpZHRoPSIyNCI+CiAgPG1ldGFkYXRhCiAgICAgaWQ9Im1ldGFkYXRhNDI1OCI+CiAgICA8cmRmOlJERj4KICAgICAgPGNjOldvcmsKICAgICAgICAgcmRmOmFib3V0PSIiPgogICAgICAgIDxkYzpmb3JtYXQ+aW1hZ2Uvc3ZnK3htbDwvZGM6Zm9ybWF0PgogICAgICAgIDxkYzp0eXBlCiAgICAgICAgICAgcmRmOnJlc291cmNlPSJodHRwOi8vcHVybC5vcmcvZGMvZGNtaXR5cGUvU3RpbGxJbWFnZSIgLz4KICAgICAgICA8ZGM6dGl0bGU+PC9kYzp0aXRsZT4KICAgICAgPC9jYzpXb3JrPgogICAgPC9yZGY6UkRGPgogIDwvbWV0YWRhdGE+CiAgPGRlZnMKICAgICBpZD0iZGVmczQyNTYiIC8+CiAgPHBhdGgKICAgICBzdHlsZT0iZmlsbDojNzU3ZjhkO2ZpbGwtb3BhY2l0eToxIgogICAgIGlkPSJwYXRoNDI1MiIKICAgICBkPSJtIDI0LDIxLjk3ODU0MyAtNi40MjE4NzUsLTYuNDIxODc1IGMgMi44MTI1LC0zLjc5Njg3NSAyLjUzMTI1LC05LjIzNDM3NTEgLTAuOTM3NSwtMTIuNzAzMTI1MSBDIDEyLjc5Njg3NSwtMC45NDMzMzIwOCA2LjY1NjI1LC0wLjk0MzMzMjA4IDIuODEyNSwyLjgwNjY2NzkgYyAtMy43NSwzLjg0Mzc1IC0zLjc1LDkuOTg0Mzc1MSAwLDEzLjc4MTI1MDEgMy40Njg3NSwzLjQ2ODc1IDguOTA2MjUsMy43OTY4NzUgMTIuNzAzMTI1LDAuOTM3NSBsIDYuNDY4NzUsNi40Njg3NSB6IE0gOS43NSwxNi42ODE2NjggYyAtMS44NzUsMCAtMy42MDkzNzUsLTAuNzAzMTI1IC00Ljk2ODc1LC0yLjA2MjUgLTEuMzEyNSwtMS4zMTI1IC0yLjA2MjUsLTMuMDQ2ODc1IC0yLjA2MjUsLTQuOTIxODc1MSAwLC0xLjg3NSAwLjc1LC0zLjYwOTM3NSAyLjA2MjUsLTQuOTY4NzUgMS4zNTkzNzUsLTEuMzEyNSAzLjA5Mzc1LC0yLjA2MjUgNC45Njg3NSwtMi4wNjI1IDEuODc1LDAgMy42MDkzNzUsMC43NSA0LjkyMTg3NSwyLjA2MjUgMS4zNTkzNzUsMS4zNTkzNzUgMi4wNjI1LDMuMDkzNzUgMi4wNjI1LDQuOTY4NzUgMCwxLjg3NTAwMDEgLTAuNzAzMTI1LDMuNjA5Mzc1MSAtMi4wNjI1LDQuOTIxODc1MSAtMS4zMTI1LDEuMzU5Mzc1IC0zLjA0Njg3NSwyLjA2MjUgLTQuOTIxODc1LDIuMDYyNSB6IiAvPgo8L3N2Zz4K')
        no-repeat center right;
      padding: 1rem 3.5rem 1rem 2rem;
      width: 100%;
      color: #757f8d;
      font-size: 1.5rem;
      line-height: 1;
      text-overflow: ellipsis;

      &:-moz-placeholder,
      &::-moz-placeholder,
      &:-moz-placeholder,
      &::-moz-placeholder,
      &::-webkit-input-placeholder {
        color: #757f8d;
      }
    }

    .people-selector {
      .noFunctions {
        color: lightgrey;
      }

      &.-selection-disable {
        label {
          cursor: inherit;
        }
      }

      &:hover {
        &:not(.-selection-disable) {
          background-color: #f8f9ff;
        }
      }

      .availabilities-duration {
        margin: 0;
        margin-bottom: 1rem;
      }

      .availabilities-entity {
        //position: absolute;
        left: 16em;
        width: 15em;
        @media (max-width: 768px) {
          display: flex;
          width: 26%;
        }
        padding-top: 0.3em;
        padding-right: 1rem;
        padding-left: 1rem;

        height: 100%;
        text-align: left;

        button.show-more-people-stripe__button.ng-star-inserted {
          position: absolute;
        }
      }

      @media (min-width: 768px) {
        .people-selector_column-icons {
          min-width: 5.4rem;
        }
      }

      position: relative;

      .availabilities-interventions,
      .availabilities-interventions-prestations {
        min-width: 5.4rem;
      }

      .availabilities-prestations {
        //position: absolute;
        left: 16em;
        width: 20em;
        @media (max-width: 768px) {
          display: flex;
          width: 26%;
        }
        padding-top: 0.3em;
        padding-right: 1rem;
        padding-left: 1rem;

        height: 100%;
        text-align: left;

        button.show-more-people-stripe__button.ng-star-inserted {
          position: absolute;
        }
      }

      .availabilities-person {
        width: 140px;
      }

      .availabilities-interventions {
        display: flex;

        //width: 65px;
        text-align: center;
      }

      .availabilities-interventions-prestations {
        width: 150px;
        text-align: center;
      }

      .availabilities-details {
        //position: absolute;
        left: 16em;
        width: calc(100% - 18em);
        @media (max-width: 768px) {
          display: flex;
          flex-direction: column;
          width: inherit;
        }
        padding-top: 0.3em;
        padding-right: 1rem;
        padding-left: 1rem;

        height: 100%;
        text-align: left;

        button.show-more-people-stripe__button.ng-star-inserted {
          position: absolute;
        }
      }

      .full-availbility {
        width: unset;
        @media (max-width: 768px) {
          margin-left: 1%;
        }
      }

      input[type='checkbox'] {
        display: none;
        visibility: hidden;

        &.-slide {
          + label {
            position: relative;
            padding: 0;
            width: max-content;
            font-weight: 300;
            line-height: 25.6px;
            font-family: $ff-base;
            text-align: right;

            &:before {
              display: block;
              position: absolute;
              right: 1.8em;
              transition: right ease-in 250ms;
              box-shadow: 0 0 4.8px 0.3px rgba(0, 0, 0, 0.2);
              border: 0.1rem solid $c-gray-lighter;
              border-radius: 1.2em;
              background-color: #fefefe;
              width: 1.2em;
              height: 1.2em;
              content: '';
            }

            &:after {
              display: inline-block;
              transition: background-color ease-in 250ms;
              margin: 0.1em 0.6em;
              border-radius: 0.8em;
              background-color: $c-gray-lightest;
              width: 2em;
              height: 0.8em;
              content: '';
              @media (max-width: 400px) {
                @media (inverted-colors: inverted) {
                  background-color: #ffffff;
                }
              }
            }
          }

          &:checked {
            + label {
              &:before {
                right: 0;
              }

              &:after {
                background-color: #34922d;
              }
            }
          }
        }
      }

      label {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0 2.8rem;
        @media (max-width: 768px) {
          padding: 0 0.8rem;
        }

        /*> div {
          width: 40px;
          @media (max-width: 768px) {
            width: 20px;
          }
        }*/
      }

      input {
        display: none;

        + label,
        .label {
          &:before {
            display: none;
          }

          &:hover {
            &:after {
              display: none;
            }
          }
        }
      }

      input:checked:not(.-slide) + label,
      input:checked:not(.-slide) + .label {
        position: relative;
        background-color: #f8f9ff;
        padding-left: 2.8rem;
        //justify-content: space-between;

        &:before {
          display: block;
          position: absolute;
          top: 0;
          bottom: 0;
          left: 1.4rem;
          border: none;
          background: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAyMS4xLjAsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjxzdmcgdmVyc2lvbj0iMS4xIiBpZD0iTGF5ZXJfMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeD0iMHB4IiB5PSIwcHgiDQoJIHdpZHRoPSI1MHB4IiBoZWlnaHQ9IjUwcHgiIHZpZXdCb3g9IjAgMCA1MCA1MCIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNTAgNTA7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4NCjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+DQoJLnN0MHtmaWxsOiMzNDkyMmQ7fQ0KPC9zdHlsZT4NCjxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik00OC43LDEwLjdMNDguNywxMC43QzQ3LDksNDQuMiw5LDQyLjUsMTAuN0wxOS44LDMzLjNsLTEyLTEyYy0xLjctMS43LTQuNS0xLjctNi4zLDBzLTEuNyw0LjUsMCw2LjNsMTUsMTUNCgljMC45LDAuOSwyLjEsMS4zLDMuMiwxLjNjMS4yLDAsMi40LTAuNCwzLjItMS4zTDQ4LjcsMTdDNTAuNCwxNS4yLDUwLjQsMTIuNCw0OC43LDEwLjd6Ii8+DQo8L3N2Zz4NCg==')
            no-repeat 50% 50%;
          background-size: 1rem;
          width: 1rem;
          height: auto;
          content: '';
        }

        &:after {
          display: none;
        }
      }

      .people {
        display: block;
        flex-grow: 1;
        padding: 0 1rem;
        max-width: 12em;
        font-size: 1.4rem;
        line-height: 2rem;
      }

      .people__firstname,
      .people__lastname {
        display: inline;
      }

      .people__phone {
        display: block;
      }

      .status {
        padding: 1rem;
      }

      .rating {
        position: absolute;
        top: 0;
        right: 0;

        padding: 1rem;

        svg {
          width: 1.3rem;
          height: 1.3rem;

          &.icon-star-full {
            fill: $c-primary;
          }
        }
      }

      .-job {
        fill: $c-primary;
        width: 1.8rem;
        min-width: 1.8rem;
        height: 1.8rem;
      }
    }

    .form-actions {
      margin-top: 2rem;
      text-align: center;

      .btn {
        outline: none;
        border-radius: 0.2rem;
        min-width: 15rem;
        font-size: 1.2rem;
        line-height: 1;
      }
    }
  }

  &__no-data {
    border-top: 5rem solid $c-primary;
    padding-top: 8rem;
    padding-bottom: 8rem;
    text-align: center;
  }

  .tabs &__day-content {
    &,
    .tabs__content {
      padding: math.div($grid-gutter-width, 2);
    }
  }

  @include media-breakpoint-up(lg) {
    .tabs &__day-content {
      &,
      .tabs__content {
        padding: 0;
        //display: grid;
      }

      .select2 {
        display: none;
      }
    }

    .tabs &__day-content {
      .tabs__content {
        [aria-hidden='true'] {
          display: block;
        }

        .planner__people-list.-placeholder {
          .planner__people {
            &:before {
              display: none;
            }
          }
        }

        > [role='tabpanel'] {
          padding: 0;

          .planner__people {
            &:before {
              display: block;
              background: $c-gray-lightest;
              height: 0.1rem;
            }

            &.-optional {
              &:before {
                border-bottom: 0.1rem dashed #e4edf7;
                background: transparent;
                height: 0;
              }
            }
          }
        }
      }
    }
  }
}

link_audit {
  margin-left: 1.5rem;
  font-family: 'Archivo Narrow', Georgia, 'Times New Roman', Times, serif;
}

.manual-els {
  .btn {
    border: 0.1rem solid $c-primary-red;
    background-color: $c-primary-red;
    padding: 0.5rem;
  }
}

.service-details {
  &__title {
    margin-bottom: 1rem;
    font-size: 1.8rem;
  }

  &__address {
    position: relative;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    font-weight: 300;
    font-size: 1.3rem;

    &:after {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00f6f6f6', endColorstr='#00f6f6f6', GradientType=.1); /* IE6-9 */
      /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#f6f6f6+0,f6f6f6+100&0+0,1+10,1+90,0+100 */
      background: -moz-linear-gradient(
        left,
        rgba(246, 246, 246, 0) 0%,
        rgba(246, 246, 246, 1) 10%,
        rgba(246, 246, 246, 1) 90%,
        rgba(246, 246, 246, 0) 100%
      ); /* FF3.6-15 */
      background: -webkit-linear-gradient(
        left,
        rgba(246, 246, 246, 0) 0%,
        rgba(246, 246, 246, 1) 10%,
        rgba(246, 246, 246, 1) 90%,
        rgba(246, 246, 246, 0) 100%
      ); /* Chrome10-25,Safari5.1-6 */
      background: linear-gradient(
        to right,
        rgba(246, 246, 246, 0) 0%,
        rgba(246, 246, 246, 1) 10%,
        rgba(246, 246, 246, 1) 90%,
        rgba(246, 246, 246, 0) 100%
      ); /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
      height: 0.1rem;
      content: '';
    }
  }

  &__details {
    font-size: 1.3rem;

    > dt {
      font-weight: 500;
    }

    dd {
      font-weight: 300;
    }
  }
}

.popup-filter {
  padding: 0;
}

.show-more-people-stripe {
  margin: 0 auto;
  padding: math.div($grid-gutter-width, 2);
  height: 50px;
  text-align: center;

  &__button {
    position: absolute;
    top: 0;
    @extend %unstyled-button;
    vertical-align: middle;
    //height: 1.0rem !important;
    width: 1rem !important;
    color: $c-primary;
  }

  @include media-breakpoint-down(lg) {
    display: none;
  }
}

/**
  List of available/unavailable persons
 */
cgdis-portal-service-plan-add-people-selector {
  ul {
    max-height: 20rem;
    overflow-x: hidden;
    overflow-y: auto;
  }

  ul li:nth-child(2n) {
    background-color: $c-gray-mid-clear;
  }
}

cgdis-portal-service-plan-copy-popup {
  ul {
    max-height: 25vh;
    overflow-x: hidden;
    overflow-y: auto;
  }

  ul li:nth-child(2n) {
    background-color: $c-gray-mid-clear;
  }

  .people-selector {
    position: relative;

    &.-selection-disable {
      label {
        cursor: inherit;
      }
    }

    &:hover {
      &:not(.-selection-disable) {
        background-color: #f8f9ff;
      }
    }

    .availabilities-person {
      width: 140px;
    }

    .availabilities-entity {
      position: absolute;
      right: 0;
      left: 0;
      padding-top: 0.3em;
      width: 100%;
      @media (max-width: 768px) {
        display: flex;
        width: 26%;
      }
      padding-right: 1rem;
      padding-left: 1rem;
      height: 100%;
      text-align: left;

      button.show-more-people-stripe__button.ng-star-inserted {
        position: absolute;
        top: -0.5em;
      }
    }

    .availabilities-prestations {
      position: absolute;
      right: 0;
      left: 0;
      padding-top: 0.3em;
      width: 100%;
      @media (max-width: 768px) {
        display: flex;
        width: 26%;
      }
      padding-right: 1rem;
      padding-left: 1rem;
      height: 100%;
      text-align: left;

      button.show-more-people-stripe__button.ng-star-inserted {
        position: absolute;
        top: -0.5em;
      }
    }

    .availabilities-interventions {
      display: flex;

      //width: 65px;
      text-align: center;
    }

    .availabilities-interventions-prestations {
      width: 150px;
      text-align: center;
    }

    label {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0 2.8rem;
      @media (max-width: 768px) {
        padding: 0 0.8rem;
      }

      > div {
        width: 25px;
      }
    }

    input {
      display: none;

      + label,
      .label {
        &:before {
          display: none;
        }

        &:hover {
          &:after {
            display: none;
          }
        }
      }
    }

    input:checked + label,
    input:checked + .label {
      position: relative;
      background-color: #f8f9ff;
      padding-left: 2.8rem;

      &:before {
        display: block;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 1.4rem;
        border: none;
        background: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAyMS4xLjAsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjxzdmcgdmVyc2lvbj0iMS4xIiBpZD0iTGF5ZXJfMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeD0iMHB4IiB5PSIwcHgiDQoJIHdpZHRoPSI1MHB4IiBoZWlnaHQ9IjUwcHgiIHZpZXdCb3g9IjAgMCA1MCA1MCIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNTAgNTA7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4NCjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+DQoJLnN0MHtmaWxsOiMzNDkyMmQ7fQ0KPC9zdHlsZT4NCjxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik00OC43LDEwLjdMNDguNywxMC43QzQ3LDksNDQuMiw5LDQyLjUsMTAuN0wxOS44LDMzLjNsLTEyLTEyYy0xLjctMS43LTQuNS0xLjctNi4zLDBzLTEuNyw0LjUsMCw2LjNsMTUsMTUNCgljMC45LDAuOSwyLjEsMS4zLDMuMiwxLjNjMS4yLDAsMi40LTAuNCwzLjItMS4zTDQ4LjcsMTdDNTAuNCwxNS4yLDUwLjQsMTIuNCw0OC43LDEwLjd6Ii8+DQo8L3N2Zz4NCg==')
          no-repeat 50% 50%;
        background-size: 1rem;
        width: 1rem;
        height: auto;
        content: '';
      }

      &:after {
        display: none;
      }
    }

    .people {
      display: block;
      flex-grow: 1;
      padding: 0 1rem;
      max-width: 12em;
      font-size: 1.4rem;
      line-height: 2rem;
    }

    .people__firstname,
    .people__lastname {
      display: inline;
    }

    .people__phone {
      display: block;
    }

    .status {
      padding: 1rem;
    }

    .rating {
      position: absolute;
      top: 0;
      right: 0;

      padding: 1rem;

      svg {
        width: 1.3rem;
        height: 1.3rem;

        &.icon-star-full {
          fill: $c-primary;
        }
      }
    }

    .-job {
      fill: $c-primary;
      width: 1.8rem;
      height: 1.8rem;
    }
  }

  .service-planner__schedule-selector {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 50%;
  }

  .copy-error-message {
    padding-bottom: 1.5em;
    max-width: 700px;
    text-align: justify;
  }
}

/**
Date selector full size in dashboard member
 */
cgdis-portal-dashboard-member {
  cgdis-portal-tabs-list.day-selector.container-fluid {
    display: unset;
    position: unset;
  }
}

/**
Date selector full size in availability planning
Warning: padding for size time slots
 */
cgdis-portal-availability-planning-detail {
  cgdis-portal-tabs-list.day-selector.container-fluid {
    display: unset;
    position: unset;

    .day-selector__title {
      padding-left: 37px;
    }
  }

  .fc-content {
    .fc-time {
      display: none;
    }
  }

  .remove-availability {
    display: flow-root;
    padding-top: 0.5rem;

    cgdis-portal-link-with-icon-delete {
      float: right;

      a.btn.-rounded.-primary.-small {
        line-height: 1.7rem;
      }
    }
  }
}

.remove-availability {
  display: none;
}

/**
Hide style for 'active' day in datepicker
 */
.hide-active {
  background-color: transparent !important;
  color: black !important;
  text-shadow: unset !important;
}

.hide-active:hover {
  color: white !important;
}

.ng-scrollbar.-horizontal {
  margin: unset;
}

.position-scrollbar {
  max-height: 5rem;

  li.planner__people.ng-star-inserted {
    border-bottom: none;
  }
}

.hide-scroll {
  .scrollable-content {
    padding-top: 0px;

    .override-scrollbar-width {
      .ng-scrollbar-view {
        width: 100% !important;
      }
    }

    @for $i from 1 through 24 {
      $minHeight: 0; // For each slots
      @if $i<6 {
        $minHeight: calc(#{$i} * 6rem);
      } @else {
        $minHeight: calc(5 * 6rem);
      }
      &.schedule-min-scroll-height-row-#{$i} {
        .ng-scrollbar-view {
          min-height: $minHeight;
        }
      }
    }

    .ng-scrollbar-view {
      //max-height: 51vh;
      @media (min-height: 900px) {
        max-height: calc(100vh - 600px);
      }

      padding-right: 0;
      overflow: hidden;
    }
  }
}

.scrollable-content {
  padding-top: 1px;

  .override-scrollbar-width {
    .ng-scrollbar-view {
      width: 100% !important;
    }
  }

  @for $i from 1 through 24 {
    $minHeight: 0; // For each slots
    @if $i<6 {
      $minHeight: calc(#{$i} * 6rem);
    } @else {
      $minHeight: calc(5 * 6rem);
    }
    &.schedule-min-scroll-height-row-#{$i} {
      .ng-scrollbar-view {
        min-height: $minHeight;
      }
    }
  }

  .ng-scrollbar-view {
    //max-height: 51vh;
    @media (min-height: 900px) {
      max-height: calc(100vh - 600px);
    }

    padding-right: 0;
    overflow-x: hidden;
    overflow-y: auto;
  }
}





.small-scrollable-content {
  padding-top: 1px;

  .ng-scrollbar-view {
    padding-right: 0;
    max-height: 30rem !important;
    overflow-x: hidden;
    overflow-y: auto;
  }
}

.hiddenScrollBar {
  .ng-scrollbar-view {
    overflow-x: scroll !important;
  }
}

cgdis-portal-service-plan-details {
  .section {
    padding-bottom: 0;
  }

  li.planner__people.ng-star-inserted {
    border-bottom: 1px solid #e9eef5;
  }

  div#schedule--content:last-of-type {
    li.planner__people {
      border: none;
    }
  }
}

cgdis-portal-service-plan-schedule-result-positions {
  .position-scrollbar {
    background: $c-primary;
  }
}

cgdis-portal-service-plan-schedule-result {
  .ng-scrollbar-view::-webkit-scrollbar {
    display: none !important;
  }
}

.slots-scrollbar {
  &.hiddenMobileScrollBar {
    .ng-scrollbar-view {
      overflow-y: hidden !important;
    }
  }
}

.person-unavailable {
  color: $c-gray-disable;
}

.person-multiple {
  color: $c-primary-red;
}

.barracked_filter {
  /*TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version.*/
  .mat-mdc-tab-body-wrapper {
    height: 0;
  }

  .mat-mdc-tab-header {
    padding: 0 !important;
  }
  /*TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version.*/
  .mat-mdc-tab {
    border-color: #2d3c7d !important;
    background-color: #2d3c7d !important;
    min-width: unset;
  }

  [role='tablist'] {
    border: #2d3c7d 1px;
    background-color: white !important;
    padding: 0;
  }

  [aria-selected='true'] {
    border: #2d3c7d 1px solid;
    background-color: white !important;
    color: #2d3c7d !important;
  }
  /*TODO(mdc-migration): The following rule targets internal classes of tabs that may no longer apply for the MDC version.*/
  .mat-tab-header-pagination-chevron {
    border-color: white;
  }
}

.owl-popup {
  position: relative;
  font-size: 20px;
  .owl-carousel {
    padding: 2rem 0;

    .owl-item {
      border: none;
      color: $c-primary !important;
      font-size: 18px;
      > div {
        text-align: center;
        //margin: 0 6rem;
      }
    }

    .owl-nav {
      //position: absolute;
      //left: 0;
      //right: 0;
      //top: 0;
      //bottom: 0;
      //margin: 0 1rem;
      //z-index: 10;

      [class*='owl-'] {
        display: flex;
        //background-color: #fff !important;
        position: absolute;
        top: 0;
        bottom: 0;
        justify-content: center;
        align-items: center;
        z-index: 9;
        transition: background-color 250ms ease-in;
        margin: 0;
        border-radius: 0.4rem;
        background-color: transparent !important;
        padding: 0;
        width: 4rem;
        color: $c-primary !important;
        font-size: 20px;

        &.disabled {
          display: none;
        }

        svg {
          width: 1.6rem;
          height: 1.6rem;
        }
      }

      .owl-prev {
        top: -13px;
        left: 0;
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }

      .owl-next {
        top: -13px;
        right: 0;
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }
  }
}


.backupModePlan {
  margin-bottom: 1rem;
  border: 1px solid black;
  padding: 5px;
}
