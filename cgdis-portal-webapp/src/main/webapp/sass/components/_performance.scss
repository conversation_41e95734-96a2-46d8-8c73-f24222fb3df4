cgdis-portal-general-information {
  .mat-accordion {
    .mat-expansion-panel-content {
      .performance {
        line-height: 1.6;
      }
    }
  }
}

.performance {
  display: flex;
  flex-direction: column;

  text-align: center;

  &__left {
    //float: left;
    padding-left: 10%;

    @media (max-width: 450px) {
      padding-left: 0 !important;
    }
  }

  &__first {
    float: left;
    padding-left: 0 !important;
  }

  &__right {
    float: right;
    padding-right: 10%;

    @media (max-width: 450px) {
      padding-right: 0 !important;
    }
  }

  &__figure {
    vertical-align: top;
    color: $c-secondary-lighter;
    font-size: 2.8rem;

    //text-align: left;
    font-family: $ff-heading;
    &__title {
      display: block;
      height: 2em;
      color: $c-primary-red;
      font-size: 1.5rem;
    }
    &__content {
      display: inline-block;
      position: relative;
      vertical-align: top;
      float: left;
      width: 50%;

    }

    &_content-multiple-items {
      display: flex;
      flex-direction: row;
    }
  }

  &__legend {
    color: $c-gray-light;
    font-weight: 300;
    font-size: 1.4rem;
    text-align: left;
  }

  ul {
    padding: 0;
    list-style-type: none;
    //margin: -2rem 0 0;
  }
}

/*.panel.performances-calendar {
    margin-top: -2rem;

  @media (max-width: 1400px) {
    margin-top: 50px !important;
  }

}*/

.summary-mobile {
  padding-bottom: 2rem;

  .statistics-value {
    font-size: 1.8rem;
  }
}

@include media-breakpoint-down(md) {
  .summary-mobile-columns {
    .page-content {
      padding-right: 0;
      padding-left: 0;

      .summary-mobile-performances {
        > div {
          padding: 0;
          &:nth-child(even) {
            .performance__figure___title {
              padding-left: 1rem;
            }
          }

          &:nth-child(odd) {
            .performance__figure_content {
              padding-right: 1rem;
            }
          }

          .performance__left {
            float: none;

            .performance__figure {
              display: flex;
              line-height: 24px;

              .performance__figure__title {
                //display: inline-block;
                //width: 30%;
                width: 5rem;
                line-height: 24px;
                text-align: left;
              }

              .performance__figure_content {
                flex-grow: 1;
                padding-right: 1rem;
                line-height: inherit;
                line-height: 24px;
                text-align: right;

                ul {
                  margin-bottom: 0;
                  line-height: 24px;

                  li {
                    margin-top: 0;
                    line-height: 24px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
