@use 'sass:math';

$inputFontWeight: 300;

// Readonly style
[type='date'],
[type='time'],
[type='datetime'],
[type='text'],
[type='password'],
[type='email'],
[type='search'],
[type='tel'],
[type='number'] {
  &[readonly] {
    border: none;
  }
}

[type='date'],
[type='time'],
[type='datetime'],
[type='text'],
[type='password'],
[type='email'],
[type='search'],
[type='tel'],
[type='number'],
textarea,
select,
[role='radio'] {
  padding: $input-padding;
  line-height: $input-line-height;
  @media (max-width: 768px) {
    line-height: $mobile-input-line-height;
  }
}

ngx-select {
  line-height: $input-line-height;
}

[role='radio'] {
  padding-bottom: #{($input-padding + $input-border-bottom-size)};
}

[type='date'],
[type='time'],
[type='datetime'],
[type='text'],
[type='password'],
[type='email'],
[type='search'],
[type='tel'],
[type='number'],
textarea,
select,
ngx-select {
  border: $input-border;
  border: 0;
  border-bottom: $input-border-bottom-size solid rgba(#757f8d, 0.2);
  background-color: $input-bg;
  font-style: normal;
  font-weight: $inputFontWeight;
  font-stretch: normal;
  //color: $c-secondary-lighter;
  font-family: $ff-base;

  .form-item & {
    &:not(.-inline) {
      width: 100%;
    }
  }

  &.error {
    border-color: map-get(map-get($message-types, 'error'), 'border');
    color: map-get(map-get($message-types, 'error'), 'color');
  }

  .form-item-error & {
    border-color: map-get(map-get($message-types, 'error'), 'border');
    color: map-get(map-get($message-types, 'error'), 'color');
  }
}

.cron-field-active-view-minutely,
.cron-field-active-view-hourly,
.cron-field-active-view-weekly {
  &.error {
    border-color: map-get(map-get($message-types, 'error'), 'border');
    color: map-get(map-get($message-types, 'error'), 'color');
  }

  .form-item-error cron-editor .cron-editor-tab-content {
    > div:first-child {
      border-color: map-get(map-get($message-types, 'error'), 'border');
      color: map-get(map-get($message-types, 'error'), 'color');

      .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading, .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch, .mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing {
        border-color: map-get(map-get($message-types, 'error'), 'border');
      }

      label {
        color: map-get(map-get($message-types, 'error'), 'color');
      }
    }
  }
}



.form-item {
  .ngx-select__toggle.btn {
    font-weight: $inputFontWeight;
  }

  select {
    &.-inline {
      padding-top: 0;
    }
  }
}

select {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAyMS4wLjIsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjxzdmcgdmVyc2lvbj0iMS4xIiBpZD0iTGF5ZXJfMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeD0iMHB4IiB5PSIwcHgiDQoJIHZpZXdCb3g9IjAgMCAxNiA5LjEiIHN0eWxlPSJlbmFibGUtYmFja2dyb3VuZDpuZXcgMCAwIDE2IDkuMTsiIHhtbDpzcGFjZT0icHJlc2VydmUiPg0KPHN0eWxlIHR5cGU9InRleHQvY3NzIj4NCgkuc3Qwe2ZpbGw6Izk5OTk5OTt9DQo8L3N0eWxlPg0KPGc+DQoJPHBhdGggY2xhc3M9InN0MCIgZD0iTTgsOS4xQzcuNyw5LjEsNy40LDksNy4yLDguOEwwLDEuNkwxLjYsMEw4LDYuNUwxNC40LDBMMTYsMS42TDguOCw4LjhDOC42LDksOC4zLDkuMSw4LDkuMXoiLz4NCjwvZz4NCjwvc3ZnPg0K);
  background-image: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgdmVyc2lvbj0iMS4xIgogICB2aWV3Qm94PSIwIDAgNTEyIDUxMiIKICAgaWQ9Imljb24tY2hldnJvbiI+CiAgPG1ldGFkYXRhCiAgICAgaWQ9Im1ldGFkYXRhNDE4NyI+CiAgICA8cmRmOlJERj4KICAgICAgPGNjOldvcmsKICAgICAgICAgcmRmOmFib3V0PSIiPgogICAgICAgIDxkYzpmb3JtYXQ+aW1hZ2Uvc3ZnK3htbDwvZGM6Zm9ybWF0PgogICAgICAgIDxkYzp0eXBlCiAgICAgICAgICAgcmRmOnJlc291cmNlPSJodHRwOi8vcHVybC5vcmcvZGMvZGNtaXR5cGUvU3RpbGxJbWFnZSIgLz4KICAgICAgICA8ZGM6dGl0bGU+PC9kYzp0aXRsZT4KICAgICAgPC9jYzpXb3JrPgogICAgPC9yZGY6UkRGPgogIDwvbWV0YWRhdGE+CiAgPGRlZnMKICAgICBpZD0iZGVmczQxODUiIC8+CiAgPHBhdGgKICAgICBzdHlsZT0iZmlsbDojYzEwMDJlIgogICAgIGQ9Im0gMjU1Ljk0MjcyLDI5OC4wNTcyOCAtMTc0LC0xNjcgYyAtNCwtNCAtMTEsLTQgLTE2LDAgbCAtMzAsMzAgYyAtNSw0IC01LDExIC0xLDE2IGwgMjEzLDIwNCBjIDIsMiA1LDMgOCwzIDMsMCA2LC0xIDgsLTMgbCAyMTMsLTIwNCBjIDQsLTUgNCwtMTIgMCwtMTYgbCAtMzEsLTMwIGMgLTQsLTQgLTExLC00IC0xNiwwIHoiCiAgICAgaWQ9InBhdGg0MTgxIiAvPgo8L3N2Zz4K');
  background-position: right 1rem top 50%;
  background-size: 1.4rem;
  background-repeat: no-repeat;
  padding-right: 3.5rem;
}

.readOnlyCheckbox {
  pointer-events: none !important;
}

input[type='checkbox'].-slide:checked + .readOnlyCheckbox:after {
  background-color: $c-gray;
}

input[type='checkbox']:not(.-slide):checked + .readOnlyCheckbox:after {
  color: $c-gray;
}

input[type='checkbox'] {
  display: none;
  visibility: hidden;

  &:not(.-slide) {
    + label {
      display: flex;
      position: relative;
      align-items: center;
      padding: 0;
      font-weight: 300;
      font-family: $ff-base;

      &:before {
        display: inline-block;
        vertical-align: baseline;
        cursor: pointer;
        margin-right: 0.2em;
        border: 0.1rem solid $c-gray-lighter;
        border-radius: 0.4rem;
        background: #fff;
        width: 1em;
        height: 1em;
        content: '';
      }

      &:after {
        position: absolute;
        left: 0;
        opacity: 0;
        border: 0.1rem solid transparent;
        background: transparent;
        width: 1em;
        height: 1em;
        content: '✓';
        color: #34922d;
        font-size: 1em;
        line-height: 1.2;
        text-align: center;

        &.disabled {
          color: #ff0000;
        }
      }

      &:hover::after {
        opacity: 0;
      }
    }

    &:checked {
      + label {
        &::after {
          opacity: 1;
          top: 0;
        }
      }
    }

    // Handle disabled styles
    &.disabled + label,
    + label.disabled {
      cursor: default;
      opacity: 0.5;

      &::after {
        color: #adadad;
      }

      &::before {
        border-color: #ccc;
        background: #f5f5f5;
      }
    }
  }

  &.-slide {
    + label {
      position: relative;
      /*width: 100%;
          display: flex;
          justify-content: space-between;*/
      align-items: center;
      padding: 0;
      font-weight: 300;
      font-family: $ff-base;
      text-align: right;
      @media (min-width: 400px) {
        display: inline-flex;
      }

      @media (max-width: 400px) {
        display: flex;
        justify-content: space-between;
        width: 100%;
        text-align: left;
      }

      &:before {
        display: block;
        position: absolute;
        right: 1.8em;
        transition: right ease-in 250ms;
        box-shadow: 0 0 4.8px 0.3px rgba(0, 0, 0, 0.2);
        border: 0.1rem solid $c-gray-lighter;
        border-radius: 1.2em;
        background-color: #fefefe;
        width: 1.2em;
        height: 1.2em;
        content: '';
      }

      &:after {
        display: inline-block;
        -webkit-transition: background-color ease-in 250ms;
        transition: background-color ease-in 250ms;
        margin: 0 0.7em;
        border-radius: 0.8em;
        background-color: #e9eef5;
        width: 2em;
        height: 0.8em;
        content: '';
        @media (max-width: 400px) {
          @media (inverted-colors: inverted) {
            background-color: #ffffff;
          }
        }
        /*bottom: 4px;
              margin-bottom: -2.1px;*/
      }
    }

    &:checked {
      + label {
        &:before {
          right: 0;
        }

        &:after {
          background-color: #34922d;
        }
      }
    }
  }
}

input[type='radio'] {
  display: none;

  + label {
    display: inline-block;
    position: relative;
    padding: 0 0 0 1.2em;
    line-height: 1;

    &:before {
      display: inline-block;
      position: absolute;
      top: 0;
      left: 0;
      cursor: pointer;
      margin-right: 0.2em;
      border: 0.1rem solid $c-gray-lighter;
      border-radius: 50%;
      background: #fff;
      width: 1.4rem;
      height: 1.4rem;
      content: '';
    }

    &:after {
      position: absolute;
      top: 0;
      left: 0;
      transform: scale(0.6);
      opacity: 0;
      border: 0.1rem solid transparent;
      border-radius: 50%;
      background: $c-primary;
      width: 1.4rem;
      height: 1.4rem;
      content: '';
    }

    &:hover::after {
      opacity: 0.5;
    }
  }

  &:checked {
    + label {
      &::after {
        opacity: 1;
      }
    }
  }
}

input[type='time'] {
  background-image: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAyMS4xLjAsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjxzdmcgdmVyc2lvbj0iMS4xIiBpZD0iTGF5ZXJfMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeD0iMHB4IiB5PSIwcHgiDQoJIHdpZHRoPSI1MHB4IiBoZWlnaHQ9IjUwcHgiIHZpZXdCb3g9IjAgMCA1MCA1MCIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNTAgNTA7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4NCjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+DQoJLnN0MHtmaWxsOiNDMTAwMkU7fQ0KPC9zdHlsZT4NCjxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik0yNSwwLjRDMTEuNCwwLjQsMC40LDExLjQsMC40LDI1czExLDI0LjYsMjQuNiwyNC42czI0LjYtMTEsMjQuNi0yNC42bDAsMEM0OS42LDExLjQsMzguNiwwLjQsMjUsMC40eg0KCSBNMjYuOCw0Ni4xdi00LjNoLTMuNXY0LjNDMTMsNDUuMiw0LjgsMzcsMy45LDI2LjhoNC4zdi0zLjVIMy45QzQuOCwxMywxMyw0LjgsMjMuMiwzLjl2NC4zaDMuNVYzLjlDMzcsNC44LDQ1LjIsMTMsNDYuMSwyMy4yaC00LjMNCgl2My41aDQuM0M0NS4yLDM3LDM3LDQ1LjIsMjYuOCw0Ni4xeiIvPg0KPHBvbHlnb24gY2xhc3M9InN0MCIgcG9pbnRzPSIyNSwyNC43IDE4LjIsMTIuNiAxNS4yLDE0LjMgMjIuOSwyOC4yIDM1LjYsMjguMSAzNS42LDI0LjYgIi8+DQo8L3N2Zz4NCg==');
  background-position: right 1rem top 50%;
  background-size: 1.4rem;
  background-repeat: no-repeat;
  padding-right: 3.5rem;
}

input.-with-datepicker,
input[type='date'] {
  background-image: url('data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAyMS4xLjAsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjxzdmcgdmVyc2lvbj0iMS4xIiBpZD0iTGF5ZXJfMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeD0iMHB4IiB5PSIwcHgiDQoJIHdpZHRoPSI1MHB4IiBoZWlnaHQ9IjUwcHgiIHZpZXdCb3g9IjAgMCA1MCA1MCIgc3R5bGU9ImVuYWJsZS1iYWNrZ3JvdW5kOm5ldyAwIDAgNTAgNTA7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4NCjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+DQoJLnN0MHtmaWxsOiNDMTAwMkU7fQ0KPC9zdHlsZT4NCjxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik00NS44LDUuN2gtNS45Yy0wLjcsMC0xLjMsMC42LTEuMywxLjNjMCwwLjcsMC42LDEuMywxLjMsMS4zaDUuOWMwLjcsMCwxLjMsMC42LDEuMywxLjN2OC4xSDIuOFY5LjYNCgljMC0wLjcsMC42LTEuMywxLjMtMS4zaDYuOGMwLjcsMCwxLjMtMC42LDEuMy0xLjNjMC0wLjctMC42LTEuMy0xLjMtMS4zSDQuMUMyLDUuNywwLjMsNy40LDAuMyw5LjZ2MzVjMCwyLjEsMS43LDMuOSwzLjksMy45aDQxLjcNCgljMi4xLDAsMy45LTEuNywzLjktMy45di0zNUM0OS43LDcuNCw0OCw1LjcsNDUuOCw1Ljd6IE00Ny4xLDQ0LjZjMCwwLjctMC42LDEuMy0xLjMsMS4zSDQuMWMtMC43LDAtMS4zLTAuNi0xLjMtMS4zVjIwLjJoNDQuMw0KCUw0Ny4xLDQ0LjZMNDcuMSw0NC42eiBNMTkuNyw4LjNoMTAuM2MwLjcsMCwxLjMtMC42LDEuMy0xLjNjMC0wLjctMC42LTEuMy0xLjMtMS4zSDE5LjdjLTAuNywwLTEuMywwLjYtMS4zLDEuMw0KCUMxOC40LDcuNywxOSw4LjMsMTkuNyw4LjN6IE0xNS4xLDE1LjJjMS41LDAsMi43LTEuMiwyLjctMi43YzAtMS0wLjYtMS45LTEuNC0yLjNWMi41YzAtMC43LTAuNi0xLjMtMS4zLTEuMw0KCWMtMC43LDAtMS4zLDAuNi0xLjMsMS4zdjcuN2MtMC44LDAuNS0xLjQsMS4zLTEuNCwyLjNDMTIuNCwxNCwxMy42LDE1LjIsMTUuMSwxNS4yeiBNMzQuOSwxNS4yYzEuNSwwLDIuNy0xLjIsMi43LTIuNw0KCWMwLTEtMC42LTEuOS0xLjQtMi4zVjIuNWMwLTAuNy0wLjYtMS4zLTEuMy0xLjNjLTAuNywwLTEuMywwLjYtMS4zLDEuM3Y3LjdjLTAuOCwwLjUtMS40LDEuMy0xLjQsMi4zQzMyLjIsMTQsMzMuNCwxNS4yLDM0LjksMTUuMnoNCgkiLz4NCjwvc3ZnPg0K');
  background-position: right 1rem top 50%;
  background-size: 1.6rem;
  background-repeat: no-repeat;
  padding-right: 3.5rem !important;
  //padding-bottom: 0 !important;
  //height: 2.3rem !important;
  @media (max-width: 768px) {
    //background-size: 1.0rem;
    padding-right: 2.5rem;
  }

  &[readonly] {
    background-image: none;
  }
}

label {
  font-weight: 300;
  font-size: 1.4rem;
}

.sameMarginThanFormItem {
  margin-bottom: math.div($grid-gutter-width, 2);
}

cgdis-portal-admin-export-detail {
  .checkboxLabel {
    display: flex !important;
    float: right;
  }

  cgdis-portal-checkbox-field {
    display: contents;

    cgdis-portal-field.ng-star-inserted {
      display: table;
      width: inherit;
    }
  }
}

cgdis-portal-operational-contact-form {
  cgdis-portal-checkbox-field {
    .form-item {
      margin-top: -5px;
      padding: 0.5rem;
    }
  }
}

.form-item {
  //@media (min-width: 768px) {
  margin-bottom: math.div($grid-gutter-width, 2);
  //}

  .form-item {
    margin-bottom: 0;
  }

  [role='radio'] {
    + [role='radio'] {
      margin-right: 0.5em;
    }
  }

  label {
    display: block;
    padding: 0 0.5rem;
    color: $c-secondary-lighter;
    font-weight: 500;
    font-size: inherit;
    font-family: $ff-base;
  }

  &.-inline {
    display: flex;
    align-items: stretch;
    margin-top: 2rem;

    label {
      align-items: center;
      margin-top: 0.5rem;
      margin-bottom: 0;
      padding-left: 1.5rem;
      text-align: start;
    }
  }
}

div {
  &[role='radio'] {
    display: inline-block;
  }
}

.defaultFormTemplate {
  .accordion__trigger {
    &::after {
      display: none;
    }
  }
}

/**
Used for version tables on service plan and service plan model
 */
.overflow-hidden {
  .accordion__panel {
    padding-right: 10px;
    padding-bottom: 4px;
    padding-left: 10px;
    overflow: hidden !important;
    //margin-top: -4rem;
    button.right.margin.-primary.btn.ng-star-inserted {
      margin-right: 0;
    }
  }
}

.form-item {
  &.form-item-error {
    > label {
      color: map-get(map-get($message-types, 'error'), 'color');
    }
  }

  &:not(.form-item-error) {
    .form-item-error-block {
      display: none;
    }
  }
}

.form-label {
  display: block;
  width: 15em;
  color: #3d3b54;
  font-weight: 500;
  font-size: inherit;
  //padding: 0 .5rem;
  font-family:
    'Roboto',
    -apple-system,
    system-ui,
    BlinkMacSystemFont,
    'Segoe UI',
    'Helvetica Neue',
    Arial,
    sans-serif;
}
