/*
* Write here style that will affect more than a couple of pages
*/
@use 'sass:math';
body {
  &:before {
    position: fixed;
    top: 6rem;
    right: 0;
    bottom: 0;
    left: 0;
    opacity: 0;
    z-index: 0;
    transition: opacity 0s ease-in;
    background-color: rgba(0, 0, 0, 0.2);
    content: '';
  }

  &.-with-main-nav {
    overflow-y: hidden;
  }
  @include media-breakpoint-up(sm) {
    &.-with-main-nav {
      &:before {
        opacity: 1;
        z-index: 19;
        transition: opacity 0.2s ease-in;
      }
    }
  }
}

svg {
  max-width: 100%;
}
//padding-top: 0;
//
//padding-bottom: 0;
//
//min-height: calc(100vh - 8rem);
main.main {
  position: relative;
  //overflow-x: hidden;

  @include media-breakpoint-down(sm) {
    padding-top: 6rem;
  }

  @include media-breakpoint-up(sm) {
    padding-top: 6rem;
    padding-bottom: 5rem;
    //min-height: calc(100vh - 12rem);
  }
  /*@include media-breakpoint-up(xl) {
      margin-top: 6rem;
      margin-bottom: 5rem;
      padding-top:0;
      padding-bottom:0;
      //max-height: calc(100vh - 11rem);
    }*/
}

.section {
  display: block;
  margin: 1.5rem auto;
  //padding-bottom: 5rem;
  width: 100%;

  @media (min-width: 768px) {
    padding-top: 1.5rem;
  }

  &.-fill-height {
    height: calc(100% - 3rem);
    // padding-bottom: 3rem !important;
  }
}

.container-fluid {
  max-width: map_get($container-max-widths, xl);
}

@media (min-width: 1200px) and (max-width: 1600px) {
  .container:not(.--no-padding) {
    padding-right: 50px;
    padding-left: 50px;
  }
}

.page-header {
  margin: 0 0 2rem;
  @extend %clearfix;

  &.page-header-flex {
    display: flex;
    @media (min-width: 800px) {
      align-items: center;
    }
    @media (max-width: 800px) {
      flex-direction: column;
    }

    .page-header-subtitle-flex {
      flex-grow: 2;
    }
  }

  &.page-header-subtitle-align {
    .section__title {
      display: inline-block;

      @include media-breakpoint-up(sm) {
        & + * {
          display: inline-flex;
          margin-left: 1em;
        }
      }
    }

    cgdis-portal-button-link {
      &::before {
        display: block;
        content: '';
      }
    }
  }

  .ngx-select__toggle.btn.form-control {
    @media (min-width: 768px) {
      margin-bottom: 0.1rem;
    }
  }
}

.service-list {
  nav {
    padding: 1.5rem 2.5rem;
    a {
      color: inherit;
    }
    > ul {
      @extend %unstyled-list;
      color: $c-secondary-lighter;
      font-weight: 500;
      font-size: 1.4rem;
      line-height: 2.5rem;

      > li {
        > ul {
          @extend %unstyled-list;
          margin: 1rem auto;
          color: $c-gray;
          font-weight: 300;
          font-size: 1.4rem;
          line-height: 2.5rem;
        }
      }
    }
  }
}

.performances-calendar {
  padding-top: 0.5rem;

  .btn-edit {
    position: absolute;
    top: 1rem;
    right: 1rem;
  }

  @include media-breakpoint-up(md) {
    //padding: $grid-gutter-width;
    padding: 5px;
    //padding-top: $grid-gutter-width;
    .btn-edit {
      top: $grid-gutter-width;
      right: $grid-gutter-width;
    }
  }
}

.roulement {
  margin-bottom: 2rem;
  color: $c-secondary-lighter;
  font-style: normal;
  font-weight: normal;
  font-stretch: normal;
  font-size: 2rem;
  line-height: 1.43;
  font-family: $ff-heading;
  text-align: left;

  &__panel {
    margin-top: math.div($grid-gutter-width, 2);
    margin-bottom: math.div($grid-gutter-width, 2);
    padding: math.div($grid-gutter-width, 2);
  }

  &__list {
    @extend %unstyled-list;
    font-size: 1.2rem;

    > li {
      display: flex;
      align-items: center;
      padding-top: 0.5rem;
      padding-bottom: 0.5rem;

      span {
        flex-grow: 1;
      }

      svg {
        width: 1.8rem;
        height: 1.8rem;
      }

      .btn.-link {
        padding: 0.5rem;
      }

      & + li {
        border-top: 0.1rem solid $c-gray-lightest;
      }
    }
  }
}

.roulement-toggle {
  &.btn[aria-expanded='true'] {
    display: none;
  }

  + form[aria-hidden='true'] {
    display: none;
  }
}

cgdis-portal-link-with-icon,
cgdis-portal-link-with-icon-delete,
cgdis-portal-link-with-icon-copy {
  font-size: initial;
  line-height: 0;
}

cgdis-portal-up-or-down-button {
  .dropdown__trigger {
    font-size: initial;
  }
}

.logout {
  position: fixed;
  top: 18px;
  right: 0;
  margin-right: 1em;

  :hover {
    color: $c-gray;
  }
}

.logas-logout {
  position: absolute;
  top: 25%;
  right: 30%;
}

.page-header {
  .btn.right {
    & + .btn.right {
      margin-right: 1rem;
    }
  }
}

.mobile-situation-person {
  margin-top: 4rem !important;
  .page-header {
    @media (max-width: 768px) {
      margin-top: 4rem !important;
    }
  }
}

.planning {
  .btn-duplicate {
    margin: 1rem;
  }
}

.clickable {
  :hover {
    cursor: pointer;
  }
}
cgdis-portal-scroll-table-left {
  ng-scrollbar {
    &.cgdis-scroll__hide-scrollbars {
      .cgdis-scroll__viewport {
        .cgdis-scroll-content-wrapper.ng-scroll-content {
          padding-right: 0;
        }
      }
    }
  }
}
cgdis-portal-scroll-table-top {
  ng-scrollbar {
    &.cgdis-scroll__hide-scrollbars {
      .cgdis-scroll__viewport {
        .cgdis-scroll-content-wrapper.ng-scroll-content {
          padding-bottom: 0;
        }
      }
    }
  }
}
/** Scrollbar style */
ng-scrollbar {
  .ng-scrollbar__box-sizing__content {
    box-sizing: content-box;
  }
  &.cgdis-scroll__hide-scrollbars {
    .cgdis-scroll__viewport {
      .cgdis-scroll-content-wrapper.ng-scroll-content {
        //padding: 0;
      }
    }
    //scrollbars,
    .ng-scrollbar-thumb.scrollbar-thumb {
      display: none;
    }
  }
  .ng-scroll-viewport {
    &.cgdis-scroll__viewport {
      .cgdis-scroll-content-wrapper {
        div[role='tabpanel'] {
          //width: 2000px;
          ul {
            //width: 2000px;
          }
        }
      }
    }
  }
}
.ng-scrollbar.-horizontal {
  margin-right: 2rem;
  margin-bottom: 1rem;
  background: transparent;
  height: 10px;
}
.ng-scrollbar.-vertical {
  width: 10px;
}
.ng-scrollbar-thumb.scrollbar-thumb {
  background: $c-primary;
  &:hover,
  &:focus,
  &:active {
    background: $c-primary;
  }
}
.ng-scrollbar-view.scrollbar-thumb {
  overflow: hidden;
}

/**
Inputs (number)
*/
input[type='number'] {
  -moz-appearance: textfield;
  margin: 0;
}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.arrow-up {
  position: absolute;
  top: 1.4em;
  right: 0;
  border: none;
  background: transparent;
  width: 1.5em;
  height: 1.5em;
  color: $c-primary-red;
  svg {
    width: 1.5em;
    height: 1em;
  }
}
.arrow-down {
  position: absolute;
  top: 2.6em;
  right: 0;
  border: none;
  background: transparent;
  width: 1.5em;
  height: 1.5em;
  color: $c-primary-red;
  svg {
    width: 1.5em;
    height: 1em;
  }
}

a:hover {
  color: unset;
}

.backdrop {
  z-index: 19 !important;
}

.mat-mdc-tooltip {
  font-size: 1.2rem !important;
}

/**
Bootstrap Pagination component (news list)
 */
ngb-pagination {
  .pagination {
    display: block;
  }

  .page-link {
    display: unset;
    position: unset;
    margin-left: unset;
    border: unset;
    background-color: unset;
    padding: unset;
    color: unset;
    line-height: unset;
  }

  li.page-item {
    display: inline-block;
    padding: 0.2rem;

    a {
      display: inline-block;
      transition:
        color 250ms ease-in-out,
        background-color 250ms ease-in-out;
      border: 0.1rem solid $c-primary-red !important;
      border-radius: 0.2rem;
      background-color: #fff !important;
      width: 4rem;
      height: 4rem;
      overflow: hidden;
      color: $c-primary-red !important;
      line-height: 4rem;
      text-align: center;
      white-space: nowrap;
    }

    &.active {
      a {
        background-color: $c-primary-red !important;
        color: #fff !important;
      }
    }
  }
}

/* Fix scroll issue for material dialog, on iOS & safari */
.cdk-global-scrollblock {
  position: initial !important;
  width: initial !important;
  overflow: hidden !important;
}

input:-moz-read-only {
  color: black;
}

input:read-only {
  color: black;
}

input:disabled {
  opacity: 1;
  color: black;
}

