@charset "utf-8";
@import 'ckeditor5/ckeditor5.css';
@import 'nouislider/dist/nouislider.css';
@import 'utilities/text';
@import 'utilities/functions';
@import 'utilities/_variables';
@import 'utilities/_mixins';
@import 'utilities/_helpers';
// Import debug if debug mode is active
@import 'utilities/_debug';
@import 'utilities/_inputs';
@import 'mixins/form';
@import 'mixins/filters';
@import 'mixins/ngx-select';

//@import "utilities/display"; Only active if necessary
@import 'layout/bootstrap-grid';

@import 'base/_reboot';
@import 'base/_reboot-more';
@import 'base/_fonts';
@import 'base/_typography';
@import 'base/_general';

@import 'layout/_header';
@import 'layout/_navigation';
@import 'layout/_footer';
@import 'layout/_form';
@import 'layout/_filter';

@import 'components/swimlane-datatable';

@import 'components/ellipsis';
@import 'components/highlight';
@import 'components/filters';
@import 'components/error-management';
@import 'components/spinner';
@import 'components/admin';
@import 'components/ngxselect';
@import 'components/select2/core';
@import 'components/magnific-popup/main';
@import 'ngx-owl-carousel-o/lib/styles/scss/owl.carousel';
@import 'ngx-owl-carousel-o/lib/styles/scss/owl.theme.default';
@import 'components/owl/theme.default';
@import 'components/cron-editor';
@import 'components/_browsehappy';
@import 'components/_buttons';
@import 'components/_icons';
@import 'components/_form';
@import 'components/_language-switcher';
@import 'components/_pager';
@import 'components/_statuses';
@import 'components/_highcharts';
@import 'components/_legend-box';
@import 'components/_occupancy';
@import 'components/_news-sidebar';
@import 'components/_datepicker';
@import 'components/_tabs';
@import 'components/_panel';
@import 'components/_planner';
@import 'components/_dropdown';
@import 'components/_checkbox';
@import 'components/_progress-bar';
@import 'components/_fullcalendar';
@import 'components/_performance';
@import 'components/_automatic-planning';
@import 'components/_generation-timer';
@import 'components/_service-list';
@import 'components/_add-event';
@import 'components/_plan-travail';
@import 'components/_teammate';
@import 'components/_accordion';
@import 'components/_planning-form';
@import 'components/_planning-template';
@import 'components/_calendar-event-tooltip';
@import 'components/vehicle';
@import 'components/_login';
@import 'components/_simple-table';
@import 'components/simple-popup';
@import 'components/_tile';
@import 'components/_menu';
@import 'components/_service-plan-list';
@import 'components/_dashboard';
@import 'components/_tab';
@import 'components/_nouislider';
@import 'components/_schedule';
@import 'components/_general-information';
@import 'components/_preference-popup';
@import 'components/_datatables';
@import 'components/_user-rights';
@import 'components/_news-list';
@import 'components/_news-view';
@import 'components/angular-material';
@import 'components/general-availability';
@import 'components/button-group-selector';
@import 'components/expansion-panel.component';
@import 'components/people-management-functions';

@import 'sass/components/permamonitor/permamonitor-export';
@import 'sass/components/rici/rici-export';

.pleaseRotateMessage,
.pleaseRotateMessageTablet,
.pleaseRotateMessageMobile {
  display: none;
}

@media (max-width: 1200px) and (min-width: 992px) and (orientation: portrait) {
  .pleaseRotateTablet {
    display: none;
  }
  .pleaseRotateMessageTablet {
    display: block;
  }
}

@media (max-width: 992px) and (orientation: portrait) {
  .pleaseRotateMobile {
    display: none;
  }
  .pleaseRotateMessageMobile {
    display: block;
  }
}

html {
  //--mat-expansion-container-text-font: 'Archivo Narrow', Georgia, "Times New Roman", Times, serif;
  --mat-expansion-container-text-font: 'Roboto', -apple-system, system-ui,
  BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
  --mat-expansion-header-text-weight: 400;
}
