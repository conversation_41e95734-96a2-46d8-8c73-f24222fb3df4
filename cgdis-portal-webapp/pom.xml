<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>cgdis-portal</artifactId>
    <groupId>lu.fujitsu.ts.cgdis</groupId>
    <version>0.66.0.2000</version>
    <relativePath>../</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>cgdis-portal-webapp</artifactId>
  <packaging>jar</packaging>
  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-configuration-processor</artifactId>
      <optional>true</optional>
    </dependency>
    <!-- ePortal -->
    <dependency>
      <groupId>lu.fujitsu.ts.eportal</groupId>
      <artifactId>ePortal-server-mvc</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>springfox-swagger2</artifactId>
          <groupId>io.springfox</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-oauth2-client</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-oauth2-jose</artifactId>
    </dependency>
    <!-- Springboot -->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-actuator</artifactId>
    </dependency>
    <!--<dependency>-->
      <!--<groupId>org.springframework.boot</groupId>-->
      <!--<artifactId>spring-boot-actuator-docs</artifactId>-->
    <!--</dependency>-->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-security</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-test</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-aop</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-properties-migrator</artifactId>
      <scope>runtime</scope>
    </dependency>
    <!--<dependency>-->
    <!--<groupId>org.springframework.boot</groupId>-->
    <!--<artifactId>spring-boot-starter-security</artifactId>-->
    <!--</dependency>-->
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-logging</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-webmvc</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-tomcat</artifactId>
      <!--
                  <scope>provided</scope>
      -->
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>


    <!-- Cgdis Portal -->

    <dependency>
      <groupId>lu.fujitsu.ts.cgdis</groupId>
      <artifactId>cgdis-portal-export</artifactId>
    </dependency>


    <dependency>
      <groupId>lu.fujitsu.ts.cgdis</groupId>
      <artifactId>cgdis-portal-business</artifactId>
    </dependency>

    <dependency>
      <groupId>lu.fujitsu.ts.cgdis</groupId>
      <artifactId>cgdis-portal-ad-connector</artifactId>
    </dependency>
    <dependency>
      <groupId>lu.fujitsu.ts.cgdis</groupId>
      <artifactId>cgdis-portal-news-connector</artifactId>

    </dependency>
    <dependency>
      <groupId>org.mariadb.jdbc</groupId>
      <artifactId>mariadb-java-client</artifactId>
    </dependency>
    <!-- logging -->
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>jcl-over-slf4j</artifactId>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>jul-to-slf4j</artifactId>
    </dependency>



    <!--<dependency>-->
      <!--&lt;!&ndash; TODO to remove for production!!!!!!&ndash;&gt;-->
      <!--<groupId>org.bgee.log4jdbc-log4j2</groupId>-->
      <!--<artifactId>log4jdbc-log4j2-jdbc4</artifactId>-->
    <!--</dependency>-->

    <dependency>

      <groupId>io.micrometer</groupId>

      <artifactId>micrometer-core</artifactId>

    </dependency>

    <!-- Micrometer Prometheus registry  -->

    <dependency>

      <groupId>io.micrometer</groupId>

      <artifactId>micrometer-registry-prometheus</artifactId>

    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-thymeleaf</artifactId>
    </dependency>

    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>

    </dependency>
    <dependency>
      <groupId>de.codecentric</groupId>
      <artifactId>spring-boot-admin-starter-client</artifactId>
    </dependency>
    <dependency>
      <groupId>p6spy</groupId>
      <artifactId>p6spy</artifactId>
      <version>${p6spy.version}</version>
      <scope>compile</scope>
    </dependency>
      <dependency>
          <groupId>org.junit.jupiter</groupId>
          <artifactId>junit-jupiter</artifactId>
          <version>RELEASE</version>
          <scope>test</scope>
      </dependency>

      <!--    <dependency>-->
<!--      <groupId>com.itextpdf</groupId>-->
<!--      <artifactId>html2pdf</artifactId>-->
<!--      <version>2.1.4</version>-->
<!--    </dependency>-->
<!--    <dependency>-->
<!--      <groupId>io.apigee.trireme</groupId>-->
<!--      <artifactId>trireme-core</artifactId>-->
<!--      <version>0.9.4</version>-->
<!--    </dependency>-->
<!--    <dependency>-->
<!--    <groupId>io.apigee.trireme</groupId>-->
<!--    <artifactId>trireme-node12</artifactId>-->
<!--    <version>0.9.4</version>-->
<!--  </dependency>-->
<!--    <dependency>-->
<!--      <groupId>io.apigee.trireme</groupId>-->
<!--      <artifactId>trireme-node12src</artifactId>-->
<!--      <version>0.9.4</version>-->
<!--    </dependency>-->

    <dependency>
      <groupId>org.springframework.session</groupId>
      <artifactId>spring-session-data-redis</artifactId>
    </dependency>

<!--    <dependency>-->
<!--      <groupId>org.springdoc</groupId>-->
<!--      <artifactId>springdoc-openapi-ui</artifactId>-->
<!--    </dependency>-->
    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-webmvc-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-security</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springdoc</groupId>
      <artifactId>springdoc-openapi-ui</artifactId>
      <version>${springdoc.version}</version>
    </dependency>
<!--    <dependency>-->
<!--      <groupId>org.springdoc</groupId>-->
<!--      <artifactId>springdoc-openapi-javadoc</artifactId>-->
<!--      <version>${springdoc.version}</version>-->
<!--    </dependency>-->
<!--    <dependency>-->
<!--      <groupId>com.github.therapi</groupId>-->
<!--      <artifactId>therapi-runtime-javadoc</artifactId>-->
<!--      <version>0.15.0</version>-->
<!--    </dependency>-->
<!--    <dependency>-->
<!--      <groupId>com.github.therapi</groupId>-->
<!--      <artifactId>therapi-runtime-javadoc-scribe</artifactId>-->
<!--      <version>0.15.0</version>-->
<!--      <scope>provided</scope>-->
<!--    </dependency>-->
  </dependencies>

  <build>
    <defaultGoal>spring-boot:run</defaultGoal>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <executable>true</executable>
        </configuration>

      </plugin>


      <plugin>
        <groupId>nl.geodienstencentrum.maven</groupId>
        <artifactId>sass-maven-plugin</artifactId>
        <version>3.7.2</version>
        <executions>
          <execution>
            <id>compile</id>
            <phase>process-resources</phase>
            <goals>
              <goal>update-stylesheets</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <sassSourceDirectory>${pom.basedir}/src/main/webapp/sass</sassSourceDirectory>
<!--          <includes>-->
<!--            <include>**/*.scss</include>-->
<!--          </includes>-->

<!--          <destination>${css-dir}</destination>-->
<!--          <css-dir>${pom.basedir}/src/main/webapp/css/</css-dir>-->
          <destination>${build.directory}/classes/public/css/</destination>
<!--          <destination>${pom.basedir}/src/main/resources/public/css/</destination>-->
          <failOnError>false</failOnError>
          <sassOptions>
            <style>:compact</style>
            <always_update>true</always_update>

          </sassOptions>
        </configuration>

      </plugin>

      <plugin>
        <groupId>pl.project13.maven</groupId>
        <artifactId>git-commit-id-plugin</artifactId>
        <configuration>
          <generateGitPropertiesFile>true</generateGitPropertiesFile>
          <excludeProperties>
            <excludeProperty>^git.local.branch.*$</excludeProperty>
          </excludeProperties>
        </configuration>
        <executions>
          <execution>
            <id>get-the-git-infos</id>
            <goals>
              <goal>revision</goal>
            </goals>
          </execution>
          <execution>
            <id>validate-the-git-infos</id>
            <goals>
              <goal>validateRevision</goal>
            </goals>
            <!-- *NOTE*: The default phase of validateRevision is verify, but in case you want to change it, you can do so by adding the phase here -->
            <phase>package</phase>
          </execution>
        </executions>
      </plugin>

    </plugins>
  </build>

  <profiles>
    <profile>
      <id>dev</id>
      <dependencies>

        <dependency>
          <groupId>p6spy</groupId>
          <artifactId>p6spy</artifactId>
          <version>${p6spy.version}</version>
        </dependency>


      </dependencies>
    </profile>
    <profile>
      <id>redis</id>
      <dependencies>

        <dependency>
          <groupId>io.lettuce</groupId>
          <artifactId>lettuce-core</artifactId>
<!--          <version>2.6.4</version>-->
        </dependency>
      </dependencies>
    </profile>
    <profile>
      <id>prod</id>
      <dependencies>
        <dependency>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
      </dependencies>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-resources-plugin</artifactId>
            <version>${maven-resources-plugin.version}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-war-plugin</artifactId>
            <configuration>
              <warSourceDirectory>target/frontend/app</warSourceDirectory>
              <webResources>
                <resource>
                  <directory>src/main/webapp</directory>
                  <includes>
                    <include>WEB-INF/**</include>
                  </includes>
                </resource>
              </webResources>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-maven-plugin</artifactId>
            <configuration>
              <executable>true</executable>
              <!--<addResources>true</addResources>-->
            </configuration>
            <executions>
              <execution>
                <goals>
                  <goal>repackage</goal>
                </goals>
              </execution>
            </executions>
          </plugin>

          <!-- Javascript build -->
          <plugin>
            <groupId>com.github.eirslett</groupId>
            <artifactId>frontend-maven-plugin</artifactId>
            <version>${frontend-maven-plugin.version}</version>
            <executions>
              <execution>
                <id>install node and npm</id>
                <goals>
                  <goal>install-node-and-npm</goal>
                </goals>
                <configuration>
                  <nodeVersion>${node.version}</nodeVersion>
                  <npmVersion>${npm.version}</npmVersion>
                </configuration>
              </execution>
              <execution>
                <id>npm install</id>
                <goals>
                  <goal>npm</goal>
                </goals>
                <phase>generate-resources</phase>
                <configuration>
                  <!--<workingDirectory>cgdis-portal-webapp/</workingDirectory>-->
                  <arguments>install</arguments>
                  <npmInheritsProxyConfigFromMaven>false</npmInheritsProxyConfigFromMaven>
                </configuration>
              </execution>
              <execution>
                <id>npm angularcli:prod</id>
                <goals>
                  <goal>npm</goal>
                </goals>
                <phase>generate-resources</phase>
                <configuration>
                  <!--<workingDirectory>cgdis-portal-webapp/</workingDirectory>-->
                  <arguments>run angularcli:prod</arguments>
                </configuration>
              </execution>
            </executions>
          </plugin>

        </plugins>

      </build>
    </profile>

    <!-- ##### Mobile builds ##### -->
    <!-- ###    MobileDev    ### -->
    <profile>
      <id>mobiledev</id>
      <dependencies>
        <dependency>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
      </dependencies>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-resources-plugin</artifactId>
            <version>${maven-resources-plugin.version}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-war-plugin</artifactId>
            <configuration>
              <warSourceDirectory>target/frontend/app</warSourceDirectory>
              <webResources>
                <resource>
                  <directory>src/main/webapp</directory>
                  <includes>
                    <include>WEB-INF/**</include>
                  </includes>
                </resource>
              </webResources>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-maven-plugin</artifactId>
            <configuration>
              <executable>true</executable>
            </configuration>
            <executions>
              <execution>
                <goals>
                  <goal>repackage</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <!-- Javascript build -->
          <plugin>
            <groupId>com.github.eirslett</groupId>
            <artifactId>frontend-maven-plugin</artifactId>
            <version>${frontend-maven-plugin.version}</version>
            <executions>
              <execution>
                <id>install node and yarn</id>
                <goals>
                  <goal>install-node-and-yarn</goal>
                </goals>
                <configuration>
                  <nodeVersion>${node.version}</nodeVersion>
                  <yarnVersion>${yarn.version}</yarnVersion>
                </configuration>
              </execution>
              <execution>
                <id>yarn install</id>
                <goals>
                  <goal>yarn</goal>
                </goals>
                <configuration>
                  <arguments>install</arguments>
                </configuration>
              </execution>
              <execution>
                <id>yarn angularcli:mobile:dev</id>
                <goals>
                  <goal>yarn</goal>
                </goals>
                <phase>generate-resources</phase>
                <configuration>
                  <arguments>angularcli:mobile:dev</arguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <!-- ###    MobileProd    ### -->
    <profile>
      <id>mobileprod</id>
      <dependencies>
        <dependency>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
      </dependencies>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-resources-plugin</artifactId>
            <version>${maven-resources-plugin.version}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-war-plugin</artifactId>
            <configuration>
              <warSourceDirectory>target/frontend/app</warSourceDirectory>
              <webResources>
                <resource>
                  <directory>src/main/webapp</directory>
                  <includes>
                    <include>WEB-INF/**</include>
                  </includes>
                </resource>
              </webResources>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-maven-plugin</artifactId>
            <configuration>
              <executable>true</executable>
            </configuration>
            <executions>
              <execution>
                <goals>
                  <goal>repackage</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <!-- Javascript build -->
          <plugin>
            <groupId>com.github.eirslett</groupId>
            <artifactId>frontend-maven-plugin</artifactId>
            <version>${frontend-maven-plugin.version}</version>
            <executions>
              <execution>
                <id>install node and yarn</id>
                <goals>
                  <goal>install-node-and-yarn</goal>
                </goals>
                <configuration>
                  <nodeVersion>${node.version}</nodeVersion>
                  <yarnVersion>${yarn.version}</yarnVersion>
                </configuration>
              </execution>
              <execution>
                <id>yarn install</id>
                <goals>
                  <goal>yarn</goal>
                </goals>
                <configuration>
                  <arguments>install</arguments>
                </configuration>
              </execution>
              <execution>
                <id>yarn angularcli:mobile:prod</id>
                <goals>
                  <goal>yarn</goal>
                </goals>
                <phase>generate-resources</phase>
                <configuration>
                  <arguments>angularcli:mobile:prod</arguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <!-- ###    MobileTest    ### -->
    <profile>
      <id>mobiletest</id>
      <dependencies>
        <dependency>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
      </dependencies>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-resources-plugin</artifactId>
            <version>${maven-resources-plugin.version}</version>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-war-plugin</artifactId>
            <configuration>
              <warSourceDirectory>target/frontend/app</warSourceDirectory>
              <webResources>
                <resource>
                  <directory>src/main/webapp</directory>
                  <includes>
                    <include>WEB-INF/**</include>
                  </includes>
                </resource>
              </webResources>
            </configuration>
          </plugin>
          <plugin>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-maven-plugin</artifactId>
            <configuration>
              <executable>true</executable>
            </configuration>
            <executions>
              <execution>
                <goals>
                  <goal>repackage</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <!-- Javascript build -->
          <plugin>
            <groupId>com.github.eirslett</groupId>
            <artifactId>frontend-maven-plugin</artifactId>
            <version>${frontend-maven-plugin.version}</version>
            <executions>
              <execution>
                <id>install node and yarn</id>
                <goals>
                  <goal>install-node-and-yarn</goal>
                </goals>
                <configuration>
                  <nodeVersion>${node.version}</nodeVersion>
                  <yarnVersion>${yarn.version}</yarnVersion>
                </configuration>
              </execution>
              <execution>
                <id>yarn install</id>
                <goals>
                  <goal>yarn</goal>
                </goals>
                <configuration>
                  <arguments>install</arguments>
                </configuration>
              </execution>
              <execution>
                <id>yarn angularcli:mobile:test</id>
                <goals>
                  <goal>yarn</goal>
                </goals>
                <phase>generate-resources</phase>
                <configuration>
                  <arguments>angularcli:mobile:test</arguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
